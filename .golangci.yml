run:
  timeout: 3m

linters-settings:
  stylecheck:
    # Disable specific rule ST1003. We do 'Id' instead of 'ID' to have consistency with protobuf 
    checks: ["all", "-ST1003"]
    initialisms: ["ACL", "API", "ASCII", "CPU", "CSS", "DNS", "EOF", "GUID", "HTML", "HTTP", "HTTPS", "IP", "JSON", "QPS", "RAM", "RPC", "SLA", "SMTP", "SQL", "SSH", "TCP", "TLS", "TTL", "UDP", "UI", "GID", "UID", "UUID", "URI", "URL", "UTF8", "VM", "XML", "XMPP", "XSRF", "XSS", "SIP", "RTP", "AMQP", "DB", "TS"]
  goconst:
    min-occurrences: 4

  gocyclo:
    # Stricter cyclomatic complexity threshold
    min-complexity: 6

  errcheck:
    # Reduced exclusion list to ensure critical error handling
    exclude-functions:
      - io.Copy

  staticcheck:
    # Include more rules for deeper static analysis
    checks:
      - SA1000  # Invalid regular expression
      - SA4006  # Unused function result

  nolintlint:
    # Keep strict requirements for using `nolint`
    require-explanation: true
    require-specific: true

  dupl:
    # Effectively disable dupl by setting a very high threshold
    threshold: 500

  funlen:
    # Stricter limits for function length
    lines: 40
    statements: 15

  gocritic:
    # Add general improvement checks
    enabled-checks:
      - sloppyReassign  # Detect sloppy reassignment
      - rangeValCopy  # Avoid copying large structs in range loops

issues:
  exclude-rules:
    - path: vendor/.*
      text: ".*"
      linters:
        - all
    - path: _test\.go
      linters: [gomnd]
      text: "magic number"
    - path: lib/proto/hero/.*
      text: ".*"
      linters:
        - all
    - linters:
        - stylecheck
      text: "should not use underscores in Go names"

linters:
  enable:
    - govet
    - errcheck
    - staticcheck
    - gofmt
    - ineffassign
    - goconst
    # - gocyclo turn off temporarily
    - dupl
    - unparam
    - unused
    - gosec
    - stylecheck
    - misspell  # Check for spelling errors
    - gocritic

  disable: []  # Empty array instead of 'none'

output:
  formats:
    - format: colored-line-number  # Updated to use map format
  print-issued-lines: true
  sort-results: true
