{"permissions": {"allow": ["Bash(grep:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(true)", "Bash(find:*)", "Bash(aws cloudformation describe-stacks:*)", "Bash(aws cloudformation:*)", "Bash(aws ecs list-services:*)", "Bash(aws ecr describe-repositories:*)", "Bash(aws cloudfront:*)", "Bash(aws s3 ls:*)", "Bash(aws s3 cp:*)", "Bash(npx next lint:*)", "WebFetch(domain:docs.aws.amazon.com)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(make:*)", "<PERSON><PERSON>(docker-compose exec:*)", "Bash(node:*)", "Bash(git fetch:*)", "Bash(git checkout:*)", "Bash(git commit:*)", "Bash(go build:*)", "Bash(npm install:*)", "Bash(rm:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(claude mcp:*)", "mcp__sentry__find_organizations", "mcp__sentry__find_projects", "mcp__sentry__find_errors", "mcp__sentry__find_transactions", "mcp__sentry__find_issues", "mcp__sentry__find_tags", "mcp__sentry__find_releases", "Bash(npm run build:*)", "mcp__postgres__list_schemas", "mcp__postgres__list_objects", "mcp__postgres__get_object_details", "mcp__postgres__execute_sql", "Bash(npm run lint)", "mcp__sentry__get_issue_details", "Bash(go mod:*)", "Bash(rg:*)", "<PERSON><PERSON>(aws secretsmanager describe-secret:*)", "<PERSON><PERSON>(aws secretsmanager:*)", "Bash(aws:*)", "Bash(ls:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(docker run:*)", "mcp__sentry__search_docs", "mcp__sentry__get_doc", "mcp__sentry__whoami", "WebFetch(domain:www.twilio.com)", "<PERSON><PERSON>(go test:*)", "<PERSON><PERSON>(go run:*)", "<PERSON><PERSON>(curl:*)", "Bash(export AWS_PROFILE=andreithegoat)", "WebFetch(domain:aws.amazon.com)", "Bash(cdk diff:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(GOOS=linux GOARCH=amd64 go build -tags lambda.norpc -o bootstrap main.go)", "Bash(zip:*)", "Bash(git pull:*)", "Bash(git cherry-pick:*)", "Bash(git add:*)", "Ba<PERSON>(go vet:*)", "Bash(gofmt:*)"], "deny": []}}