{"name": "hero-command-and-control-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "NODE_OPTIONS=--max-old-space-size=4096 next build", "start": "next start", "lint": "next lint", "test": "jest", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@aws-amplify/auth": "^6.12.1", "@aws-sdk/client-kinesis-video": "^3.816.0", "@dnd-kit/core": "^6.3.1", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^6.4.7", "@mui/material": "^6.4.6", "@react-pdf/renderer": "^4.3.0", "@sentry/browser": "^9.10.1", "@sentry/nextjs": "^9.10.1", "@tanstack/react-query": "^5.64.1", "@tanstack/react-query-devtools": "^5.64.1", "@tiptap/extension-blockquote": "^2.11.7", "@tiptap/extension-color": "^2.11.7", "@tiptap/extension-heading": "^2.11.7", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/extension-text-style": "^2.11.7", "@tiptap/extension-underline": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@twilio/voice-sdk": "^2.12.3", "@types/google-protobuf": "^3.15.12", "@types/supercluster": "^7.1.3", "@types/uuid": "^10.0.0", "amazon-ivs-player": "^1.39.0", "amazon-kinesis-video-streams-webrtc": "^2.4.0", "aws-amplify": "^6.14.1", "axios": "^1.7.9", "date-fns": "^3.5.0", "google-protobuf": "^3.21.4", "hls.js": "^1.5.20", "libphonenumber-js": "^1.12.9", "lodash.debounce": "^4.0.8", "lucide-react": "^0.474.0", "mapbox-gl": "^3.10.0", "match-sorter": "^8.0.1", "mui-nested-menu": "^4.0.1", "next": "^15.3.0-canary.28", "proto": "*", "react": "^19.0.0", "react-csv": "^2.2.2", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-icons": "^5.4.0", "react-map-gl": "^7.1.8", "react-pro-sidebar": "^1.1.0", "react-resizable-panels": "^2.1.7", "react-split": "^2.0.14", "react-transition-group": "^4.4.5", "react-virtualized-auto-sizer": "^1.0.25", "react-virtuoso": "^4.12.3", "react-window": "^1.8.11", "sharp": "^0.33.5", "supercluster": "^8.0.1", "swiper": "^11.2.5", "twilio-client": "^1.15.1", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.6", "@eslint/eslintrc": "^3", "@rushstack/eslint-patch": "^1.10.5", "@storybook/addon-essentials": "^8.6.7", "@storybook/addon-onboarding": "^8.6.7", "@storybook/blocks": "^8.6.7", "@storybook/experimental-addon-test": "^8.6.7", "@storybook/nextjs": "^8.6.7", "@storybook/react": "^8.6.7", "@storybook/test": "^8.6.7", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19", "@types/react-csv": "^1.1.10", "@types/react-dom": "^19", "@types/react-dropzone": "^4.2.2", "@types/react-transition-group": "^4.4.12", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint": "^8.49.0", "eslint-config-next": "^15.1.6", "eslint-plugin-storybook": "^0.11.6", "eslint-plugin-unused-imports": "^4.1.4", "jest": "^29.7.0", "postcss": "^8", "storybook": "^8.6.7", "tailwindcss": "^3.4.1", "ts-jest": "^29.2.6", "typescript": "^5"}}