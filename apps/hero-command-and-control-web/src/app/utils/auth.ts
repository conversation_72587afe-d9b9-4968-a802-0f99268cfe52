'use client';
const CLIENT_ID = process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || '';
const _cookieBase = `CognitoIdentityServiceProvider.${CLIENT_ID}`;

const _buildCookieAccessTokenKey = (username: string) => `${_cookieBase}.${username}.accessToken`;
const _buildCookieIDTokenKey = (username: string) => `${_cookieBase}.${username}.idToken`;
const _buildCookieRefreshTokenKey = (username: string) => `${_cookieBase}.${username}.refreshToken`;
const _buildCookieScopeKey = (username: string) => `${_cookieBase}.${username}.tokenScopesString`;
const _buildCookieUsernameKey = () => `${_cookieBase}.LastAuthUser`;

export interface Tokens {
    accessToken?: string;
    idToken?: string;
    refreshToken?: string;
    username?: string;
    /**
     * Cognito user unique identifier (JWT `sub` claim). This is decoded from the `idToken` whenever it is available
     * and provided for convenience so callers don’t need to manually parse the JWT.
     */
    sub?: string;
}

// Decode a JWT and return its payload or undefined on failure
const _decodeJWTPayload = (token?: string): Record<string, unknown> | undefined => {
    if (!token) return undefined;
    try {
        const base64Url = token.split('.')[1];
        if (!base64Url) return undefined;
        // JWTs use URL-safe base64 so convert to regular and pad if necessary
        let base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const padding = 4 - (base64.length % 4);
        if (padding !== 4) {
            base64 += '='.repeat(padding);
        }
        const jsonPayload = atob(base64);
        return JSON.parse(jsonPayload);
    } catch (_) {
        return undefined;
    }
};

// Utility function to get cookie value by name
const getCookieValue = (name: string): string | undefined => {
    if (typeof window === 'undefined') {
        return undefined;
    }
    const cookies = document.cookie.split('; ').reduce((acc: Record<string, string>, cookie) => {
        const [key, value] = cookie.split('=');
        acc[key] = decodeURIComponent(value);
        return acc;
    }, {});
    return cookies[name];
};

// Fetch tokens from cookies
export const getTokensFromCookies = (): Tokens => {
    const username = getCookieValue(_buildCookieUsernameKey());

    if (!username) {
        return {};
    }

    const usernameForCookieKey = encodeURIComponent(username);
    const accessToken = getCookieValue(_buildCookieAccessTokenKey(usernameForCookieKey));
    const idToken = getCookieValue(_buildCookieIDTokenKey(usernameForCookieKey));
    const refreshToken = getCookieValue(_buildCookieRefreshTokenKey(usernameForCookieKey));

    // Attempt to decode the JWT `idToken` to obtain the `sub` claim
    const payload = _decodeJWTPayload(idToken);
    const sub = payload && typeof payload['sub'] === 'string' ? payload['sub'] : undefined;

    return {
        username,
        accessToken,
        idToken,
        refreshToken,
        sub,
    };
};

// Remove tokens from cookies
export const removeTokensFromCookies = (): void => {
    console.log('removeTokensFromCookies: Starting cookie removal...');
    const username = getCookieValue(_buildCookieUsernameKey());
    console.log('removeTokensFromCookies: Found username:', username);

    if (!username) {
        console.log('removeTokensFromCookies: No username found, skipping cookie removal');
        return;
    }

    const usernameForCookieKey = encodeURIComponent(username);

    // Set expiration date to the past to effectively delete the cookies
    const expirationDate = new Date(0).toUTCString();

    // Remove each cookie by setting its expiration to the past
    document.cookie = `${_buildCookieAccessTokenKey(usernameForCookieKey)}=; expires=${expirationDate}; path=/`;
    document.cookie = `${_buildCookieIDTokenKey(usernameForCookieKey)}=; expires=${expirationDate}; path=/`;
    document.cookie = `${_buildCookieRefreshTokenKey(usernameForCookieKey)}=; expires=${expirationDate}; path=/`;
    document.cookie = `${_buildCookieScopeKey(usernameForCookieKey)}=; expires=${expirationDate}; path=/`;
    document.cookie = `${_buildCookieUsernameKey()}=; expires=${expirationDate}; path=/`;

    if (typeof window !== 'undefined') {
        // Try to clear using the full host (e.g. command.gethero.com) *and* the apex domain (e.g. gethero.com)
        const hostParts = window.location.hostname.split('.');
        const variants: string[] = [];
        if (hostParts.length >= 2) {
            // apex like gethero.com
            variants.push(`.${hostParts.slice(-2).join('.')}`);
        }
        // full host like .command.gethero.com (only add if different from apex)
        const fullHostDomain = `.${window.location.hostname}`;
        if (!variants.includes(fullHostDomain)) {
            variants.push(fullHostDomain);
        }

        console.log('removeTokensFromCookies: Clearing cookies for domains:', variants);
        variants.forEach((d) => {
            const domainAttr = `domain=${d}`;
            console.log(`removeTokensFromCookies: Clearing domain ${d}`);
            document.cookie = `${_buildCookieAccessTokenKey(usernameForCookieKey)}=; expires=${expirationDate}; path=/; ${domainAttr}`;
            document.cookie = `${_buildCookieIDTokenKey(usernameForCookieKey)}=; expires=${expirationDate}; path=/; ${domainAttr}`;
            document.cookie = `${_buildCookieRefreshTokenKey(usernameForCookieKey)}=; expires=${expirationDate}; path=/; ${domainAttr}`;
            document.cookie = `${_buildCookieScopeKey(usernameForCookieKey)}=; expires=${expirationDate}; path=/; ${domainAttr}`;
            document.cookie = `${_buildCookieUsernameKey()}=; expires=${expirationDate}; path=/; ${domainAttr}`;
        });
    }
    console.log('removeTokensFromCookies: Cookie removal complete');
};

const _storageBase = `CognitoIdentityServiceProvider.${CLIENT_ID}`;

const _buildStorageAccessTokenKey = (username: string) => `${_storageBase}.${username}.accessToken`;
const _buildStorageIDTokenKey = (username: string) => `${_storageBase}.${username}.idToken`;
const _buildStorageRefreshTokenKey = (username: string) => `${_storageBase}.${username}.refreshToken`;
// const _buildCookieScopeKey = (username: string) => `${_cookieBase}.${username}.tokenScopesString`;
const _buildStorageUsernameKey = () => `${_storageBase}.LastAuthUser`;


// Utility function to get value from localStorage
const getLocalStorageValue = (name: string): string | undefined => {
    return localStorage.getItem(name) || undefined;
};

// Fetch tokens from localStorage
export const getTokensFromLocalStorage = (): Tokens => {
    const username = getLocalStorageValue(_buildStorageUsernameKey());

    if (!username) {
        return {};
    }

    const accessToken = getLocalStorageValue(_buildStorageAccessTokenKey(username));
    const idToken = getLocalStorageValue(_buildStorageIDTokenKey(username));
    const refreshToken = getLocalStorageValue(_buildStorageRefreshTokenKey(username));

    return {
        username,
        accessToken,
        idToken,
        refreshToken,
    };
};

// Remove tokens from localStorage
export const removeTokensFromLocalStorage = (): void => {
    const username = getLocalStorageValue(_buildStorageUsernameKey());

    if (!username) {
        return;
    }

    localStorage.removeItem(_buildStorageAccessTokenKey(username));
    localStorage.removeItem(_buildStorageIDTokenKey(username));
    localStorage.removeItem(_buildStorageRefreshTokenKey(username));
    localStorage.removeItem(_buildStorageUsernameKey());
};

// Save tokens to localStorage
export const saveTokensToLocalStorage = (tokens: Tokens): void => {
    if (!tokens.username) {
        return;
    }

    localStorage.setItem(_buildStorageUsernameKey(), tokens.username);

    if (tokens.accessToken) {
        localStorage.setItem(_buildStorageAccessTokenKey(tokens.username), tokens.accessToken);
    }

    if (tokens.idToken) {
        localStorage.setItem(_buildStorageIDTokenKey(tokens.username), tokens.idToken);
    }

    if (tokens.refreshToken) {
        localStorage.setItem(_buildStorageRefreshTokenKey(tokens.username), tokens.refreshToken);
    }
};
