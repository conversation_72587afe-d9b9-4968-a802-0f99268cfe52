"use client";
import { Theme<PERSON>rovider, createTheme } from "@mui/material/styles";
import { useSearchParams } from "next/navigation";
import { Asset, AssetType, ListAssetsRequest } from "proto/hero/assets/v2/assets_pb";
import { CancelOrderRequest } from "proto/hero/orders/v2/orders_pb";
import {
  ListSituationsRequest,
  Situation,
} from "proto/hero/situations/v2/situations_pb";
import { useEffect, useRef, useState } from "react";
import { Panel, PanelGroup } from "react-resizable-panels";
import { stringToAssetType } from "../apis/services/workflow/assets/enumConverters";
import { useListAssets } from "../apis/services/workflow/assets/hooks";
import { useCancelOrder } from "../apis/services/workflow/orders/hooks";
import { hookSituationStatusToString } from "../apis/services/workflow/situations/enumConverters";
import { useListSituations } from "../apis/services/workflow/situations/hooks";
import MapComponent, { MapComponentRef } from "../components/MapComponent/MapComponent";
import Sidebar from "../components/Sidebar";
import { useCallContext } from "../contexts/Call/CallContext";
import { useDispatcher } from "../contexts/User/DispatcherContext";
import ActionPane from "./components/ActionPane";
import CameraInterruptionModal from "./components/CameraInterruptionModal";
import { MicrophoneErrorAlert } from "./components/CommsComponents/MicrophoneErrorAlert";
import TaskBar from "./components/TaskBar";
import UnitsSection from "./components/UnitsSection";
import styles from "./styles.module.css";

const THEME = createTheme({
  typography: {
    fontFamily: `Roboto`,
  },
});

export default function CadPage() {
  // ----- FETCH OR CREATE A NEW DISPATCHER -----
  const {
    asset: dispatcherAsset,
    isLoading: isDispatcherLoading,
    error: fetchDispatcherError,
  } = useDispatcher();

  const searchParams = useSearchParams();
  const incidentIdFromUrl = searchParams?.get("incidentId");

  const [selectedSituation, setSelectedSituation] = useState<Situation | null>(
    null
  );
  const [selectedLocation, setSelectedLocation] = useState<
    | {
      placeName: string;
      address: string;
      coordinates: [number, number];
    }
    | undefined
  >(undefined);
  const [isActionPaneExpanded, setIsActionPaneExpanded] = useState(false);
  const [isUnitsSectionCollapsed, setIsUnitsSectionCollapsed] = useState(false);

  // Add ref for MapComponent to access centering functionality
  const mapComponentRef = useRef<MapComponentRef>(null);

  // Function to center map on a specific asset
  const handleCenterOnAsset = (asset: Asset) => {
    mapComponentRef.current?.centerOnAsset(asset);
  };

  // Function to open asset info modal (same as clicking on asset marker)
  const handleOpenAssetInfo = (asset: Asset) => {
    // Set the selected responder and open the popup
    mapComponentRef.current?.setSelectedResponder(asset);
    mapComponentRef.current?.openAssetPopup(asset);
  };

  // Function to open assigned incident in action pane
  const handleOpenAssignedIncident = (situationId: string) => {
    const situation = situations?.situations?.find(s => s.id === situationId);
    if (situation) {
      setSelectedSituation(situation);
      setSelectedLocation(undefined);
      if (actionPanePanelRef.current) {
        actionPanePanelRef.current.resize(32);
        setIsActionPaneExpanded(true);
      }
    }
  };

  // Cancel order mutation
  const cancelOrderMutation = useCancelOrder();

  // Function to remove asset from incident (cancel order)
  const handleRemoveFromIncident = async (assetId: string, orderId: string) => {
    try {
      await cancelOrderMutation.mutateAsync({
        id: orderId,
        reason: "Removed by dispatcher",
      } as CancelOrderRequest);
      console.log(`Successfully removed asset ${assetId} from incident via order ${orderId}`);
    } catch (error) {
      console.error(`Failed to remove asset ${assetId} from incident:`, error);
      // You could add a toast notification here to show the error to the user
    }
  };

  // TODO: Handle paging properly
  const pageSize: number = 5000;
  const pageToken: string = "";
  const orderBy: string = "create_time desc";

  const incidentRequestParams: ListSituationsRequest = {
    pageSize,
    pageToken,
    orderBy,
  } as ListSituationsRequest;

  const {
    data: situations,
    isLoading,
    isError,
    error,
    refetch: refetchSituations,
  } = useListSituations(incidentRequestParams, {
    refetchInterval: 3000,
  });

  const assetRequestParams: ListAssetsRequest = {
    pageSize,
    pageToken,
  } as ListAssetsRequest;

  const {
    data: assets,
    isLoading: isAssetsLoading,
    isError: isAssetsError,
    error: assetsError,
    refetch: refetchAssets,
  } = useListAssets(assetRequestParams, {
    refetchInterval: 3000,
  });

  const assetList = (assets && assets.assets) || [];

  const responders = assetList?.filter(
    (asset) => {
      const assetType = typeof asset.type === 'string'
        ? stringToAssetType(asset.type)
        : asset.type;
      return assetType === AssetType.RESPONDER ||
        assetType === AssetType.DISPATCHER ||
        assetType === AssetType.SUPERVISOR;
    }
  );

  const cameras = assetList?.filter(
    // @ts-expect-error TODO: Fix type issue
    (asset) => asset.type === "ASSET_TYPE_CAMERA"
  );

  // Callback using Situation instead of Report
  const handleSituationSelect = (situation: Situation | null) => {
    setSelectedSituation(situation);
    setSelectedLocation(undefined);
    if (situation && actionPanePanelRef.current) {
      actionPanePanelRef.current.resize(32);
      setIsActionPaneExpanded(true);
    }
  };

  const handleCollapseActionPane = () => {
    if (actionPanePanelRef.current) {
      actionPanePanelRef.current.collapse();
      setIsActionPaneExpanded(false);
    }
  };

  const handleExpandActionPane = () => {
    if (actionPanePanelRef.current) {
      actionPanePanelRef.current.resize(32);
      setIsActionPaneExpanded(true);
    }
  }

  // Callback to handle
  const onCallInterruptionAccept = async (situationId: string) => {
    const refreshedResponse = await refetchSituations();
    const refreshedSituations = refreshedResponse.data;
    const upcomingSituation = refreshedSituations?.situations?.find(
      (situation: Situation) => situation.id === situationId
    );

    if (upcomingSituation) {
      setSelectedSituation(upcomingSituation);
      setSelectedLocation(undefined);
    }
  };

  const actionPanePanelRef = useRef<any>(null);

  useEffect(() => {
    localStorage.removeItem("react-resizable-panels:example");
    if (actionPanePanelRef.current) {
      actionPanePanelRef.current.collapse();
    }
  }, []);

  useEffect(() => {
    if (actionPanePanelRef.current) {
      if (selectedSituation) {
        actionPanePanelRef.current.resize(32);
        setIsActionPaneExpanded(true);
      } else {
        actionPanePanelRef.current.collapse();
        setIsActionPaneExpanded(false);
      }
    }
  }, [selectedSituation]);

  // ----- AUTO-SELECT SITUATION BASED ON ACTIVE CALL -----
  // Syncs 'selectedSituation' with the 'currentActiveCall.situationId' from CallContext
  // to ensure the ActionPane shows the correct details.
  const { currentActiveCall } = useCallContext();
  const processedCallSituationIdRef = useRef<string | null | undefined>(null);

  // ----- AUTO-SELECT SITUATION BASED ON URL PARAMETER -----
  // Handle incidentId from URL query parameter
  useEffect(() => {
    if (incidentIdFromUrl && situations?.situations && !selectedSituation) {
      const situationFromUrl = situations.situations.find(
        (situation: Situation) => situation.id === incidentIdFromUrl
      );

      if (situationFromUrl) {
        console.log(`[CAD] Auto-selecting incident from URL: ${incidentIdFromUrl}`);
        setSelectedSituation(situationFromUrl);
        setSelectedLocation(undefined);
        if (actionPanePanelRef.current) {
          actionPanePanelRef.current.resize(32);
          setIsActionPaneExpanded(true);
        }
      }
    }
  }, [incidentIdFromUrl, situations, selectedSituation]);

  useEffect(() => {
    const activeCallSituationId = currentActiveCall?.situationId;
    const allSituations = situations?.situations || [];

    // Only run the sync logic if the active call situation ID has actually changed
    // since the last time we processed it, and if we have situations loaded.
    if (
      activeCallSituationId &&
      allSituations.length > 0 &&
      activeCallSituationId !== processedCallSituationIdRef.current
    ) {
      // Find the situation matching the active call's situation ID
      const situationForActiveCall = allSituations.find(
        (s: Situation) => s.id === activeCallSituationId
      );

      // If found, update the state
      if (situationForActiveCall) {
        setSelectedSituation(situationForActiveCall);
        setSelectedLocation(undefined); // Reset selected location
      }
      // Update the ref to mark this situation ID as processed
      processedCallSituationIdRef.current = activeCallSituationId;
    } else if (!activeCallSituationId && processedCallSituationIdRef.current) {
      // Optional: Handle the case when the active call ends? 
      processedCallSituationIdRef.current = undefined;
    }

    // Depend primarily on the active call's situation ID and the situations list
  }, [currentActiveCall?.situationId, situations]);

  // TODO: Remove it after Login is properly implemented
  if (isDispatcherLoading) {
    return <div>Loading dispatcher info...</div>;
  }

  if (fetchDispatcherError) {
    return <div>Error: {fetchDispatcherError.message}</div>;
  }

  if (!dispatcherAsset) {
    return <div>No dispatcher asset available.</div>;
  }

  const activeSituations = situations?.situations?.filter(
    (situation) => {
      const statusString = hookSituationStatusToString(situation.status);
      return statusString !== "SITUATION_STATUS_RESOLVED" && statusString !== "SITUATION_STATUS_COMPLETED";
    }
  ) || [];

  return (
    <ThemeProvider theme={THEME}>
      <main className={styles.mainContainer}>
        <CameraInterruptionModal
          dispatcherAsset={dispatcherAsset}
          onAcceptSituation={(situationId) => setSelectedSituation(situationId)}
        />
        <MicrophoneErrorAlert />
        <Sidebar />
        <div className={styles.contentContainer}>
          <div className={styles.taskBarContainer}>
            {/* Pass the situations directly and the updated callback */}
            <TaskBar
              onSituationSelect={handleSituationSelect}
              situations={activeSituations || []}
              selectedSituation={selectedSituation}
              assets={assetList}
            />
          </div>
          <div className={styles.mapAndActionContainer}>
            <PanelGroup autoSaveId="example" direction="horizontal">
              <Panel
                ref={actionPanePanelRef}
                id="action-pane"
                order={1}
                defaultSize={0}
                collapsible
                collapsedSize={0}
                minSize={32}
              >
                <ActionPane
                  key={selectedSituation?.id}
                  selectedSituationId={selectedSituation?.id}
                  onLocationSelect={(location) => setSelectedLocation(location)}
                  onResolveSituation={() => setSelectedSituation(null)}
                  onCollapseActionPane={handleCollapseActionPane}
                  responders={responders || []}
                />
              </Panel>
              {/* <PanelResizeHandle /> */}
              <Panel
                id="map-pane"
                order={2}
                defaultSize={100}
                minSize={68}
                style={{ height: "100%", alignSelf: "flex-end" }}
              >
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    height: "100%",
                  }}
                >
                  <div style={{ flex: isUnitsSectionCollapsed ? 1 : "0 0 70%" }}>
                    <MapComponent
                      selectedSituation={selectedSituation}
                      agents={responders || []}
                      cameras={cameras || []}
                      selectedLocation={selectedLocation}
                      activeSituations={activeSituations || []}
                      isActionPaneExpanded={isActionPaneExpanded}
                      onSelectSituation={handleSituationSelect}
                      onExpandActionPane={handleExpandActionPane}
                      ref={mapComponentRef}
                    />
                  </div>
                  <div style={{
                    flex: isUnitsSectionCollapsed ? "0 0 auto" : "0 0 30%",
                    height: isUnitsSectionCollapsed ? "auto" : "30vh", // Force concrete height
                    minHeight: isUnitsSectionCollapsed ? "auto" : "200px",
                    maxHeight: isUnitsSectionCollapsed ? "auto" : "30vh",
                  }}>
                    <UnitsSection
                      selectedSituation={selectedSituation}
                      selectedLocation={selectedLocation}
                      assets={responders}
                      onCollapsedChange={setIsUnitsSectionCollapsed}
                      onCenterOnAsset={handleCenterOnAsset}
                      onOpenAssetInfo={handleOpenAssetInfo}
                      onOpenAssignedIncident={handleOpenAssignedIncident}
                      onRemoveFromIncident={handleRemoveFromIncident}
                    />
                  </div>
                </div>
              </Panel>
            </PanelGroup>
          </div>
        </div>
      </main>
    </ThemeProvider>
  );
}
