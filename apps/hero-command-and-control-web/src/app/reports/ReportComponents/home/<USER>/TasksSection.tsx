import { useOrder } from "@/app/apis/services/workflow/orders/hooks";
import { toTitleCase } from "@/app/utils/utils";
import { Label } from "@/design-system/components/Label";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import {
  Box,
  CircularProgress,
  IconButton,
  Menu,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import React, { useState } from "react";
import { ReassignReportPopup } from "../../common/ReassignReportPopup";

export interface TaskItem {
  id: string;
  name: string;
  status: string;
  caseId: string;
  caseType: string;
  reportDate: string;
  reportingOfficer: string;
  reportId: string;
  originalStatus: string;
}

interface TasksSectionProps {
  tasks: TaskItem[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  onTaskClick: (orderId: string, reportId: string, status: string) => void;
}

export const TasksSection: React.FC<TasksSectionProps> = ({
  tasks,
  isLoading,
  isError,
  error,
  onTaskClick,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [selectedReportId, setSelectedReportId] = useState<string | null>(null);
  const open = Boolean(anchorEl);

  // Reassign popup state
  const [reassignPopupOpen, setReassignPopupOpen] = useState(false);

  // Get the selected order data
  const { data: selectedOrderData } = useOrder(selectedOrderId || "", {
    enabled: !!selectedOrderId,
    queryKey: ["order", selectedOrderId],
  });

  const handleClick = (event: React.MouseEvent<HTMLElement>, taskId: string) => {
    event.stopPropagation(); // Prevent row click
    setAnchorEl(event.currentTarget);
    setSelectedTaskId(taskId);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setSelectedTaskId(null);
  };

  const handleReassignReport = () => {
    // Find the selected task to get order and report info
    const selectedTask = tasks.find(task => task.id === selectedTaskId);
    if (selectedTask) {
      setSelectedOrderId(selectedTask.id); // task.id is actually the order ID
      setSelectedReportId(selectedTask.reportId); // Set the report ID
      setReassignPopupOpen(true);
    }
    handleClose();
  };

  const handleReassignPopupClose = () => {
    setReassignPopupOpen(false);
    setSelectedOrderId(null);
    setSelectedReportId(null);
  };

  const handleReassignSuccess = () => {
    // Optionally refresh data or show success message
    console.log("Report reassigned successfully");
  };

  return (
    <Box sx={{ mb: 3 }}>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          mb: 2,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Typography style="body1" color={colors.grey[900]}>
            MY TASKS
          </Typography>
          <Box
            sx={{
              backgroundColor: colors.rose[600],
              color: "white",
              borderRadius: "50%",
              width: "20px",
              height: "20px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              fontSize: "12px",
              fontWeight: "bold",
            }}
          >
            {tasks.length}
          </Box>
        </Box>
        <Box>{/* Filter icon could go here */}</Box>
      </Box>

      <Paper
        elevation={0}
        sx={{
          borderRadius: "12px",
          border: `1px solid ${colors.grey[200]}`,
          overflow: "hidden",
          boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
          p: !isLoading && !isError && tasks.length === 0 ? 0 : 2,
          pb: 0,
        }}
      >
        {!isLoading && !isError && tasks.length === 0 ? (
          <Box
            sx={{
              height: "78px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Typography style="caps1" color={colors.grey[500]}>
              NO TASKS
            </Typography>
          </Box>
        ) : (
          <TableContainer
            sx={{
              maxHeight: "314px",
              "&::-webkit-scrollbar-track": {
                backgroundColor: colors.grey[100],
              },
              "&::-webkit-scrollbar-thumb": {
                backgroundColor: colors.grey[300],
                borderRadius: "4px",
              },
              "&::-webkit-scrollbar-thumb:hover": {
                backgroundColor: colors.grey[400],
              },
              scrollbarWidth: "thin",
              scrollbarColor: `${colors.grey[300]} ${colors.grey[100]}`,
            }}
          >
            <Table stickyHeader>
              <TableHead sx={{ "& th": { py: 0 } }}>
                <TableRow sx={{ backgroundColor: colors.grey[50] }}>
                  <TableCell>
                    <Typography style="tag2" color={colors.grey[700]}>
                      Task
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography style="tag2" color={colors.grey[700]}>
                      Status
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography style="tag2" color={colors.grey[700]}>
                      Report ID
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography style="tag2" color={colors.grey[700]}>
                      Case Type
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography style="tag2" color={colors.grey[700]}>
                      Report Date
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography style="tag2" color={colors.grey[700]}>
                      Reporting Officer
                    </Typography>
                  </TableCell>
                  <TableCell width="50px">
                    {/* Actions column header - empty for now */}
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center" sx={{ py: 3 }}>
                      <CircularProgress size={24} />
                    </TableCell>
                  </TableRow>
                ) : isError ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center" sx={{ py: 3 }}>
                      <Typography style="body3" color={colors.grey[900]}>
                        Error loading tasks: {error?.message}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  tasks.map((task) => (
                    <TableRow
                      key={task.id}
                      sx={{
                        cursor: "pointer",
                        "&:hover": { backgroundColor: colors.grey[50] },
                      }}
                      onClick={() =>
                        onTaskClick(task.id, task.reportId, task.originalStatus)
                      }
                    >
                      <TableCell>
                        <Typography style="body3" color={colors.grey[900]}>
                          {task.name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Label
                          color={task.status === "In Progress" ? "amber" : "grey"}
                          label={task.status}
                          size={"small"}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography style="body4" color={colors.grey[900]}>
                          {task.caseId}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography style="body4" color={colors.grey[900]}>
                          {task.caseType}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography style="body4" color={colors.grey[900]}>
                          {task.reportDate}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box
                          sx={{ display: "flex", alignItems: "center", gap: 1 }}
                        >
                          <Box
                            sx={{
                              width: 20,
                              height: 20,
                              borderRadius: "50%",
                              bgcolor: colors.grey[100],
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                          >
                            <Typography style="tag2" color={colors.grey[600]}>
                              {task.reportingOfficer.charAt(0).toUpperCase()}
                            </Typography>
                          </Box>
                          <Typography style="body4" color={colors.grey[900]}>
                            {toTitleCase(task.reportingOfficer)}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <IconButton
                          aria-label={`menu for task ${task.id}`}
                          aria-controls={open && selectedTaskId === task.id ? "task-menu" : undefined}
                          aria-haspopup="true"
                          aria-expanded={open && selectedTaskId === task.id ? "true" : undefined}
                          onClick={(event) => handleClick(event, task.id)}
                          size="small"
                          sx={{
                            color: colors.grey[600],
                            padding: "4px",
                            "&:hover": { backgroundColor: colors.grey[100] }
                          }}
                        >
                          <MoreVertIcon fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      <Menu
        id="task-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "task-menu-button",
        }}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "200px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
          },
        }}
      >
        <MenuItem onClick={handleReassignReport}>
          Reassign report
        </MenuItem>
      </Menu>

      {/* Reassign Report Popup */}
      <ReassignReportPopup
        open={reassignPopupOpen}
        onClose={handleReassignPopupClose}
        order={selectedOrderData}
        reportId={selectedReportId || undefined}
        onSuccess={handleReassignSuccess}
      />
    </Box>
  );
};
