import React from "react";
import { Box } from "@mui/material";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { MdOutlineFeedback } from "react-icons/md";

interface ReportRejectionCardProps {
  rejectionNote?: string;
  reviewer?: string;
  rejectionDate?: string;
}

const ReportRejectionCard: React.FC<ReportRejectionCardProps> = ({
  rejectionNote,
  rejectionDate = "Mar 12, 2024 at 14:02",
}) => {

  return (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        flexDirection: "column",
        alignItems: "flex-start",
        borderRadius: "12px",
        border: `1px solid ${colors.grey[200]}`,
        background: "#FFF",
        overflow: "hidden",
        position: "relative",
        boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
        mb: 3,
        p: 3,
      }}
    >
      <Box
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {/* Message with icon */}
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Box sx={{ mr: 2 }}>
            <MdOutlineFeedback size={30} color={colors.rose[600]} />
          </Box>
          <Box sx={{ flex: 1 }}>
            {/* Title with date */}
            <Box sx={{ display: "flex", gap: 2, alignItems: "center", mb: "12px" }}>
              <Typography style="h2" color={colors.grey[900]}>
                Report Rejected
              </Typography>
              <Typography style="tag2" color={colors.grey[500]}>
                {rejectionDate}
              </Typography>
            </Box>
            <Box sx={{ display: "flex", mb: "12px" }}>
              <Typography style="body4" color={colors.grey[700]}>
                {rejectionNote && rejectionNote.trim() 
                  ? `The supervisor has rejected this report with the following reason.`
                  : `The supervisor has rejected this report.`
                }
              </Typography>
            </Box>
            {rejectionNote && rejectionNote.trim() && (
              <Box sx={{ display: "flex"}}>
              <Typography
                style="body4"
                color={colors.grey[500]}
                className="italic"
              >
                “{rejectionNote}”
              </Typography>
              </Box>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ReportRejectionCard;
