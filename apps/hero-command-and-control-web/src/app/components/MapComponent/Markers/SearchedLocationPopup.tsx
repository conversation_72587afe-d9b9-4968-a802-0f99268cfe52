import React from "react";
import { Box, Typography, Button } from "@mui/material";
import AddLocationIcon from "@mui/icons-material/AddLocation";
import { Situation } from "proto/hero/situations/v2/situations_pb";

interface SearchedLocationPopupProps {
  address: string;
  locationName?: string;
  coordinates: { lat: number; lng: number };
  selectedSituation: Situation | null;
  onImageClick: () => void;
  onAddToIncident: () => void;
}

const SearchedLocationPopup: React.FC<SearchedLocationPopupProps> = ({
  address,
  locationName,
  coordinates,
  selectedSituation,
  onImageClick,
  onAddToIncident,
}) => {
  const title = locationName || address;
const subtitle = locationName
    ? address
    : `${coordinates.lat.toFixed(5)}, ${coordinates.lng.toFixed(5)}`;

  return (
    <Box
      sx={{
        display: "flex",
        p: 0.5,
        alignItems: "center",
        overflow: "hidden",
      }}
    >
      {/* <ButtonBase onClick={onImageClick}>
        <Box
          sx={{
            position: "relative",
            width: 90,
            height: 90,
            mr: 2,
            borderRadius: 1,
            overflow: "hidden",
          }}
        >
          <Box
            component="img"
            src={StreetView.src}
            alt="streetview"
            sx={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
            }}
          />
          <Box
            sx={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              bgcolor: "rgba(0, 0, 0, 0.4)",
            }}
          />
          <Box
            sx={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          >
            <Md360 size={24} color="white" />
          </Box>
        </Box>
      </ButtonBase> */}

      <Box sx={{ display: "flex", flexDirection: "column", flex: 1 }}>
        <Typography
          noWrap
          sx={{ fontWeight: 600, fontSize: 16, color: "#07090A" }}
        >
          {title}
        </Typography>
        {subtitle && (
          <Typography
            sx={{
              fontWeight: 400,
              fontSize: 12,
              color: "#374151",
              display: "-webkit-box",
              WebkitBoxOrient: "vertical",
              WebkitLineClamp: 2,
              overflow: "hidden",
            }}
          >
            {subtitle}
          </Typography>
        )}
        {selectedSituation && (
          <Box sx={{ mt: 1, width: "100%" }}>
            <Button
              variant="contained"
              size="small"
              startIcon={<AddLocationIcon />}
              onClick={onAddToIncident}
              fullWidth
              sx={{
                textTransform: "none",
                backgroundColor: "#EAF2FF",
                color: "#0060FF",
                boxShadow: "none",
                borderRadius: "8px",
                fontSize: 12,
                py: 1,
              }}
            >
              Add to incident
            </Button>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default SearchedLocationPopup;
