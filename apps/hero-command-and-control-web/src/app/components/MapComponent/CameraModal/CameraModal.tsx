"use client";
import { Asset } from "proto/hero/assets/v2/assets_pb";
import React, { useEffect, useState } from "react";
import LivePlayer from "../../HlsPlayer/LivePlayer";
import styles from "./CameraModal.module.css";

export const ANT_MEDIA_SERVER_URL = "http://54.201.139.119:5080";

type CameraModalProps = {
  selectedCameras: Asset[] | null;
  onClose: () => void;
};

// Helper function to safely parse JSON
const safeJsonParse = (jsonString: string, fallback: any = {}) => {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn("Failed to parse JSON:", error, "String:", jsonString);
    return fallback;
  }
};

// Helper function to extract KVS channel name from camera additionalInfoJson
const getKvsChannelName = (camera: Asset): string | null => {
  if (!camera.additionalInfoJson) {
    return null;
  }

  try {
    const additionalInfo = safeJsonParse(camera.additionalInfoJson);

    // Check if camera info exists and has metadata
    if (additionalInfo.camera?.metadata) {
      const channelName = additionalInfo.camera.metadata.kvs_normal_channel_name;
      // Return null if channel name is empty string or whitespace-only
      return channelName && channelName.trim() ? channelName : null;
    }

    return null;
  } catch (error) {
    console.error("Error parsing camera additionalInfoJson:", error);
    return null;
  }
};

export const CameraModal: React.FC<CameraModalProps> = ({
  selectedCameras,
  onClose,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (selectedCameras && selectedCameras.length > 0) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  }, [selectedCameras]);

  const handleClose = () => {
    setIsOpen(false);
    setTimeout(() => {
      onClose();
    }, 500);
  };

  const previewClass =
    selectedCameras && selectedCameras.length === 1
      ? styles.singleCamera
      : styles.multipleCameras;

  return (
    <div
      className={`${styles.cameraModal} ${isOpen ? styles.open : styles.closed
        }`}
    >
      <div className={styles.modalContent}>
        <div className={styles.header}>
          <div className={styles.cameraName}>
            {selectedCameras && selectedCameras[0].name}
          </div>
          <button className={styles.closeButton} onClick={handleClose} />
        </div>
        <div className={`${styles.cameraPreview} ${previewClass}`}>
          {selectedCameras &&
            selectedCameras.map((camera) => {
              // Check if camera has KVS configuration
              const kvsChannelName = getKvsChannelName(camera);

              if (kvsChannelName) {
                // Use KVS LivePlayer implementation
                console.log('Using KVS LivePlayer for camera:', camera.name, 'with channel:', kvsChannelName);
                return (
                  <div key={camera.name} className={styles.cameraPlayer}>
                    <LivePlayer
                      cameraName={camera.name}
                      streamName={kvsChannelName}
                    />
                  </div>
                );
              } else {
                // Use Ant Media implementation
                console.log('Using Ant Media for camera:', camera.name);

                // Use camera name as the stream ID for Ant Media
                const streamName = camera.name;
                // URL encode the stream name to handle special characters
                const encodedStreamName = encodeURIComponent(streamName);
                const iframeSrc = `${ANT_MEDIA_SERVER_URL}/WebRTCAppEE/play.html?id=${encodedStreamName}&playOrder=hls`;

                return (
                  <div key={camera.name} className={styles.cameraPlayer}>
                    <iframe
                      src={iframeSrc}
                      width="100%"
                      height="100%"
                      style={{ border: 0, borderRadius: 8, minHeight: 300 }}
                      allowFullScreen
                      title={camera.name}
                    />
                  </div>
                );
              }
            })}
        </div>
      </div>
    </div>
  );
};
