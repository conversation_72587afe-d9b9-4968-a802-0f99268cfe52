.cameraModal {
  position: fixed;
  top: 54px;
  right: 20px;
  width: 500px;
  background: #f7f7f7;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transform: translateX(120%);
  transition: transform 0.3s ease-in-out;
  z-index: 1000;
  border-radius: 8px;
}

.open {
  transform: translateX(0);
}

.closed {
  transform: translateX(120%);
}

.modalContent {
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 0 4px;
}

.cameraName {
  font-weight: 600;
  font-size: 1.1rem;
  color: #333;
}

.closeButton {
  position: relative;
  background: white;
  color: #666;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.closeButton:hover {
  background: white;
  transform: scale(1.1);
  color: #333;
}

.closeButton::after {
  content: '×';
  font-size: 24px;
  line-height: 1;
  margin-top: -2px;
}

.cameraPreview {
  width: 100%;
  aspect-ratio: 4 / 3;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
  padding: 0;
  background: #f7f7f7;
  border-radius: 8px;
  border: 2px solid #000;
  box-sizing: border-box;
}

.singleCamera {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.multipleCameras {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  max-height: 60vh;
  padding: 16px;
  height: 100%;
  box-sizing: border-box;
}

.cameraPlayer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.cameraPlayer iframe {
  width: 100%;
  height: 100%;
  min-height: 0;
  min-width: 0;
  border: 0;
  border-radius: 8px;
  display: block;
}

.controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 3;
  display: flex;
  gap: 8px;
}

.refreshButton {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.refreshButton:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f7f7f7;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}

.loading {
  text-align: center;
  color: #333;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e0e0e0;
  border-radius: 50%;
  border-top-color: #333;
  animation: spin 1s ease-in-out infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.errorOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}

.error {
  color: #ff4444;
  text-align: center;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.cameraInfo {
  position: relative;
  margin-bottom: 12px;
  padding: 8px 0;
  border-radius: 4px;
  color: #333;
  width: fit-content;
}

.cameraId {
  font-size: 0.9em;
  opacity: 0.8;
}

.cameraAddress {
  font-size: 0.9em;
  color: #666;
  margin-top: 4px;
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.modalLoadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f7f7f7;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 8px;
}