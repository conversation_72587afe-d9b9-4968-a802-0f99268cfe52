'use client';
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { signIn, signOut, signUp as amplifySignUp, confirmSignUp as amplifyConfirmSignUp, getCurrentUser } from 'aws-amplify/auth';
import { resetPassword, confirmResetPassword } from 'aws-amplify/auth';
import { SignUpInput, ConfirmSignUpInput } from '@aws-amplify/auth';
import { getTokensFromCookies,removeTokensFromLocalStorage, removeTokensFromCookies, saveTokensToLocalStorage } from '@/app/utils/auth';

// Configure Amplify Auth
import { Amplify } from 'aws-amplify';

const authConfig = {
  userPoolId: process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID || '',
  userPoolClientId: process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || '',
  identityPoolId: process.env.NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID || '',
};

Amplify.configure({
  Auth: {
    Cognito: authConfig
  }
});

interface AuthContextType {
  isAuthenticated: boolean;
  loading: boolean;
  user: any | null;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  localLogout: () => Promise<void>;
  signUp: (username: string, password: string, email: string) => Promise<void>;
  confirmSignUp: (username: string, code: string) => Promise<void>;
  forgotPassword: (username: string) => Promise<void>;
  confirmForgotPassword: (username: string, code: string, newPassword: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<any | null>(null);

  useEffect(() => {
    // Check if user is already authenticated
    const checkAuth = async () => {
      try {
        console.log('AuthContext: Starting auth check, URL:', window.location.href);
        
        // First check for tokens in cookies
        const tokens = getTokensFromCookies();
        console.log('AuthContext: Tokens from cookies:', !!tokens.accessToken);
        
        if (tokens.accessToken && tokens.username) {
          console.log('AuthContext: Found tokens in cookies, setting authenticated');
          // If we have tokens in cookies, set the user as authenticated
          setIsAuthenticated(true);
          setUser({ username: tokens.username });
          saveTokensToLocalStorage(tokens); // save tokens to localStorage to ensure Amplify can find them
          setLoading(false);
          return;
        }
        
        console.log('AuthContext: No tokens in cookies, trying getCurrentUser...');
        // If no tokens in cookies, try getCurrentUser
        const currentUser = await getCurrentUser();
        console.log('AuthContext: getCurrentUser successful:', currentUser);
        setIsAuthenticated(true);
        setUser(currentUser);
        // We don't save tokens here as they're already in localStorage from Amplify
      } catch (error) {
        console.log('AuthContext: Authentication failed:', error);
        // User is not authenticated or refresh failed
        setIsAuthenticated(false);
        setUser(null);
      }
      setLoading(false);
      console.log('AuthContext: Authentication check complete');
    };
    checkAuth();
  }, []);


  const login = async (username: string, password: string) => {
    try {
      const { isSignedIn } = await signIn({ username, password });
      if (isSignedIn) {
        // First check for tokens in cookies
        const tokens = getTokensFromCookies();
        
        if (tokens.accessToken && tokens.username) {
          // If we have tokens in cookies, set the user as authenticated
          setIsAuthenticated(true);
          setUser({ username: tokens.username });
          saveTokensToLocalStorage(tokens);
          return;
        }
        
        // If no tokens in cookies, use getCurrentUser
        const currentUser = await getCurrentUser();
        setIsAuthenticated(true);
        setUser(currentUser);
        // We don't save tokens here as they're already in localStorage from Amplify
      }
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const logout = async () => {
    window.location.href = '/logout';
  };

  const localLogout = async () => {
    try {
      console.log('AuthContext: Starting logout process...');
      
      // Global sign out handles both local and server-side token invalidation
      await signOut({ global: true });
      setIsAuthenticated(false);
      setUser(null);
      removeTokensFromLocalStorage();
      removeTokensFromCookies();
      
      console.log('AuthContext: Logout complete');
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    }
  };

  const signUp = async (username: string, password: string, email: string) => {
    try {
      const signUpInput: SignUpInput = {
        username,
        password,
        options: {
          userAttributes: {
            email,
          }
        }
      };
      await amplifySignUp(signUpInput);
    } catch (error) {
      console.error('Sign up failed:', error);
      throw error;
    }
  };

  const confirmSignUp = async (username: string, code: string) => {
    try {
      const confirmSignUpInput: ConfirmSignUpInput = {
        username,
        confirmationCode: code
      };
      await amplifyConfirmSignUp(confirmSignUpInput);
    } catch (error) {
      console.error('Confirmation failed:', error);
      throw error;
    }
  };

  const forgotPassword = async (username: string) => {
    try {
      await resetPassword({ username });
    } catch (error) {
      console.error('Forgot password failed:', error);
      throw error;
    }
  };

  const confirmForgotPassword = async (username: string, code: string, newPassword: string) => {
    try {
      await confirmResetPassword({
        username,
        confirmationCode: code,
        newPassword
      });
    } catch (error) {
      console.error('Confirm forgot password failed:', error);
      throw error;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        loading,
        user,
        login,
        logout,
        localLogout,
        signUp,
        confirmSignUp,
        forgotPassword,
        confirmForgotPassword,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 