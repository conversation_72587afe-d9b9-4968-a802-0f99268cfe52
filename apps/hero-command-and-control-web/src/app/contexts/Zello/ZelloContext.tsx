"use client";
import React, { createContext, useState, useEffect, ReactNode, useMemo } from "react";
import Script from "next/script";
import { useAssetByCognitoSub, useAssetPrivate, useGetZelloChannels } from "@/app/apis/services/workflow/assets/hooks";
import { getTokensFromCookies } from "@/app/utils/auth";
import { ZelloChannel } from "proto/hero/assets/v2/assets_pb";


interface ZelloContextType {
  sessions: Record<string, any>;
  channelStatuses: Record<string, "notConnected" | "connecting" | "connected">;
  activeChannel: string | null;
  isSpeaking: boolean;
  startTalk: () => void;
  stopTalk: () => void;
  receivingFrom: string;
  setDesiredChannels: (channels: string[]) => void;
  zelloChannels: ZelloChannel[];
}

export const ZelloContext = createContext<ZelloContextType | null>(null);

interface ZelloProviderProps {
  children: ReactNode;
}

export const ZelloProvider: React.FC<ZelloProviderProps> = ({ 
  children, 
}) => {
  const { data: zelloChannelsData } = useGetZelloChannels();
  const zelloChannels = zelloChannelsData?.zelloChannels || [];
  const { sub } = getTokensFromCookies();
  const whoamiQuery = useAssetByCognitoSub(sub || '');
  const whoami = whoamiQuery.data?.asset;
  const assetPrivateQuery = useAssetPrivate(whoami?.id || '');
  const zelloCreds = assetPrivateQuery.data?.zelloCreds;
  const [scriptLoaded, setScriptLoaded] = useState(false);
  
  // Track multiple sessions by channel
  const [sessions, setSessions] = useState<Record<string, any>>({});
  const [channelStatuses, setChannelStatuses] = useState<Record<string, "notConnected" | "connecting" | "connected">>({});
  const [desiredChannels, setDesiredChannels] = useState<string[]>([]);
  const [activeChannel, setActiveChannel] = useState<string | null>(null);
  
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [receivingFrom, setReceivingFrom] = useState("");
  const [outgoingMessage, setOutgoingMessage] = useState<any>(null);

  const zelloUrl = useMemo(() => "wss://zellowork.io/ws/herosafety", []);

  // Initialize a session for a specific channel
  const initSession = async (channelName: string) => {
    updateChannelStatus(channelName, "connecting");
    
    if (!zelloCreds) {
      console.error("Zello credentials not available.");
      return;
    }
    
    if (!(window.ZCC && window.ZCC.Sdk)) {
      console.error("ZCC SDK is not available.");
      return;
    }
    
    try {
      // Initialize SDK if not already done
      if (!Object.keys(sessions).length) {
        await window.ZCC.Sdk.init({
          player: true,
          decoder: true,
          recorder: true,
          encoder: true,
        });
      }
    } catch (err) {
      console.trace(err);
      return;
    }

    const { username, password } = zelloCreds;

    const usernameWithPlusSignsAsSpaces = username.replace(/\+/g, ' ');
    const newSession = new window.ZCC.Session({
      serverUrl: zelloUrl,
      username: usernameWithPlusSignsAsSpaces,
      password,
      channel: channelName,
      maxConnectAttempts: 10,
      connectRetryTimeoutMs: 1000,
      autoSendAudio: true,
    });

    try {
      await newSession.connect();
      
      // Set up event listeners for this session
      setupSessionEventListeners(newSession, channelName);
      
      // Update sessions state
      setSessions(prev => ({
        ...prev,
        [channelName]: newSession
      }));
      
      updateChannelStatus(channelName, "connected");
      
      // If this is the first channel or there's no active channel, set it as active
      if (!activeChannel) {
        setActiveChannel(channelName);
      }
    } catch (error) {
      console.error(`Failed to connect to channel ${channelName}:`, error);
      updateChannelStatus(channelName, "notConnected");
    }
  };

  // Helper to update channel status
  const updateChannelStatus = (channelName: string, status: "notConnected" | "connecting" | "connected") => {
    setChannelStatuses(prev => ({
      ...prev,
      [channelName]: status
    }));
  };

  // Set up event listeners for a session
  const setupSessionEventListeners = (session: any, channelName: string) => {
    const handleVoiceStart = (incomingMessage: any) => {
      const from = incomingMessage.options?.messageData?.from;
      // Only update UI if this is from the active channel
      if (channelName === activeChannel) {
        setReceivingFrom(from);
      }
    };

    const handleVoiceStop = () => {
      // Only update UI if this is from the active channel
      if (channelName === activeChannel) {
        setReceivingFrom("");
      }
    };

    session.on(window.ZCC.Constants.EVENT_INCOMING_VOICE_DID_START, handleVoiceStart);
    session.on(window.ZCC.Constants.EVENT_INCOMING_VOICE_DID_STOP, handleVoiceStop);
  };

  // Disconnect a session for a specific channel
  const disconnectSession = async (channelName: string) => {
    const session = sessions[channelName];
    if (session) {
      try {
        // Disconnect the session
        await session.disconnect();
        
        // Update sessions state
        setSessions(prev => {
          const newSessions = { ...prev };
          delete newSessions[channelName];
          return newSessions;
        });
        
        updateChannelStatus(channelName, "notConnected");
      } catch (error) {
        console.error(`Failed to disconnect from channel ${channelName}:`, error);
      }
    }
  };

  // Function to set desired channels
  const handleSetDesiredChannels = async (channelIds: string[]) => {
    if (channelIds.length > 1) {
        throw new Error("multiple channels not supported yet")
    }
    // Check if arrays have the same elements regardless of order
    if (channelIds.length === desiredChannels.length && 
        channelIds.every(channelId => desiredChannels.includes(channelId)) &&
        desiredChannels.every(channelId => channelIds.includes(channelId))) return;
    setDesiredChannels(channelIds);
    
    // If no channels provided, disconnect all
    if (!channelIds.length) {
      Object.keys(sessions).forEach(disconnectSession);
      setActiveChannel(null);
      return;
    }
    
    // Disconnect channels that are no longer desired
    await Promise.all(
      Object.keys(sessions)
        .filter(channelName => !channelIds.includes(channelName))
        .map(channelName => disconnectSession(channelName))
    );

    // Connect to new channels
    channelIds.forEach(channelId => {
        console.log('connected to: ', channelId)
      if (!sessions[channelId]) {
        initSession(channelId);
      }
    });
    setActiveChannel(channelIds[0]);
  };

  // Initialize sessions once the script is loaded and credentials are available.
  useEffect(() => {
    if (scriptLoaded && zelloCreds && zelloChannels.length > 0) {
      const initChannel = zelloChannels[0].zelloChannelId;
          initSession(initChannel);
    }
  }, [scriptLoaded, zelloCreds, zelloChannels]);


  // Ping interval to monitor connection health for all sessions
  useEffect(() => {
    const intervalId = setInterval(() => {
      Object.entries(sessions).forEach(([channelName, session]) => {
        if (!session) return;
        
        const sendMessagePromise = session.sendCommandWithCallback("ping", {}, null);
        const timeoutId = setTimeout(() => {
          updateChannelStatus(channelName, "notConnected");
        }, 5000);

        sendMessagePromise
          .then(() => clearTimeout(timeoutId))
          .catch((error: any) => {
            clearTimeout(timeoutId);
            const expectedError = "unknown command";
            if (error.toString() === expectedError) {
                // Treat this as a successful ping.
                updateChannelStatus(channelName, "connected");
            } else {
                updateChannelStatus(channelName, "notConnected");
                console.error(`Error sending message to channel ${channelName}:`, error);
            }
          });
      });
    }, 5000);

    return () => clearInterval(intervalId);
  }, [sessions]);

  const startTalk = () => {
    console.log(sessions)
    if (activeChannel && sessions[activeChannel]) {
      setIsSpeaking(true);
      const msg = sessions[activeChannel].startVoiceMessage();
      setOutgoingMessage(msg);
    } else {
      console.error("No active session available.");
    }
  };

  const stopTalk = () => {
    if (outgoingMessage) {
      outgoingMessage.stop();
      setIsSpeaking(false);
    }
  };

  const contextValue: ZelloContextType = {
    sessions,
    channelStatuses,
    activeChannel,
    isSpeaking,
    startTalk,
    stopTalk,
    receivingFrom,
    setDesiredChannels: handleSetDesiredChannels,
    zelloChannels
  };

  return (
    <ZelloContext.Provider value={contextValue}>
      <Script
        src="https://zello.io/sdks/js/latest/zcc.sdk.js"
        strategy="afterInteractive"
        onLoad={() => setScriptLoaded(true)}
      />
      {children}
    </ZelloContext.Provider>
  );
};
