type Environment = "local" | "dev" | "prod";

interface ServiceUrls {
  [key: string]: Record<Environment, string>;
}

const SERVICE_URLS: ServiceUrls = {
  fieldReport: {
    local: "http://localhost:9080",
    dev: "https://dev.api.example.com",
    prod: "https://fieldreport.api.demo-1.gethero.com",
  },
  asset: {
    local: "http://localhost:9082",
    dev: "https://dev.api.example.com",
    prod: "https://assets.api.demo-1.gethero.com",
  },
  situations: {
    local: "http://localhost:9081",
    dev: "https://dev.api.example.com",
    prod: "https://situations.api.demo-1.gethero.com",
  },
  commandLoop: {
    local: "http://localhost:8082",
    dev: "https://dev.api.example.com",
    prod: "https://command.api.demo-1.gethero.com",
  },
  communications: {
    local: "http://localhost:9084",
    dev: "https://dev.api.example.com",
    prod: "https://communications.api.demo-1.gethero.com",
  },
  workflow: {
    local: "http://localhost:9086",
    dev: "https://dev.api.example.com",
    prod: "https://workflow.api.demo-1.gethero.com",
  },
  filerepository: {
    local: "http://localhost:9089",
    dev: "https://dev.api.example.com",
    prod: "https://filerepository.api.demo-1.gethero.com",
  },
};

const getEnvironment = (): Environment => {
  return  __DEV__ ? "local" : "prod";
};

const ENVIRONMENT: Environment = getEnvironment();

export const getServiceUrl = (service: keyof ServiceUrls): string => {
  const urls = SERVICE_URLS[service];
  if (!urls) {
    throw new Error(`Invalid service: ${service}`);
  }
  return urls[ENVIRONMENT] || urls.prod; // Fallback to prod
};

export const FIELDREPORT_SERVICE_URL = getServiceUrl("fieldReport");
export const ASSET_SERVICE_URL = getServiceUrl("asset");
export const SITUATIONS_SERVICE_URL = getServiceUrl("situations");
export const COMMANDLOOP_SERVICE_URL = getServiceUrl("commandLoop");
export const COMMUNICATIONS_SERVICE_URL = getServiceUrl("communications");
export const WORKFLOW_SERVICE_URL = getServiceUrl("workflow");
