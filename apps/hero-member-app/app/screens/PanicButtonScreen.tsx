import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Animated,
  PanResponder,
  Alert,
  Platform,
  Button,
  ActivityIndicator,
} from "react-native";
import { jwtDecode } from "jwt-decode";
import { useRouter } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { CountdownCircleTimer } from "react-native-countdown-circle-timer";
import useLocationStore from "../store/useLocationStore";
import { useCreateFieldReport } from "../apis/services/fieldreport/hooks";
import {
  CreateFieldReportRequest,
  FieldReportStatus,
  ReportType,
} from "proto/hero/fieldreport/v1/fieldreport_pb";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { useCreateAsset, useGetAssetByCognitoSub } from "../apis/services/assets/hooks";
import { AssetType, Asset, CreateAssetRequest, GetAssetByCognitoSubRequest } from "proto/hero/assets/v1/assets_pb";
import { useUserInfoStore } from "../store/useUserInfoStore";
import { useGetVideoCallAccessToken } from "../apis/services/communications/videocall/hooks";
import { CreateGroupChatRequest, GetChatUserTokenRequest, GetVideoCallAccessTokenRequest } from "proto/hero/communications/v1/conversation_pb";
import { Camera } from "expo-camera";
import { useAuthStore } from "../store/useAuthStore";
import * as WebBrowser from "expo-web-browser";
import { revokeAsync } from "expo-auth-session";
import { useCreateGroupChat, useGetChatUserToken } from "../apis/services/communications/chat/hooks";

const TEXTS = {
  title: "SOS Alert",
  sosMessage: "SOS will be sent",
  panicButtonText: "Tap or Hold",
  countdownDescriptionBold: "After 3 seconds",
  countdownDescription:
    "if the alert has not been canceled, your alert and location will be sent to HERO support and your safety contacts.",
  sliderText: "Slide to Cancel",
  bottomText: "Hero and 2 contacts will be notified",
  heroOnTheWay: "Hero is on the way",
  cancelButtonLabel: "Cancel",
};

const { width } = Dimensions.get("window");
const buttonSize = width * 0.6;
const SLIDER_WIDTH = width * 0.8;
const SLIDER_HEIGHT = 50;
const SLIDER_MARGIN = 10;

const STATES = {
  IDLE: "idle",
  COUNTDOWN: "countdown",
  HERO_ON_THE_WAY: "heroOnTheWay",
};

interface DecodedToken {
  email: string;
  sub: string;
}

const clientId = "3fv7mur82c02q30o7oflj0u5ub";
const userPoolUrl =
  "https://auth.demo-1.gethero.com";
const redirectUri = "myapp://callback/";
const discoveryDocument = {
  authorizationEndpoint: userPoolUrl + "/oauth2/authorize",
  tokenEndpoint: userPoolUrl + "/oauth2/token",
  revocationEndpoint: userPoolUrl + "/oauth2/revoke",
};

export default function PanicButtonScreen() {
  const router = useRouter();

  // ------ Auth store (for logout) ------
  const { authTokens, setAuthTokens } = useAuthStore();

  let email = "";
  let cognitoJwtSub = "";

  if (authTokens?.idToken) {
    try {
      const decoded = jwtDecode<DecodedToken>(authTokens.idToken);
      email = decoded.email;
      cognitoJwtSub = decoded.sub;
    } catch (error) {
      console.error("Error decoding idToken:", error);
    }
  }

  const { data: getAssetByCongitoSubData, error: getAssetByCongitoSubError } = useGetAssetByCognitoSub(
    {
      cognitoJwtSub: cognitoJwtSub,
    } as GetAssetByCognitoSubRequest,
    3000
  );

  const userInfoFromServer = getAssetByCongitoSubData?.asset;
  useLocationStore.getState().updateUserId(userInfoFromServer?.id ?? "");

    // Create user if not found
    useEffect(() => {
      if (getAssetByCongitoSubError) {
        console.error('Error fetching user info:', getAssetByCongitoSubError.message);
        if (getAssetByCongitoSubError.message.includes("not found") ||
          getAssetByCongitoSubError.message.includes("404")) {
          if (!email) {
            console.error("No email found in token.");
            return;
          }
          const username = email.split("@")[0];
          createUserMutation.mutate(
            {
              asset: {
                name: username,
                type: AssetType.USER,
                cognitoJwtSub: cognitoJwtSub,
              },
            } as CreateAssetRequest,
            {
              onSuccess: (data) => {
                if (data.asset) {
                  setUserInfo(data.asset);
                  console.log("User created successfully:", data.asset);
                } else {
                  console.error("No asset data received");
                  Alert.alert("Error", "Failed to create user. Please try again.");
                }
              },
              onError: (error) => {
                console.error("Failed to create user:", error);
                Alert.alert("Error", "Failed to create user. Please try again.");
              },
            }
          );
        }
      }
    }, [getAssetByCongitoSubError]);

  useEffect(() => {
    if (userInfoFromServer && userInfoFromServer?.id !== useUserInfoStore.getState().userInfo?.id) {
      setUserInfo(userInfoFromServer);
    }
  }, [userInfoFromServer]);
  
  // --------- User-creation logic ---------
  const setUserInfo = useUserInfoStore((state) => state.setUserInfo);
  const createUserMutation = useCreateAsset();
  const getVideoCallAccessToken = useGetVideoCallAccessToken();
  const getChatAccessToken = useGetChatUserToken();
  const createGroupChat = useCreateGroupChat();

  const requestExpoPermissions = async () => {
    if (Platform.OS !== 'web') {
      const { status: cameraStatus } = await Camera.requestCameraPermissionsAsync();
      const { status: audioStatus } = await Camera.requestMicrophonePermissionsAsync();
    }
  };

  useEffect(() => {
    requestExpoPermissions();
  }, []);

  // --------- Field report logic ---------
  const [isCountdownActive, setIsCountdownActive] = useState(false);
  const [reportId, setReportId] = useState<string | null>(null);
  const location = useLocationStore((state) => state.location);
  const createFieldReportMutation = useCreateFieldReport();
  const [currentState, setCurrentState] = useState(STATES.IDLE);

  // Animated values
  const sosMessageOpacity = useRef(new Animated.Value(0)).current;
  const belowTextOpacity = useRef(new Animated.Value(0)).current;
  const sliderOpacity = useRef(new Animated.Value(0)).current;
  const heroMessageOpacity = useRef(new Animated.Value(0)).current;
  const cancelButtonOpacity = useRef(new Animated.Value(0)).current;
  const pulseScale = useRef(new Animated.Value(1)).current;

  const navigateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handler for panic button press
  const handlePanicButtonPress = async () => {
    // Instead of logging in, we just move to countdown:
    setCurrentState(STATES.COUNTDOWN);
  };

  // Transition to HeroOnTheWay
  const transitionToHeroOnTheWay = () => {
    setCurrentState(STATES.HERO_ON_THE_WAY);

    createFieldReportMutation.mutate(
      {
        createdBy: userInfoFromServer?.id,
        reportType: ReportType.PANIC,
        status: FieldReportStatus.CREATED,
        latitude: location?.latitude ?? 0,
        longitude: location?.longitude ?? 0,
      } as CreateFieldReportRequest,
      {
        onSuccess: (data) => {
          if (data.report) {
            setReportId(data.report.id);
          }

            getVideoCallAccessToken.mutate({
            userId: userInfoFromServer?.id ?? "",
            channel: `reporter${data.report?.id?.replace(/[^a-zA-Z0-9]/g, '')}`,
            } as GetVideoCallAccessTokenRequest,
            {
              onSuccess: (data) => {
              console.log("Successfully created video call access token");

              },
              onError: (error) => {
              console.error("Error fetching access token:", error);
              },
            },
            )

          getChatAccessToken.mutate({
            userId: userInfoFromServer?.id ?? "",
          } as GetChatUserTokenRequest,
            {
              onSuccess: (data) => {
                console.log("Successfully created chat access token = ", JSON.stringify(data));

              },
              onError: (error) => {
                console.error("Error fetching access token:", error);
              },
            },
          )

            createGroupChat.mutate(
            {
              userId: userInfoFromServer?.id ?? "",
              groupName: "reporter/" + data.report?.id as string,
            } as CreateGroupChatRequest,
            {
              onSuccess: (data) => {
              console.log("Successfully created chat group");
              },
              onError: (error) => {
              console.error("Error creating chat group:", error);
              },
              onSettled: (data, error, variables) => {
              console.log("Payload sent for creating chat group:", variables);
              },
            },
            )
        },
        onError: (error) => {
          console.error("Failed to create report. Please try again.");
        },
      },
    );
  };

  useEffect(() => {
    navigateTimeoutRef.current = setTimeout(() => {
      if (createFieldReportMutation.data?.report && getVideoCallAccessToken.data?.accessToken && getChatAccessToken.data?.token && createGroupChat.data?.groupId) {
        router.push({
          pathname: "/screens/PanicSettingsScreen",
          params: {
            reportId: createFieldReportMutation.data.report.id,
            commsToken: getVideoCallAccessToken.data?.accessToken ?? "",
            commsChannel: getVideoCallAccessToken.data?.channel ?? "",
            commsAppId: getVideoCallAccessToken.data?.appId ?? "",
            chatAccessToken: getChatAccessToken.data?.token,
            chatAppKey: getChatAccessToken.data?.appKey,
            chatGroupId: createGroupChat.data.groupId,
          },
        });
        setCurrentState(STATES.IDLE);
        navigateTimeoutRef.current = null;
      }
    }, 3000);
  }, [createFieldReportMutation.data, getVideoCallAccessToken.data?.accessToken, getChatAccessToken.data?.token, createGroupChat.data?.groupId]);


  // Handler when countdown completes
  // Countdown callback
  const handleCountdownComplete = () => {
    transitionToHeroOnTheWay();
  };

  // Slider cancel logic
  const translationX = useRef(new Animated.Value(0)).current;
  const distance = useRef(0);

  // Reset slider
  const release = () => {
    Animated.spring(translationX, {
      toValue: 0,
      useNativeDriver: true,
    }).start();
  };

  // PanResponder to handle the "slide to cancel"
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderMove: (_, gestureState) => {
        if (gestureState.dx < 0) {
          translationX.setValue(Math.max(gestureState.dx, -distance.current));
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.dx <= -distance.current) {
          setCurrentState(STATES.IDLE);
          translationX.setValue(0);
          sosMessageOpacity.setValue(0);
          belowTextOpacity.setValue(0);
          sliderOpacity.setValue(0);
          heroMessageOpacity.setValue(0);
          cancelButtonOpacity.setValue(0);
        }
        release();
      },
    })
  ).current;

  // Manage animations
  useEffect(() => {
    if (currentState === STATES.IDLE) {
      translationX.setValue(0);
      sosMessageOpacity.setValue(0);
      belowTextOpacity.setValue(0);
      sliderOpacity.setValue(0);
      heroMessageOpacity.setValue(0);
      cancelButtonOpacity.setValue(0);

      // Start button pulse
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseScale, {
            toValue: 1.2,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseScale, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else if (currentState === STATES.COUNTDOWN) {
      pulseScale.stopAnimation();
      pulseScale.setValue(1);

      Animated.timing(sosMessageOpacity, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();

      Animated.timing(belowTextOpacity, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
        delay: 250,
      }).start();

      Animated.timing(sliderOpacity, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
        delay: 500,
      }).start();
    } else if (currentState === STATES.HERO_ON_THE_WAY) {
      Animated.parallel([
        Animated.timing(heroMessageOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(cancelButtonOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
          delay: 250,
        }),
      ]).start();
    }
  }, [
    currentState,
    sosMessageOpacity,
    belowTextOpacity,
    sliderOpacity,
    heroMessageOpacity,
    cancelButtonOpacity,
    pulseScale,
    translationX,
  ]);

  // Canceling hero on the way
  const handleCancel = () => {
    if (navigateTimeoutRef.current) {
      clearTimeout(navigateTimeoutRef.current);
      navigateTimeoutRef.current = null;
    }
    setCurrentState(STATES.IDLE);
  };

  useEffect(() => {
    return () => {
      if (navigateTimeoutRef.current) {
        clearTimeout(navigateTimeoutRef.current);
        navigateTimeoutRef.current = null;
      }
    };
  }, []);

  // ----------------- LOGOUT LOGIC -----------------
  const logout = async () => {
    console.log("Auth tokens:", JSON.stringify(authTokens, null, 2));
    setAuthTokens(null);
    if (!authTokens?.refreshToken || !discoveryDocument || !clientId) {
      return;
    }
    try {
      const urlParams = new URLSearchParams({
        client_id: clientId,
        logout_uri: redirectUri,
      });
      // Open the logout page in the browser
      await WebBrowser.openAuthSessionAsync(
        `${userPoolUrl}/logout?${urlParams.toString()}`
      );
      // Revoke the refresh token
      const revokeResponse = await revokeAsync(
        {
          clientId: clientId,
          token: authTokens?.accessToken,
        },
        discoveryDocument
      );
      if (revokeResponse) {
        router.replace("/screens/LoginScreen");
      }
    } catch (error) {
      console.error("Error during token revocation:", error);
      router.replace("/screens/LoginScreen");
    }
  };

  if (!userInfoFromServer) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Title at the top center */}
      <Text style={styles.title}>{TEXTS.title}</Text>

      {/* The rest of your existing panic flow code remains unchanged */}
      <View style={styles.messageContainer}>
        {(currentState === STATES.COUNTDOWN ||
          currentState === STATES.HERO_ON_THE_WAY) && (
            <Animated.Text
              style={[
                styles.sosMessage,
                {
                  opacity:
                    currentState === STATES.COUNTDOWN
                      ? sosMessageOpacity
                      : heroMessageOpacity,
                },
              ]}
            >
              {currentState === STATES.COUNTDOWN
                ? TEXTS.sosMessage
                : TEXTS.heroOnTheWay}
            </Animated.Text>
          )}
      </View>

      <View
        style={{
          marginBottom: 50,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        {currentState === STATES.IDLE && (
          <Animated.View
            style={[
              styles.pulse,
              {
                width: buttonSize * 1.2,
                height: buttonSize * 1.2,
                borderRadius: (buttonSize * 1.2) / 2,
                transform: [{ scale: pulseScale }],
              },
            ]}
          />
        )}

        {currentState === STATES.IDLE && (
          <TouchableOpacity
            style={[
              styles.panicButton,
              {
                width: buttonSize,
                height: buttonSize,
                borderRadius: buttonSize / 2,
              },
            ]}
            onPress={handlePanicButtonPress}
          >
            <Text style={styles.panicButtonText}>{TEXTS.panicButtonText}</Text>
          </TouchableOpacity>
        )}

        {currentState === STATES.COUNTDOWN && (
          <View
            style={{
              width: buttonSize,
              height: buttonSize,
              borderRadius: buttonSize / 2,
              backgroundColor: "#e0e0e0",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CountdownCircleTimer
              isPlaying
              duration={3}
              colors={["#cc4c4c", "#a43a3a", "#8b2e2e"]}
              colorsTime={[3, 1.5, 0]}
              onComplete={handleCountdownComplete}
              size={buttonSize}
              strokeWidth={20}
              strokeLinecap="square"
              trailColor="#e0e0e0"
            >
              {({ remainingTime }) => (
                <Text style={styles.countdownText}>{remainingTime}</Text>
              )}
            </CountdownCircleTimer>
          </View>
        )}

        {currentState === STATES.HERO_ON_THE_WAY && (
          <View
            style={{
              width: buttonSize,
              height: buttonSize,
              borderRadius: buttonSize / 2,
              backgroundColor: "#e0e0e0",
              justifyContent: "center",
              alignItems: "center",
              position: "relative",
            }}
          >
            <Image
              source={require("../../assets/images/HeroLogo.png")}
              style={{ width: 120, height: 146 }}
            />
          </View>
        )}
      </View>

      <View style={styles.belowCountdownContainer}>
        {currentState === STATES.COUNTDOWN && (
          <Animated.Text
            style={[styles.belowCountdownText, { opacity: belowTextOpacity }]}
          >
            <Text style={{ fontWeight: "700" }}>
              {TEXTS.countdownDescriptionBold}
            </Text>
            , {TEXTS.countdownDescription}
          </Animated.Text>
        )}

        {currentState === STATES.HERO_ON_THE_WAY && (
          <Animated.View style={{ opacity: heroMessageOpacity }}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleCancel}
            >
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </Animated.View>
        )}
      </View>

      {currentState === STATES.COUNTDOWN && (
        <Animated.View
          style={[styles.sliderContainer, { opacity: sliderOpacity }]}
          onLayout={(event) => {
            const { width: sliderWidth } = event.nativeEvent.layout;
            distance.current = sliderWidth - SLIDER_HEIGHT - SLIDER_MARGIN * 2;
          }}
        >
          <Text style={styles.sliderText}>{TEXTS.sliderText}</Text>
          <Animated.View
            style={[
              styles.slider,
              { transform: [{ translateX: translationX }] },
            ]}
            {...panResponder.panHandlers}
          />
        </Animated.View>
      )}

      {currentState === STATES.IDLE && (
        <Text style={styles.bottomText}>{TEXTS.bottomText}</Text>
      )}

      {/* Show a logout button IF we have tokens */}
      {authTokens && (
        <View style={{ position: "absolute", top: 40, right: 20 }}>
          <Button title="Logout" onPress={logout} />
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#fff",
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  title: {
    position: "absolute",
    top: 70,
    fontSize: 16,
    fontWeight: "400",
    color: "#000",
    textAlign: "center",
  },
  messageContainer: {
    height: 60,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 30,
  },
  sosMessage: {
    fontSize: 30,
    color: "#666",
    fontWeight: "700",
    textAlign: "center",
  },
  panicButton: {
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#cc4c4c",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 10,
  },
  panicButtonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "bold",
    textAlign: "center",
  },
  countdownText: {
    fontSize: 40,
    fontWeight: "bold",
    color: "#000",
    textAlign: "center",
  },
  belowCountdownContainer: {
    height: 100,
    alignItems: "center",
    paddingHorizontal: 20,
    justifyContent: "center",
  },
  belowCountdownText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
  bottomText: {
    position: "absolute",
    bottom: 40,
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
  sliderContainer: {
    width: SLIDER_WIDTH,
    height: SLIDER_HEIGHT + SLIDER_MARGIN * 2,
    backgroundColor: "#f0f0f0",
    borderRadius: SLIDER_HEIGHT / 2,
    justifyContent: "center",
    position: "absolute",
    bottom: 50,
  },
  slider: {
    width: SLIDER_HEIGHT,
    height: SLIDER_HEIGHT,
    backgroundColor: "#cc4c4c",
    borderRadius: SLIDER_HEIGHT / 2,
    position: "absolute",
    right: SLIDER_MARGIN,
  },
  sliderText: {
    position: "absolute",
    left: 20,
    fontSize: 16,
    color: "#666",
    fontWeight: "bold",
  },
  pulse: {
    position: "absolute",
    backgroundColor: "#ffcccc",
    opacity: 0.5,
  },
  cancelButton: {
    backgroundColor: "rgba(220,220,220,1)",
    borderRadius: 40,
    padding: 2,
    justifyContent: "center",
    alignItems: "center",
    width: 60,
    height: 60,
  },
});
