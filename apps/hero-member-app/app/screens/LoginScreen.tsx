import React, { useEffect } from "react";
import { <PERSON>, Button, Alert, StyleSheet, Text } from "react-native";
import {
  ResponseType,
  useAuthRequest,
  exchangeCodeAsync,
} from "expo-auth-session";
import * as WebBrowser from "expo-web-browser";
import { useRouter } from "expo-router";
import { useAuthStore } from "../store/useAuthStore";

// TODO: Ensure this is called once in your app entry point
WebBrowser.maybeCompleteAuthSession();

// Replace with your actual config
const clientId = "3fv7mur82c02q30o7oflj0u5ub";
const userPoolUrl =
  "https://auth.demo-1.gethero.com";
const redirectUri = "myapp://callback/";

export default function LoginScreen() {
  const router = useRouter();
  const { authTokens, setAuthTokens } = useAuthStore();

  const handleLoginPress = async () => {
    await promptAsync();
  };

  // If already logged in, redirect
  useEffect(() => {
    console.log("Auth tokens", authTokens)
    if (authTokens) {
      router.replace("/screens/PanicButtonScreen");
    }
  }, [authTokens]);

  // Discovery document for Cognito
  const discoveryDocument = {
    authorizationEndpoint: `${userPoolUrl}/oauth2/authorize`,
    tokenEndpoint: `${userPoolUrl}/oauth2/token`,
    revocationEndpoint: `${userPoolUrl}/oauth2/revoke`,
  };

  // Set up the request
  const [request, response, promptAsync] = useAuthRequest(
    {
      clientId,
      responseType: ResponseType.Code,
      redirectUri,
      usePKCE: true,
    },
    discoveryDocument
  );

  // Handle response
  useEffect(() => {
    console.log("response", JSON.stringify(response, null, 2));
    const doTokenExchange = async () => {
      if (response?.type === "success") {
        try {
          const { code } = response.params;
          const tokenResult = await exchangeCodeAsync(
            {
              code,
              clientId,
              redirectUri,
              extraParams: {
                code_verifier: request?.codeVerifier || "",
              },
            },
            discoveryDocument
          );
          setAuthTokens(tokenResult);
          router.replace("/screens/PanicButtonScreen");
        } catch (error) {
          console.error("Token exchange failed:", error);
          Alert.alert("Error", "Login failed. Please try again.");
        }
      }
    };

    doTokenExchange();
  }, [response]);

  if (authTokens) {
    return (
      <View style={styles.container}>
        <Text style={styles.text}>You are already logged in!</Text>
        <Button
          title="Go to Map"
          onPress={() => router.push("/screens/PanicButtonScreen")}
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Button disabled={!request} title="Login" onPress={handleLoginPress} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, alignItems: "center", justifyContent: "center" },
  text: { fontSize: 16, marginBottom: 10 },
});
