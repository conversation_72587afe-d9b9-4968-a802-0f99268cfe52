COMPOSE_PROJECT_NAME := hero-core
export COMPOSE_PROJECT_NAME

proto:
	cd lib/proto && docker-compose up

AWS_REGION ?= us-west-2
AWS_PROFILE ?= default
AWS_ACCOUNT_ID ?= ************
SECRET_ID ?= local/services/communications/.env

# Authenticate with AWS ECR
ecr-login:
	aws ecr get-login-password --region $(AWS_REGION) --profile $(AWS_PROFILE) | docker login --username AWS --password-stdin $(AWS_ACCOUNT_ID).dkr.ecr.$(AWS_REGION).amazonaws.com

# This checks if the .env file exists, and if not, it fetches it from AWS Secrets Manager
scaffold:
	@# Only fetch if the file does not already exist
	@if [ ! -f "services/communications/.env" ]; then \
		aws sso login; \
		echo "⏬ Fetching $(SECRET_ID) from AWS Secrets Manager..."; \
		aws secretsmanager get-secret-value \
			--region $(AWS_REGION) \
			--profile $(AWS_PROFILE) \
			--secret-id $(SECRET_ID) \
			--query SecretString \
			--output text \
			> services/communications/.env ; \
		echo "✅ services/communications/.env created"; \
	else \
		echo "ℹ️  services/communications/.env already exists – skipping download"; \
	fi

run: ecr-login
	make scaffold && docker-compose up --build 2>&1 | tee /tmp/build.log || (echo "Build failed. Check /tmp/build.log for details" && exit 1)

run-insecure: ecr-login
	SKIP_PERMISSIONS_CHECK=true docker-compose up --build 2>&1 | tee /tmp/build.log || (echo "Build failed. Check /tmp/build.log for details" && exit 1)

stop:
	docker-compose stop

down:
	docker-compose down -v --remove-orphans

test:
	docker run --rm -v $(PWD):/app -w /app golang:latest bash -c 'for dir in services/*; do [ -d "$$dir" ] && cd "$$dir" && go test ./... -v && cd -; done'

# example usage: make run-service service_name
run-service:
	@if [ -z "$(word 2,$(MAKECMDGOALS))" ]; then \
		echo "Usage: make run-service <service-name>"; \
		exit 1; \
	fi
	@if ! grep -q "^[[:space:]]*$(word 2,$(MAKECMDGOALS)):" docker-compose.yml; then \
		echo "Service '$(word 2,$(MAKECMDGOALS))' not found in docker-compose.yml"; \
		exit 1; \
	fi
	docker-compose up --build $(word 2,$(MAKECMDGOALS))
	
gg-run:
	cd infra/scripts && ./setup-greengrass-mac.sh && ./run-greengrass-mac.sh

gg-deploy:
	cd infra/scripts && ./deploy-local-greengrass.sh


db: goose-install db-run
	goose up

db-prod: goose-install
	goose up --env=.env.production
	make db-print

db-prod-fga: goose-install
	docker run --rm \
		--name migrate \
		-e OPENFGA_DATASTORE_ENGINE=postgres \
		-e OPENFGA_DATASTORE_URI=$$(grep GOOSE_AUTH_DBSTRING .env.production | cut -d '=' -f2) \
		openfga/openfga:latest \
		migrate

db-new: goose-install
	@read -p "Enter migration name: " name; \
	if [ -z "$$name" ]; then \
		echo "Migration name cannot be empty"; \
		exit 1; \
	fi; \
	goose create $$name sql
	
db-down: goose-install db-run
	goose down

db-down-prod: goose-install
	goose down --env=.env.production
	make db-print
	
db-print: db-run
	docker-compose exec -T postgres psql -U postgres -d mydatabase -f /scripts/print-db-schema.sql > migrations/current-db-schema.md

goose-install:
	@if ! command -v goose > /dev/null; then \
		echo "Installing goose..."; \
		go install github.com/pressly/goose/v3/cmd/goose@latest; \
	else \
		echo "Goose already installed"; \
	fi

db-run:
	@if ! docker-compose ps | grep -q postgres; then \
		echo "Starting postgres container..."; \
		docker-compose up -d postgres; \
	fi; \
	echo "Waiting for PostgreSQL to be ready..."; \
	while ! docker-compose exec -T postgres pg_isready -U postgres -d mydatabase; do \
		sleep 2; \
	done; \
	echo "PostgreSQL is ready!"


clean: down db-reset fga-reset run-insecure
ready: init_metaorg init_org stop run


init_metaorg:
	@echo "Running meta org bootstrap..."
	cd infra/scripts && ./bootstrap.sh -r metaorg_setup_local -e local -a

init_org:
	@echo "Running org bootstrap..."
	cd infra/scripts && ./bootstrap.sh -r org_bootstrap_local


db-reset: pg-reset fga-reset

pg-reset: goose-install
	@echo "Resetting postgres container..."
	docker-compose stop postgres || true
	docker-compose rm -f postgres || true
	docker volume rm postgres-data || true
	docker-compose up -d postgres
	@echo "Waiting for PostgreSQL to be ready..."
	@while ! docker-compose exec -T postgres pg_isready -U postgres -d postgres; do \
		sleep 2; \
	done
	@echo "Running Goose migrations..."
	goose up



fga-reset:
	@echo "==> Resetting fga-postgres..."
	@echo "Stopping and removing fga-postgres container and associated anonymous volumes..."
	docker-compose rm -sfv fga-postgres
	@echo "Starting fga-postgres service..."
	docker-compose up -d fga-postgres
	@echo "Waiting for fga-postgres to be ready..."
	@count=0; \
	retries=30; \
	while ! docker-compose exec -T fga-postgres pg_isready -U postgres -d fga > /dev/null 2>&1; do \
		@count=$$((count + 1)); \
		if [ "$$count" -gt $$retries ]; then \
			echo "Error: fga-postgres did not become ready after $$(($$retries * 2)) seconds. Aborting."; \
			exit 1; \
		fi; \
		echo "Still waiting for fga-postgres to become healthy... (attempt $$count/$$retries)"; \
		sleep 2; \
	done
	@echo "fga-postgres is healthy."
	@echo "Running FGA migrations using 'openfga-migrate' service..."
	docker-compose run --rm openfga-migrate
	@echo "==> fga-postgres reset and migrations complete."


# Twilio webhook development targets
test-webhooks:
	@echo "🧪 Testing webhook development setup..."
	@./scripts/twilio-webhook-dev/test-setup.sh

start-ngrok:
	@echo "🚀 Starting ngrok tunnel..."
	@./scripts/twilio-webhook-dev/start-ngrok.sh

update-webhooks:
	@echo "📞 Updating Twilio webhooks..."
	@./scripts/twilio-webhook-dev/update-twilio-webhooks.sh

stop-webhooks:
	@echo "🛑 Stopping webhook development..."
	@pkill ngrok || true
	@rm -f .ngrok.log .ngrok_url .twilio_backup.json || true
	@echo "✅ Webhook development stopped"

dev-webhooks: test-webhooks
	@echo "🎯 Starting complete webhook development environment..."
	@./scripts/twilio-webhook-dev/start-ngrok.sh
	@./scripts/twilio-webhook-dev/update-twilio-webhooks.sh
	@echo "🎉 Webhook development environment ready!"
	@echo "💡 Use 'make stop-webhooks' to clean up when done"
	@echo "🐳 Starting services with webhook support..."
	@export COMMS_SERVER_PUBLIC_DOMAIN=$$(cat .ngrok_url 2>/dev/null || echo "communications.basic.api.gethero.com") && docker-compose up --build

%:
	@: