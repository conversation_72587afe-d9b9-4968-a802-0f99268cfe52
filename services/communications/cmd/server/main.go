package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"common/middleware"

	"connectrpc.com/connect"
	"github.com/getsentry/sentry-go"
	"github.com/joho/godotenv"

	cellularcallApi "communications/internal/cellularcall/api/connect"
	cellularcallUsecase "communications/internal/cellularcall/usecase"
	chatApi "communications/internal/chat/api/connect"
	chatDB "communications/internal/chat/data"
	chatUsecase "communications/internal/chat/usecase"
	pttApi "communications/internal/ptt/api/connect"
	pttUsecase "communications/internal/ptt/usecase"
	videocallApi "communications/internal/videocall/api/connect"
	videocallUsecase "communications/internal/videocall/usecase"
	conversationv1connect "proto/hero/communications/v1/conversationconnect"
)

const LocalEnv = "local"

// NewVideoCallServiceServer sets up the Connect-based Video Call service.
func NewVideoCallServiceServer() (string, http.Handler) {
	if os.Getenv("GO_ENV") == LocalEnv {
		if err := godotenv.Load(); err != nil {
			log.Println("No .env file found or error loading .env")
		}
	}
	agoraAppId := os.Getenv("AGORA_APP_ID")
	agoraAppCertificate := os.Getenv("AGORA_APP_CERTIFICATE")
	if agoraAppId == "" || agoraAppCertificate == "" {
		log.Fatal("AGORA_APP_ID or AGORA_APP_CERTIFICATE not provided")
	}

	videoCallUsecase := videocallUsecase.NewVideoCallUsecase(agoraAppId, agoraAppCertificate)
	videoCallServiceServer := videocallApi.NewVideoCallServiceServer(videoCallUsecase)
	path, handler := conversationv1connect.NewVideoCallServiceHandler(videoCallServiceServer)
	return path, handler
}

// NewChatServiceServer sets up the Connect-based Chat service.
func NewChatServiceServer() (string, http.Handler) {
	if os.Getenv("GO_ENV") == LocalEnv {
		if err := godotenv.Load(); err != nil {
			log.Println("No .env file found or error loading .env")
		}
	}
	agoraAppId := os.Getenv("AGORA_APP_ID")
	agoraAppCertificate := os.Getenv("AGORA_APP_CERTIFICATE")
	agoraChatAppName := os.Getenv("AGORA_CHAT_APP_NAME")
	agoraChatOrgName := os.Getenv("AGORA_CHAT_ORG_NAME")
	agoraChatHostURL := os.Getenv("AGORA_CHAT_HOST_URL")
	agoraChatAppKey := os.Getenv("AGORA_CHAT_APP_ID")

	// Validate required environment variables
	if agoraAppId == "" || agoraAppCertificate == "" || agoraChatAppName == "" ||
		agoraChatOrgName == "" || agoraChatHostURL == "" || agoraChatAppKey == "" {
		if agoraAppId == "" {
			log.Fatal("AGORA_APP_ID not provided")
		}
		if agoraAppCertificate == "" {
			log.Fatal("AGORA_APP_CERTIFICATE not provided")
		}
		if agoraChatAppName == "" {
			log.Fatal("AGORA_CHAT_APP_NAME not provided")
		}
		if agoraChatOrgName == "" {
			log.Fatal("AGORA_CHAT_ORG_NAME not provided")
		}
		if agoraChatHostURL == "" {
			log.Fatal("AGORA_CHAT_HOST_URL not provided")
		}
		if agoraChatAppKey == "" {
			log.Fatal("AGORA_CHAT_APP_ID not provided")
		}
	}

	chatRepo := chatDB.NewInMemoryChatRepo()
	chatUsecase := chatUsecase.NewChatUsecase(
		agoraAppId,
		agoraAppCertificate,
		agoraChatOrgName,
		agoraChatAppName,
		agoraChatHostURL,
		agoraChatAppKey,
		chatRepo,
	)

	chatServiceServer := chatApi.NewVideoCallServiceServer(chatUsecase)
	path, handler := conversationv1connect.NewChatServiceHandler(chatServiceServer)
	return path, handler
}

// NewCellularCallServiceServer sets up the Connect-based CellularCall service.
func NewCellularCallServiceServer() (string, http.Handler, cellularcallUsecase.CellularCallUsecase) {
	if os.Getenv("GO_ENV") == LocalEnv {
		if err := godotenv.Load(); err != nil {
			log.Println("No .env file found or error loading .env")
		}
	}

	twilioAccountSid := os.Getenv("TWILIO_ACCOUNT_SID")
	twilioAPIKeySid := os.Getenv("TWILIO_API_KEY_SID")
	twilioAPIKeySecret := os.Getenv("TWILIO_API_KEY_SECRET")
	twilioAuthToken := os.Getenv("TWILIO_AUTH_TOKEN")

	waitURL := os.Getenv("CALL_QUEUE_WAIT_URL")
	if waitURL == "" {
		waitURL = "https://twimlets.com/holdmusic?Bucket=com.twilio.music.classical"
	}

	// Validate required credentials
	if twilioAccountSid == "" || twilioAPIKeySid == "" || twilioAPIKeySecret == "" ||
		twilioAuthToken == "" {
		if twilioAccountSid == "" {
			log.Fatal("TWILIO_ACCOUNT_SID not provided")
		}
		if twilioAPIKeySid == "" {
			log.Fatal("TWILIO_API_KEY not provided")
		}
		if twilioAPIKeySecret == "" {
			log.Fatal("TWILIO_API_SECRET not provided")
		}
		if twilioAuthToken == "" {
			log.Fatal("TWILIO_AUTH_TOKEN not provided")
		}
	}

	situationsURL := os.Getenv("WORKFLOW_SERVICE_URL")
	if situationsURL == "" {
		log.Fatal("WORKFLOW_SERVICE_URL not provided")
	}

	commsServerPublicDomain := os.Getenv("COMMS_SERVER_PUBLIC_DOMAIN")
	if commsServerPublicDomain == "" {
		log.Fatal("COMMS_SERVER_PUBLIC_DOMAIN not provided")
	}

	orgServiceURL := os.Getenv("ORGS_SERVICE_URL")
	if orgServiceURL == "" {
		log.Fatal("ORGS_SERVICE_URL not provided")
	}

	// Use the same workflow service URL for assets (contains both assets and situations services)
	assetsURL := situationsURL

	cellularCallUsecase, err := cellularcallUsecase.NewCellularCallUsecase(
		twilioAccountSid,
		twilioAPIKeySid,
		twilioAPIKeySecret,
		twilioAuthToken,
		waitURL,
		situationsURL,
		orgServiceURL,
		commsServerPublicDomain,
		assetsURL,
	)

	if err != nil {
		log.Fatalf("Failed to initialize cellular call usecase: %v", err)
	}

	cellCallServiceServer := cellularcallApi.NewCellularCallServiceServer(cellularCallUsecase)

	// Add server-side tracing interceptor for distributed trace continuation
	serverTracingInterceptor := middleware.SentryServerTracingInterceptor()
	path, handler := conversationv1connect.NewCellularCallServiceHandler(
		cellCallServiceServer,
		connect.WithInterceptors(serverTracingInterceptor),
	)
	return path, handler, cellularCallUsecase
}

// NewVideoCallServiceServer sets up the Connect-based Video Call service.
func NewPTTServiceServer() (string, http.Handler) {
	if os.Getenv("GO_ENV") == LocalEnv {
		if err := godotenv.Load(); err != nil {
			log.Println("No .env file found or error loading .env")
		}
	}

	assetsURL := os.Getenv("WORKFLOW_SERVICE_URL")
	if assetsURL == "" {
		log.Fatal("WORKFLOW_SERVICE_URL not provided")
	}

	pttUsecase := pttUsecase.NewPTTUsecase(assetsURL)
	pttServiceServer := pttApi.NewPTTServiceServer(pttUsecase)
	path, handler := conversationv1connect.NewPTTServiceHandler(pttServiceServer)
	return path, handler
}

// registerTwilioWebhooks sets up the necessary Twilio webhook endpoints
func registerTwilioWebhooks(mux *http.ServeMux, cellularCallUC cellularcallUsecase.CellularCallUsecase) {
	// Primary voice webhook - handles all call types
	mux.HandleFunc("/hero.communications.v1.TwilioWebhookService/voice", func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if rec := recover(); rec != nil {
				log.Printf("Webhook panic recovered: %v", rec)
				sentry.CaptureException(fmt.Errorf("webhook panic: %v", rec))
				http.Error(w, "Internal server error", http.StatusInternalServerError)
			}
		}()
		if err := r.ParseForm(); err != nil {
			log.Printf("Error parsing form data: %v", err)
			sentry.CaptureException(err)
			http.Error(w, "Error parsing form data", http.StatusBadRequest)
			return
		}

		cellularCallUC.HandleVoiceRequest(w, r)
	})

	// Status callback handler. Currently endpoint is set up, but not used in business logic yet.
	mux.HandleFunc("/hero.communications.v1.TwilioWebhookService/callstatus", func(w http.ResponseWriter, r *http.Request) {
		if err := r.ParseForm(); err != nil {
			log.Printf("Error parsing form data: %v", err)
			http.Error(w, "Error parsing form data", http.StatusBadRequest)
			return
		}

		cellularCallUC.HandleCallStatusRequest(w, r)
	})

	// TwiML endpoint for connecting an asset to a specific customer call
	mux.HandleFunc("/hero.communications.v1.TwilioWebhookService/twiml/connectAgent", func(w http.ResponseWriter, r *http.Request) {
		cellularCallUC.HandleConnectAgentTwiMLRequest(w, r)
	})

	// Agent dial status callback handler
	mux.HandleFunc("/hero.communications.v1.TwilioWebhookService/twilio/agent-dial-status", func(w http.ResponseWriter, r *http.Request) {
		if err := r.ParseForm(); err != nil {
			log.Printf("Error parsing form data: %v", err)
			http.Error(w, "Error parsing form data", http.StatusBadRequest)
			return
		}

		cellularCallUC.HandleAgentDialStatusRequest(w, r)
	})

	// Wait/Hold webhook for smart queue and hold messaging
	mux.HandleFunc("/hero.communications.v1.TwilioWebhookService/waithold", func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if rec := recover(); rec != nil {
				log.Printf("WaitHold webhook panic recovered: %v", rec)
				sentry.CaptureException(fmt.Errorf("waithold webhook panic: %v", rec))
				http.Error(w, "Internal server error", http.StatusInternalServerError)
			}
		}()
		cellularCallUC.HandleWaitHoldRequest(w, r)
	})
}

func main() {
	// Initialize Sentry for error tracking and performance monitoring
	sentrySampleRate := 1.0 // Default to 100% for development
	if env := os.Getenv("ENVIRONMENT"); env == "production" {
		sentrySampleRate = 0.1 // 10% sampling for production
	}

	sentryOptions := sentry.ClientOptions{
		Dsn:              os.Getenv("SENTRY_DSN"),
		Environment:      os.Getenv("ENVIRONMENT"),
		ServerName:       "communications-service",
		EnableTracing:    true,
		TracesSampleRate: sentrySampleRate,
		SendDefaultPII:   false, // Security: Don't send PII in public safety system
		// Fine-grained, early sampling to drop low-signal DB-only traces before they are fully built.
		TracesSampler: sentry.TracesSampler(func(ctx sentry.SamplingContext) float64 {
			name := ctx.Span.Name // top-level span / transaction name
			if strings.HasPrefix(name, "db.") {
				// Retain if we already have business-specific identifiers on the span
				if _, ok := ctx.Span.Data["call_sid"]; ok {
					return 1.0 // always keep
				}
				if _, ok := ctx.Span.Data["asset_id"]; ok {
					return 1.0
				}
				if op, ok := ctx.Span.Data["db.operation"]; ok && op != "" {
					return 1.0
				}
				if table, ok := ctx.Span.Data["db.table"]; ok && table == "call_queue" {
					return 1.0
				}
				return 0.0 // drop noisy DB-only transactions
			}

			// Fallback to the uniform sample rate defined above.
			return sentrySampleRate
		}),
	}
	err := sentry.Init(sentryOptions)
	if err != nil {
		log.Printf("Sentry initialization failed: %v", err)
	}

	// Set up service handlers
	videoCallAPIPath, videoCallAPIHandler := NewVideoCallServiceServer()
	chatAPIPath, chatAPIHandler := NewChatServiceServer()
	cellularCallAPIPath, cellularCallAPIHandler, cellularCallUC := NewCellularCallServiceServer()
	pttAPIPath, pttAPIHandler := NewPTTServiceServer()
	mux := http.NewServeMux()

	// Register Connect RPC endpoints with logging middleware
	mux.Handle(videoCallAPIPath, videoCallAPIHandler)
	mux.Handle(chatAPIPath, chatAPIHandler)
	mux.Handle(cellularCallAPIPath, cellularCallAPIHandler)
	mux.Handle(pttAPIPath, pttAPIHandler)

	// Register simplified Twilio webhooks
	registerTwilioWebhooks(mux, cellularCallUC)

	// Register health and reflection endpoints

	// Create a new mux for health endpoints that bypasses auth
	healthMux := middleware.NewHealthMux(middleware.HealthMuxConfig{
		ServiceNames: []string{
			"hero.conversation.v1.VideoCallService",
			"hero.conversation.v1.ChatService",
			"hero.conversation.v1.CellularCallService",
			"hero.conversation.v1.PTTService",
		},
		HealthResponse: "YES HOW CAN I HELP YOU",
	})

	skipPerms := os.Getenv("SKIP_PERMISSIONS_CHECK") == "true"
	srv := middleware.NewServerWithHealth(
		":8080",
		mux,
		healthMux,
		!skipPerms,
	)

	log.Println("Communications server listening on http://localhost:8080")
	if err := srv.ListenAndServe(); err != nil {
		sentry.CaptureException(err)
		sentry.Flush(2 * time.Second) // Flush before fatal exit
		log.Fatalf("Failed to serve: %v", err)
	}
	// Ensure events are sent before normal shutdown
	sentry.Flush(2 * time.Second)
}
