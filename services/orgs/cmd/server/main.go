package main

import (
	database "common/database"
	"common/middleware"
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	orgs "orgs/internal"

	sentry "github.com/getsentry/sentry-go"

	orgRepository "orgs/internal/data"
	"orgs/internal/setup"
)

func main() {
	// Initialize Sentry for error tracking and performance monitoring
	sentrySampleRate := 1.0 // Default to 100% for development
	if env := os.Getenv("ENVIRONMENT"); env == "production" {
		sentrySampleRate = 0.1 // 10% sampling for production
	}

	sentryOptions := sentry.ClientOptions{
		Dsn:              os.Getenv("SENTRY_DSN"),
		Environment:      os.Getenv("ENVIRONMENT"),
		ServerName:       "orgs-service",
		EnableTracing:    true,
		TracesSampleRate: sentrySampleRate,
		SendDefaultPII:   false, // Security: Don't send PII
	}
	// Optional release tracking for deployment correlation
	if appVersion := os.Getenv("APP_VERSION"); appVersion != "" {
		sentryOptions.Release = appVersion
	}
	err := sentry.Init(sentryOptions)
	if err != nil {
		log.Printf("Sentry initialization failed: %v", err)
	}

	mux := http.NewServeMux()

	// Initialize all DB
	repositoryType := os.Getenv("REPO_TYPE")

	var postGresDB *sql.DB = nil

	if repositoryType == "postgres" {
		databaseURL, err := database.CreateDBURL()
		if err != nil {
			sentry.CaptureException(fmt.Errorf("failed to get postgres db url: %w", err))
			sentry.Flush(2 * time.Second)
			log.Fatalf("Failed to get postgres db url: %v", err)
		}
		var openError error
		postGresDB, openError = sql.Open("postgres", databaseURL)
		if openError != nil {
			sentry.CaptureException(fmt.Errorf("failed to open postgres db: %w", openError))
			sentry.Flush(2 * time.Second)
			log.Fatalf("Failed to open postgres db: %v", openError)
		}
	}

	// Initialize Asset Repository
	orgRepo, orgDB, err := orgRepository.NewOrgRepository(postGresDB)
	if err != nil {
		sentry.CaptureException(fmt.Errorf("failed to initialize org repository: %w", err))
		sentry.Flush(2 * time.Second)
		log.Fatalf("Failed to initialize org repository: %v", err)
	}

	// Initialize setup
	// This runs the cold-start bootstrap setup,
	// creating the meta org and root admin user
	// This should only be run once per environment
	err = setup.Initialize(setup.SetupConfig{
		DB:      postGresDB,
		OrgRepo: orgRepo,
	})
	if err != nil {
		log.Fatal("Failed to initialize setup:", err)
	}

	// Register all endpoints.
	orgs.RegisterRoutes(mux, orgDB, orgRepo)

	// Additional endpoints.
	// Create a new mux for health endpoints that bypasses auth
	healthMux := middleware.NewHealthMux(middleware.HealthMuxConfig{
		ServiceNames: []string{
			"hero.orgs.v1.OrgsService",
		},
		HealthResponse: "YES HOW CAN I HELP YOU",
	})

	skipPerms := os.Getenv("SKIP_PERMISSIONS_CHECK") == "true"
	srv := middleware.NewServerWithHealth(
		":8080",
		mux,
		healthMux,
		!skipPerms,
	)

	log.Println("Orgs server listening on http://localhost:8080")
	if err := srv.ListenAndServe(); err != nil {
		sentry.CaptureException(err)
		sentry.Flush(2 * time.Second)
		log.Fatalf("Failed to serve: %v", err)
	}
	// Ensure events are sent before normal shutdown
	sentry.Flush(2 * time.Second)
}
