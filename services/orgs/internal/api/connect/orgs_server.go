package connect

import (
	"context"
	"strings"

	"orgs/internal/usecase"
	pb "proto/hero/orgs/v1"

	"connectrpc.com/connect"
)

// OrgServer implements the orgs service.
type OrgServer struct {
	orgUseCase *usecase.OrgUseCase
}

// NewOrgServer creates a new OrgServer.
func NewOrgServer(orgUseCase *usecase.OrgUseCase) *OrgServer {
	return &OrgServer{
		orgUseCase: orgUseCase,
	}
}

// CreateOrg creates a new organization.
func (orgServer *OrgServer) CreateOrg(
	ctx context.Context,
	req *connect.Request[pb.CreateOrgRequest],
) (*connect.Response[pb.CreateOrgResponse], error) {
	err := orgServer.orgUseCase.CreateOrg(ctx, req.Msg.Org)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.CreateOrgResponse{
		Org: req.Msg.Org,
	})
	return response, nil
}

// GetOrg retrieves an organization by its ID.
func (orgServer *OrgServer) GetOrg(
	ctx context.Context,
	req *connect.Request[pb.GetOrgRequest],
) (*connect.Response[pb.GetOrgResponse], error) {
	org, err := orgServer.orgUseCase.GetOrg(ctx, req.Msg.Id)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.GetOrgResponse{
		Org: org,
	})
	return response, nil
}

// DeleteOrg deletes an organization.
func (orgServer *OrgServer) DeleteOrg(
	ctx context.Context,
	req *connect.Request[pb.DeleteOrgRequest],
) (*connect.Response[pb.DeleteOrgResponse], error) {
	err := orgServer.orgUseCase.DeleteOrg(ctx, req.Msg.Id)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.DeleteOrgResponse{})
	return response, nil
}

// ListOrgs returns a list of all organizations.
func (orgServer *OrgServer) ListOrgs(
	ctx context.Context,
	req *connect.Request[pb.ListOrgsRequest],
) (*connect.Response[pb.ListOrgsResponse], error) {
	orgs, err := orgServer.orgUseCase.ListOrgs(ctx)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.ListOrgsResponse{
		Orgs: orgs,
	})
	return response, nil
}

// ValidateOrgCreds validates organization credentials.
func (orgServer *OrgServer) ValidateOrgCreds(
	ctx context.Context,
	req *connect.Request[pb.ValidateOrgCredsRequest],
) (*connect.Response[pb.ValidateOrgCredsResponse], error) {
	valid, orgAPIUser, err := orgServer.orgUseCase.ValidateOrgCreds(ctx, req.Msg.Username, req.Msg.Password)
	if err != nil {
		return nil, connect.NewError(connect.CodeInvalidArgument, err)
	}
	response := connect.NewResponse(&pb.ValidateOrgCredsResponse{
		Valid:      valid,
		OrgApiUser: orgAPIUser,
	})
	return response, nil
}

// CreateOrgAPIUser creates a new organization API user.
func (orgServer *OrgServer) CreateOrgAPIUser(
	ctx context.Context,
	req *connect.Request[pb.CreateOrgAPIUserRequest],
) (*connect.Response[pb.CreateOrgAPIUserResponse], error) {
	response, _, err := orgServer.orgUseCase.CreateOrgAPIUser(ctx, req.Msg.OrgId)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	return connect.NewResponse(response), nil
}

// GetZelloChannels returns a list of all Zello channels for an organization.
func (orgServer *OrgServer) GetZelloChannels(
	ctx context.Context,
	req *connect.Request[pb.GetZelloChannelsRequest],
) (*connect.Response[pb.GetZelloChannelsResponse], error) {
	zelloChannels, err := orgServer.orgUseCase.GetZelloChannels(ctx)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.GetZelloChannelsResponse{
		ZelloChannels: zelloChannels,
	})
	return response, nil
}

// UpdateOrg updates an existing organization.
func (orgServer *OrgServer) UpdateOrg(
	ctx context.Context,
	req *connect.Request[pb.UpdateOrgRequest],
) (*connect.Response[pb.UpdateOrgResponse], error) {
	updatedOrg, err := orgServer.orgUseCase.UpdateOrg(ctx, req.Msg.Org)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.UpdateOrgResponse{
		Org: updatedOrg,
	})
	return response, nil
}

// GetOrgAPIUserPrivate retrieves the full API user for an organization, including password.
func (orgServer *OrgServer) GetOrgAPIUserPrivateById(
	ctx context.Context,
	req *connect.Request[pb.GetOrgAPIUserPrivateByIdRequest],
) (*connect.Response[pb.GetOrgAPIUserPrivateByIdResponse], error) {
	orgAPIUser, err := orgServer.orgUseCase.GetOrgAPIUserPrivateById(ctx, req.Msg.UserId)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.GetOrgAPIUserPrivateByIdResponse{
		OrgApiUser: orgAPIUser,
	})
	return response, nil
}

func (orgServer *OrgServer) InsertOrgQueue(
	ctx context.Context,
	req *connect.Request[pb.InsertOrgQueueRequest],
) (*connect.Response[pb.InsertOrgQueueResponse], error) {
	insertedOrgQueue, err := orgServer.orgUseCase.InsertOrgQueue(ctx, req.Msg)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.InsertOrgQueueResponse{
		OrgQueue: insertedOrgQueue,
	})
	return response, nil
}

func (orgServer *OrgServer) CreateOrgTwilioQueue(
	ctx context.Context,
	req *connect.Request[pb.CreateOrgTwilioQueueRequest],
) (*connect.Response[pb.CreateOrgTwilioQueueResponse], error) {
	queueSid, err := orgServer.orgUseCase.CreateTwilioQueue(ctx, req.Msg.OrgId, req.Msg.FriendlyName)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.CreateOrgTwilioQueueResponse{
		QueueSid: queueSid,
	})
	return response, nil
}

func (orgServer *OrgServer) TurnOnGuestMode(
	ctx context.Context,
	req *connect.Request[pb.TurnOnGuestModeRequest],
) (*connect.Response[pb.TurnOnGuestModeResponse], error) {
	err := orgServer.orgUseCase.TurnOnGuestMode(ctx, req.Msg.OrgId)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	return connect.NewResponse(&pb.TurnOnGuestModeResponse{
		Success: true,
	}), nil
}

func (orgServer *OrgServer) TurnOffGuestMode(
	ctx context.Context,
	req *connect.Request[pb.TurnOffGuestModeRequest],
) (*connect.Response[pb.TurnOffGuestModeResponse], error) {
	err := orgServer.orgUseCase.TurnOffGuestMode(ctx, req.Msg.OrgId)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	return connect.NewResponse(&pb.TurnOffGuestModeResponse{
		Success: true,
	}), nil
}

func (orgServer *OrgServer) CreateCognitoUser(
	ctx context.Context,
	req *connect.Request[pb.CreateCognitoUserRequest],
) (*connect.Response[pb.CreateCognitoUserResponse], error) {
	cognitoSub, err := orgServer.orgUseCase.CreateCognitoUser(
		ctx,
		req.Msg.OrgId,
		req.Msg.Username,
		req.Msg.Email,
		req.Msg.Password,
	)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	return connect.NewResponse(&pb.CreateCognitoUserResponse{
		Success:      true,
		CognitoSubId: cognitoSub,
	}), nil
}

// AddToContactBook creates a new contact record in the organization's contact book
func (orgServer *OrgServer) AddToContactBook(
	ctx context.Context,
	req *connect.Request[pb.AddToContactBookRequest],
) (*connect.Response[pb.AddToContactBookResponse], error) {
	contact, err := orgServer.orgUseCase.AddToContactBook(ctx, req.Msg.OrgId, req.Msg.Name, req.Msg.Phone)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.AddToContactBookResponse{
		Contact: contact,
	})
	return response, nil
}

// UpdateContactInContactBook updates an existing contact record in the organization's contact book
func (orgServer *OrgServer) UpdateContactInContactBook(
	ctx context.Context,
	req *connect.Request[pb.UpdateContactInContactBookRequest],
) (*connect.Response[pb.UpdateContactInContactBookResponse], error) {
	contact, err := orgServer.orgUseCase.UpdateContactInContactBook(ctx, req.Msg.Id, req.Msg.Name, req.Msg.Phone)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.UpdateContactInContactBookResponse{
		Contact: contact,
	})
	return response, nil
}

// DeleteFromContactBook deletes a contact record from the organization's contact book
func (orgServer *OrgServer) DeleteFromContactBook(
	ctx context.Context,
	req *connect.Request[pb.DeleteFromContactBookRequest],
) (*connect.Response[pb.DeleteFromContactBookResponse], error) {
	err := orgServer.orgUseCase.DeleteFromContactBook(ctx, req.Msg.Id)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.DeleteFromContactBookResponse{})
	return response, nil
}

// GetContactFromContactBook retrieves a contact record by its ID from the organization's contact book
func (orgServer *OrgServer) GetContactFromContactBook(
	ctx context.Context,
	req *connect.Request[pb.GetContactFromContactBookRequest],
) (*connect.Response[pb.GetContactFromContactBookResponse], error) {
	contact, err := orgServer.orgUseCase.GetContactFromContactBook(ctx, req.Msg.Id)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.GetContactFromContactBookResponse{
		Contact: contact,
	})
	return response, nil
}

// ListContactsInContactBook returns paginated contact records for an organization's contact book
func (orgServer *OrgServer) ListContactsInContactBook(
	ctx context.Context,
	req *connect.Request[pb.ListContactsInContactBookRequest],
) (*connect.Response[pb.ListContactsInContactBookResponse], error) {
	// Set default page size if not provided
	pageSize := req.Msg.PageSize
	if pageSize <= 0 {
		pageSize = 50 // Default page size
	}
	if pageSize > 100 {
		pageSize = 100 // Max page size
	}

	// Get the contacts with pagination
	contacts, nextPageToken, totalCount, err := orgServer.orgUseCase.ListContactsInContactBook(ctx, req.Msg.OrgId, req.Msg.PageToken, pageSize)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}

	response := connect.NewResponse(&pb.ListContactsInContactBookResponse{
		Contacts:      contacts,
		NextPageToken: nextPageToken,
		TotalCount:    totalCount,
	})
	return response, nil
}

// GetContactByPhoneNumber retrieves a contact record by phone number from the organization's contact book
func (orgServer *OrgServer) GetContactByPhoneNumber(
	ctx context.Context,
	req *connect.Request[pb.GetContactByPhoneNumberRequest],
) (*connect.Response[pb.GetContactByPhoneNumberResponse], error) {
	contact, err := orgServer.orgUseCase.GetContactByPhoneNumber(ctx, req.Msg.OrgId, req.Msg.Phone)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.GetContactByPhoneNumberResponse{
		Contact: contact,
	})
	return response, nil
}

// CreatePreRegistrationMapping creates a new pre-registration user mapping
func (orgServer *OrgServer) CreatePreRegistrationMapping(
	ctx context.Context,
	req *connect.Request[pb.CreatePreRegistrationMappingRequest],
) (*connect.Response[pb.CreatePreRegistrationMappingResponse], error) {
	mapping, err := orgServer.orgUseCase.CreatePreRegistrationMapping(
		ctx,
		req.Msg.Email,
		req.Msg.CreatedBy,
		req.Msg.OrgId,
		req.Msg.AssetType,
	)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.CreatePreRegistrationMappingResponse{
		Mapping: mapping,
	})
	return response, nil
}

// CreatePreRegistrationMappings creates multiple pre-registration user mappings atomically
func (orgServer *OrgServer) CreatePreRegistrationMappings(
	ctx context.Context,
	req *connect.Request[pb.CreatePreRegistrationMappingsRequest],
) (*connect.Response[pb.CreatePreRegistrationMappingsResponse], error) {
	mappings, errors, err := orgServer.orgUseCase.CreatePreRegistrationMappings(ctx, req.Msg.Mappings)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.CreatePreRegistrationMappingsResponse{
		Mappings: mappings,
		Errors:   errors,
	})
	return response, nil
}

// GetPreRegistrationMapping retrieves a pre-registration mapping by email and org ID
func (orgServer *OrgServer) GetPreRegistrationMapping(
	ctx context.Context,
	req *connect.Request[pb.GetPreRegistrationMappingRequest],
) (*connect.Response[pb.GetPreRegistrationMappingResponse], error) {
	mapping, err := orgServer.orgUseCase.GetPreRegistrationMapping(ctx, req.Msg.Email, req.Msg.OrgId)
	if err != nil {
		// Check for validation errors (e.g., empty email)
		if err.Error() == "email is required" {
			return nil, connect.NewError(connect.CodeInvalidArgument, err)
		}
		// Check for not found errors
		if strings.Contains(err.Error(), "not found") {
			return nil, connect.NewError(connect.CodeNotFound, err)
		}
		// All other errors are internal server errors
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.GetPreRegistrationMappingResponse{
		Mapping: mapping,
	})
	return response, nil
}

// ListPreRegistrationMappings returns paginated pre-registration mappings for an organization
func (orgServer *OrgServer) ListPreRegistrationMappings(
	ctx context.Context,
	req *connect.Request[pb.ListPreRegistrationMappingsRequest],
) (*connect.Response[pb.ListPreRegistrationMappingsResponse], error) {
	// Set default page size if not provided
	pageSize := req.Msg.PageSize
	if pageSize <= 0 {
		pageSize = 50 // Default page size
	}
	if pageSize > 100 {
		pageSize = 100 // Max page size
	}

	mappings, nextPageToken, totalCount, err := orgServer.orgUseCase.ListPreRegistrationMappings(
		ctx,
		req.Msg.OrgId,
		req.Msg.PageToken,
		pageSize,
		req.Msg.IncludeUsed,
	)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}

	response := connect.NewResponse(&pb.ListPreRegistrationMappingsResponse{
		Mappings:      mappings,
		NextPageToken: nextPageToken,
		TotalCount:    totalCount,
	})
	return response, nil
}

// UpdatePreRegistrationMapping updates an existing pre-registration mapping
func (orgServer *OrgServer) UpdatePreRegistrationMapping(
	ctx context.Context,
	req *connect.Request[pb.UpdatePreRegistrationMappingRequest],
) (*connect.Response[pb.UpdatePreRegistrationMappingResponse], error) {
	mapping, err := orgServer.orgUseCase.UpdatePreRegistrationMapping(
		ctx,
		req.Msg.Id,
		req.Msg.AssetType,
	)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.UpdatePreRegistrationMappingResponse{
		Mapping: mapping,
	})
	return response, nil
}

// DeletePreRegistrationMapping deletes a pre-registration mapping
func (orgServer *OrgServer) DeletePreRegistrationMapping(
	ctx context.Context,
	req *connect.Request[pb.DeletePreRegistrationMappingRequest],
) (*connect.Response[pb.DeletePreRegistrationMappingResponse], error) {
	err := orgServer.orgUseCase.DeletePreRegistrationMapping(ctx, req.Msg.Id)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.DeletePreRegistrationMappingResponse{})
	return response, nil
}

// MarkMappingAsUsed marks a pre-registration mapping as used
func (orgServer *OrgServer) MarkMappingAsUsed(
	ctx context.Context,
	req *connect.Request[pb.MarkMappingAsUsedRequest],
) (*connect.Response[pb.MarkMappingAsUsedResponse], error) {
	err := orgServer.orgUseCase.MarkMappingAsUsed(ctx, req.Msg.MappingId)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&pb.MarkMappingAsUsedResponse{
		Success: true,
	})
	return response, nil
}
