package repository

import (
	"context"
	"database/sql"
	"errors"

	assets "proto/hero/assets/v2"
	pb "proto/hero/orgs/v1"
)

// ErrOrgNotFound is returned when an org cannot be found.
var ErrOrgNotFound = errors.New("org not found")

// AssetInfo represents an asset with its Cognito JWT sub.
type AssetInfo struct {
	ID            string
	CognitoJwtSub string
}

// AssetRepository defines the operations for managing assets.
type OrgRepository interface {
	// CreateOrg stores a new organization.
	CreateOrg(ctx context.Context, transaction *sql.Tx, org *pb.Org) (*pb.Org, error)
	// GetOrg returns an organization by its ID.
	GetOrg(ctx context.Context, transaction *sql.Tx, orgID int32) (*pb.Org, error)
	// ListOrgs returns a list of all organizations.
	ListOrgs(ctx context.Context, transaction *sql.Tx) ([]*pb.Org, error)
	// DeleteOrg physically deletes an organization.
	DeleteOrg(ctx context.Context, transaction *sql.Tx, orgID int32) error
	// GetOrgAPIU<PERSON> retrieves the hashed password for an API user.
	GetOrgAPIUser(ctx context.Context, transaction *sql.Tx, username string) (*pb.OrgApiUser, error)
	// GetOrgAPIUserById retrieves the hashed password for an API user by ID.
	GetOrgAPIUserById(ctx context.Context, transaction *sql.Tx, userId string) (*pb.OrgApiUser, error)
	// UpdateOrg updates an existing organization.
	UpdateOrg(ctx context.Context, transaction *sql.Tx, org *pb.Org) error

	// CreateOrgAPIUser creates a new organization API user.
	CreateOrgAPIUser(ctx context.Context, transaction *sql.Tx, orgID int32, username string, encryptedPassword string, hashedPassword string) error

	// GetZelloChannels returns a list of all Zello channels for an organization.
	GetZelloChannels(ctx context.Context, transaction *sql.Tx) ([]*pb.ZelloChannel, error)
	// CreateZelloChannel creates a new Zello channel for an organization.
	CreateZelloChannel(ctx context.Context, transaction *sql.Tx, orgID int32, zelloChannel *pb.ZelloChannel) error
	// DeleteZelloChannel deletes a Zello channel for an organization.
	DeleteZelloChannel(ctx context.Context, transaction *sql.Tx, orgID int32, zelloChannelIds []string) error

	// GetOrgAssetsWithCognitoJwtSub returns a list of assets with Cognito JWT sub for an organization.
	GetOrgAssetsWithCognitoJwtSub(ctx context.Context, transaction *sql.Tx, orgID int32) ([]*AssetInfo, error)

	// GetZelloCreds returns the Zello credentials for an asset.
	GetZelloCreds(ctx context.Context, transaction *sql.Tx, assetID string) (*assets.ZelloCreds, error)
	// DeleteZelloCreds deletes the Zello credentials for an asset.
	DeleteZelloCreds(ctx context.Context, transaction *sql.Tx, assetID string) error

	// StoreTwilioQueueConfiguration stores a Twilio queue configuration for an organization.
	StoreTwilioQueueConfiguration(ctx context.Context, transaction *sql.Tx, orgID int32, friendlyName string, queueSID string, description string) error

	// InsertOrgQueue inserts a new organization queue record and returns the created record.
	InsertOrgQueue(ctx context.Context, transaction *sql.Tx, req *pb.InsertOrgQueueRequest) (*pb.OrgQueue, error)

	GetTwilioQueueSid(ctx context.Context, transaction *sql.Tx, orgID int32) (string, error)
	DeleteTwilioQueueConfiguration(ctx context.Context, transaction *sql.Tx, orgID int32) error

	// Organization Contact Book Operations
	AddToContactBook(ctx context.Context, transaction *sql.Tx, contact *pb.ContactRecord) error
	UpdateContactInContactBook(ctx context.Context, transaction *sql.Tx, contact *pb.ContactRecord) error
	DeleteFromContactBook(ctx context.Context, transaction *sql.Tx, contactID string) error
	GetContactFromContactBook(ctx context.Context, transaction *sql.Tx, contactID string) (*pb.ContactRecord, error)
	ListContactsInContactBook(ctx context.Context, transaction *sql.Tx, orgID int32, pageToken string, pageSize int32) ([]*pb.ContactRecord, string, int32, error)
	GetContactByPhoneNumber(ctx context.Context, transaction *sql.Tx, orgID int32, phone string) (*pb.ContactRecord, error)

	// Pre-registration user mapping operations
	CreatePreRegistrationMapping(ctx context.Context, transaction *sql.Tx, mapping *pb.PreRegistrationUserMapping) (*pb.PreRegistrationUserMapping, error)
	GetPreRegistrationMapping(ctx context.Context, transaction *sql.Tx, email string, orgID int32) (*pb.PreRegistrationUserMapping, error)
	ListPreRegistrationMappings(ctx context.Context, transaction *sql.Tx, orgID int32, pageToken string, pageSize int32, includeUsed bool) ([]*pb.PreRegistrationUserMapping, string, int32, error)
	UpdatePreRegistrationMapping(ctx context.Context, transaction *sql.Tx, mapping *pb.PreRegistrationUserMapping) (*pb.PreRegistrationUserMapping, error)
	DeletePreRegistrationMapping(ctx context.Context, transaction *sql.Tx, mappingID string) error
	MarkMappingAsUsed(ctx context.Context, transaction *sql.Tx, mappingID string) error
}

// NewOrgRepository returns an OrgRepository based on the provided configuration.
func NewOrgRepository(postgresDB *sql.DB) (OrgRepository, *sql.DB, error) {
	if postgresDB == nil {
		return nil, nil, errors.New("postgresDB is nil")
	}
	return &postgresOrgRepository{db: postgresDB}, postgresDB, nil
}
