package usecase

import (
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"time"

	commonUtils "common/utils"
	assets "proto/hero/assets/v2"
	assetRepository "workflow/internal/assets/data"
	workflowUtils "workflow/internal/common/utils"
	orderRepository "workflow/internal/orders/data"
	situationRepository "workflow/internal/situations/data"

	zello "common/clients/zello"

	_ "modernc.org/sqlite" // required to support transaction for in-memory db

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/kms"
)

const FixedResourceTypeAsset = "ASSET"

// AssetUseCase defines the use-case layer for asset operations.
type AssetUseCase struct {
	database      *sql.DB // Only needed for transactional operations.
	assetRepo     assetRepository.AssetRepository
	situationRepo situationRepository.SituationRepository
	orderRepo     orderRepository.OrderRepository
	kmsClient     *kms.Client
	kmsKeyArn     string
}

// NewAssetUseCase creates a new AssetUseCase.
func NewAssetUseCase(
	database *sql.DB,
	assetRepo assetRepository.AssetRepository,
	situationRepo situationRepository.SituationRepository,
	orderRepo orderRepository.OrderRepository) (*AssetUseCase, error) {
	// For in-memory assetRepository, we need a dummy DB to support transactions.
	if database == nil {
		return nil, errors.New("database is nil: cannot initialize AssetUseCase")
	}
	ctx := context.Background()
	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-west-2"))
	if err != nil {
		log.Fatalf("failed to load AWS config: %v", err)
	}

	kmsKeyArn := os.Getenv("KMS_KEY_ARN")
	if kmsKeyArn == "" {
		kmsKeyArn = "test-key-arn"
	}

	kmsClient := kms.NewFromConfig(cfg)

	return &AssetUseCase{
		database:      database,
		assetRepo:     assetRepo,
		situationRepo: situationRepo,
		orderRepo:     orderRepo,
		kmsClient:     kmsClient,
		kmsKeyArn:     kmsKeyArn,
	}, nil
}

// CreateAsset creates a new asset.
func (assetUseCase *AssetUseCase) CreateAsset(ctx context.Context, assetRecord *assets.Asset) error {
	// Prevent setting is_internal through create API
	if assetRecord.IsInternal {
		return fmt.Errorf("is_internal field cannot be set through create API")
	}

	if assetRecord.OrgId == 0 {
		return fmt.Errorf("organization ID is required")
	}
	if assetRecord.AdditionalInfoJson == "" {
		assetRecord.AdditionalInfoJson = "{}"
	}
	if assetRecord.ContactEmail != "" && !commonUtils.IsValidEmail(assetRecord.ContactEmail) {
		return fmt.Errorf("invalid email format")
	}
	if assetRecord.ContactNo != "" {
		standardized, err := commonUtils.StandardizeUSPhoneNumber(assetRecord.ContactNo)
		if err != nil {
			return err
		}
		assetRecord.ContactNo = standardized
	}
	assetRecord.ResourceType = FixedResourceTypeAsset

	// No transaction is needed for a simple insert.
	return assetUseCase.assetRepo.CreateAsset(ctx, nil, assetRecord)
}

// GetZelloChannels returns the list of Zello channels for the organization.
func (assetUseCase *AssetUseCase) GetZelloChannels(ctx context.Context) ([]*assets.ZelloChannel, error) {
	return assetUseCase.assetRepo.GetZelloChannels(ctx, nil)
}

// CreateResponderAsset creates the asset and registers the responder with Zello,
// then encrypts and stores the Zello credentials using AWS KMS.
func (assetUseCase *AssetUseCase) CreateResponderAsset(ctx context.Context, a *assets.Asset) error {
	if a.OrgId == 0 {
		return fmt.Errorf("organization ID is required")
	}
	if a.ContactNo != "" {
		standardized, err := commonUtils.StandardizeUSPhoneNumber(a.ContactNo)
		if err != nil {
			return fmt.Errorf("CreateResponder: invalid phone number: %w", err)
		}
		a.ContactNo = standardized
	}

	err := assetUseCase.CreateAsset(ctx, a)
	if err != nil {
		return fmt.Errorf("CreateResponder: error creating asset: %w", err)
	}

	// get the zello channels for the organization
	zelloChannels, err := assetUseCase.GetZelloChannels(ctx)
	if err != nil || len(zelloChannels) == 0 {
		return fmt.Errorf("CreateResponder: error getting Zello channels: %w", err)
	}
	orgZelloChannel := ""
	for _, channel := range zelloChannels {
		if channel.OrgId == a.OrgId {
			orgZelloChannel = channel.ZelloChannelId
			break
		}
	}
	// if there is a zello channel for this org, create zello creds
	// for this asset and add the asset to the zello channel
	if orgZelloChannel != "" {
		zelloClient, err := zello.NewZelloClient()
		if err != nil {
			return fmt.Errorf("CreateResponder: error creating Zello client: %w", err)
		}
		defer func() {
			if err := zelloClient.Logout(); err != nil {
				fmt.Printf("could not logout Zello client: %v", err)
			}
		}()

		zelloUsername := a.Name // for now, use name (switch to autogen id once we have our own mobile app )
		_, password, err := zelloClient.CreateUser(zelloUsername)
		if err != nil {
			return fmt.Errorf("CreateResponder: error creating Zello user: %w", err)
		}

		// remove the user from the default channel
		_, err = zelloClient.RemoveFromChannel("Everyone", []string{zelloUsername})
		if err != nil {
			return fmt.Errorf("CreateResponder: error removing user from default channel: %w", err)
		}
		// TODO - for some VERY inexplicable reason, AddToChannels works locally but not in AWS
		// so we're just going to add the user to the first channel for now
		_, err = zelloClient.AddToChannel(orgZelloChannel, []string{zelloUsername})
		if err != nil {
			return fmt.Errorf("CreateResponder: error adding user to default channel: %w", err)
		}

		// Encrypt the Zello password using AWS KMS
		input := &kms.EncryptInput{
			KeyId:     aws.String(assetUseCase.kmsKeyArn),
			Plaintext: []byte(password),
		}
		result, err := assetUseCase.kmsClient.Encrypt(ctx, input)
		if err != nil {
			return fmt.Errorf("failed to encrypt data: %v", err)
		}
		// Save the encrypted Zello credentials using base64 encoding
		err = assetUseCase.assetRepo.CreateZelloCreds(ctx, nil, &assets.ZelloCreds{
			OrgId:             a.OrgId,
			AssetId:           a.Id,
			Username:          zelloUsername,
			EncryptedPassword: base64.StdEncoding.EncodeToString(result.CiphertextBlob),
		})
		if err != nil {
			return fmt.Errorf("CreateResponder: error saving Zello creds: %w", err)
		}
	}

	return nil
}

// GetAsset retrieves an asset by its ID.
func (assetUseCase *AssetUseCase) GetAsset(ctx context.Context, assetID string) (*assets.Asset, error) {
	return assetUseCase.assetRepo.GetAsset(ctx, nil, assetID)
}

// GetAssetPrivate retrieves the asset and its associated encrypted Zello credentials,
// then decrypts the password using AWS KMS.
func (assetUseCase *AssetUseCase) GetAssetPrivate(ctx context.Context, assetId string) (*assets.GetAssetPrivateResponse, error) {
	// Retrieve asset from the repository
	asset, err := assetUseCase.assetRepo.GetAsset(ctx, nil, assetId)
	if err != nil {
		return nil, err
	}

	// Load the Zello credentials
	zelloCreds, err := assetUseCase.assetRepo.GetZelloCreds(ctx, nil, assetId)
	if err != nil {
		return nil, err
	}

	// Decrypt the Zello credentials
	ciphertextBlob, err := base64.StdEncoding.DecodeString(zelloCreds.EncryptedPassword)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64 encrypted password: %v", err)
	}
	input := &kms.DecryptInput{
		CiphertextBlob: ciphertextBlob,
	}
	result, err := assetUseCase.kmsClient.Decrypt(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt data: %v", err)
	}

	// Update the Zello credentials with the decrypted password
	zelloCreds.Password = string(result.Plaintext)
	zelloCreds.EncryptedPassword = "" // don't return the encrypted password

	return &assets.GetAssetPrivateResponse{
		Asset:      asset,
		ZelloCreds: zelloCreds,
	}, nil
}

// GetAssetByCognitoSub retrieves an asset by its Cognito JWT sub.
func (assetUseCase *AssetUseCase) GetAssetByCognitoSub(ctx context.Context, cognitoSub string) (*assets.Asset, error) {
	return assetUseCase.assetRepo.GetAssetByCognitoSub(ctx, nil, cognitoSub)
}

// DeleteAsset deletes an asset.
func (assetUseCase *AssetUseCase) DeleteAsset(ctx context.Context, assetID string) error {
	return assetUseCase.assetRepo.DeleteAsset(ctx, nil, assetID)
}

// ListAssets returns a paginated list of assets with optional filtering and ordering.
func (assetUseCase *AssetUseCase) ListAssets(ctx context.Context, pageSize int, pageToken string, assetType assets.AssetType, assetStatus assets.AssetStatus, orderBy string) ([]*assets.Asset, string, error) {
	if pageSize <= 0 {
		pageSize = 100
	}
	paginatedAssets, err := assetUseCase.assetRepo.ListAssets(ctx, nil, pageSize, pageToken, assetType, assetStatus, orderBy)
	if err != nil {
		return nil, "", err
	}
	return paginatedAssets.Assets, paginatedAssets.NextPageToken, nil
}

// UpdateAsset updates only the fields that are provided (non‑default) in updatedAsset.
// For scalar fields, a non‑zero (or non-empty) value is considered present.
// This function uses a transaction so that if any step fails, all changes are rolled back.
func (assetUseCase *AssetUseCase) UpdateAsset(ctx context.Context, updatedAsset *assets.Asset) (*assets.Asset, error) {
	// Begin a transaction.
	transaction, beginError := assetUseCase.database.BeginTx(ctx, nil)
	if beginError != nil {
		return nil, beginError
	}

	// Retrieve the existing asset within the transaction.
	existingAsset, retrievalError := assetUseCase.assetRepo.GetAsset(ctx, transaction, updatedAsset.Id)
	if retrievalError != nil {
		_ = transaction.Rollback()
		return nil, retrievalError
	}

	// Store the original is_internal value to prevent modification
	originalIsInternal := existingAsset.IsInternal

	// Update only fields that are "present" (non‑default) in updatedAsset.
	if updatedAsset.Name != "" {
		existingAsset.Name = updatedAsset.Name
	}
	if updatedAsset.CognitoJwtSub != "" {
		existingAsset.CognitoJwtSub = updatedAsset.CognitoJwtSub
	}
	if updatedAsset.Type != assets.AssetType_ASSET_TYPE_UNSPECIFIED {
		existingAsset.Type = updatedAsset.Type
	}
	if updatedAsset.Status != assets.AssetStatus_ASSET_STATUS_UNSPECIFIED {
		existingAsset.Status = updatedAsset.Status
	}
	if updatedAsset.ContactNo != "" {
		standardized, err := commonUtils.StandardizeUSPhoneNumber(updatedAsset.ContactNo)
		if err != nil {
			_ = transaction.Rollback()
			return nil, err
		}
		existingAsset.ContactNo = standardized
	}
	if updatedAsset.ContactEmail != "" {
		if !commonUtils.IsValidEmail(updatedAsset.ContactEmail) {
			_ = transaction.Rollback()
			return nil, fmt.Errorf("invalid email format")
		}
		existingAsset.ContactEmail = updatedAsset.ContactEmail
	}
	if updatedAsset.Latitude != 0 {
		existingAsset.Latitude = updatedAsset.Latitude
	}
	if updatedAsset.Longitude != 0 {
		existingAsset.Longitude = updatedAsset.Longitude
	}
	if updatedAsset.LocationUpdateTime != "" {
		existingAsset.LocationUpdateTime = updatedAsset.LocationUpdateTime
	}
	if updatedAsset.AdditionalInfoJson != "" {
		existingAsset.AdditionalInfoJson = updatedAsset.AdditionalInfoJson
	}
	if updatedAsset.ResourceType != "" && updatedAsset.ResourceType != existingAsset.ResourceType {
		_ = transaction.Rollback()
		return nil, fmt.Errorf("resource type cannot be modified after creation")
	}

	// Always update the update time.
	existingAsset.UpdateTime = commonUtils.TimeToISO8601String(time.Now())

	// Ensure is_internal is preserved from the original value
	existingAsset.IsInternal = originalIsInternal

	// Persist the updated asset within the transaction.
	updatedAsset, updateError := assetUseCase.assetRepo.UpdateAsset(ctx, transaction, existingAsset)
	if updateError != nil {
		_ = transaction.Rollback()
		return nil, updateError
	}

	// Commit the transaction.
	if commitError := transaction.Commit(); commitError != nil {
		return nil, commitError
	}

	return updatedAsset, nil
}

// AddAdditionalInfo adds additional JSON info to an asset by merging the provided JSON.
func (assetUseCase *AssetUseCase) AddAdditionalInfo(ctx context.Context, id string, newInfo string) (string, string, error) {
	// Begin a transaction.
	tx, err := assetUseCase.database.BeginTx(ctx, nil)
	if err != nil {
		return "", "", err
	}

	// Retrieve the existing asset within the transaction.
	assetRecord, err := assetUseCase.assetRepo.GetAsset(ctx, tx, id)
	if err != nil {
		_ = tx.Rollback()
		return "", "", err
	}

	// Unmarshal the existing additional info.
	var existingMap map[string]interface{}
	if err = json.Unmarshal([]byte(assetRecord.AdditionalInfoJson), &existingMap); err != nil {
		_ = tx.Rollback()
		return "", "", fmt.Errorf("failed to parse existing additional info: %w", err)
	}

	// Unmarshal the new additional info.
	var newMap map[string]interface{}
	if err = json.Unmarshal([]byte(newInfo), &newMap); err != nil {
		_ = tx.Rollback()
		return "", "", fmt.Errorf("failed to parse new additional info: %w", err)
	}

	// Merge newMap into existingMap.
	workflowUtils.MergeJSON(existingMap, newMap)

	// Marshal the merged map back to JSON.
	mergedBytes, err := json.Marshal(existingMap)
	if err != nil {
		_ = tx.Rollback()
		return "", "", fmt.Errorf("failed to marshal merged additional info: %w", err)
	}
	mergedInfo := string(mergedBytes)

	// Use the new repository method to update only the additional info.
	_, err = assetUseCase.assetRepo.UpdateAdditionalInfoJSON(ctx, tx, id, mergedInfo)
	if err != nil {
		_ = tx.Rollback()
		return "", "", err
	}

	// Commit the transaction.
	if err := tx.Commit(); err != nil {
		return "", "", err
	}

	return id, mergedInfo, nil
}

// SetAssetInternalStatus updates only the isInternal flag of an asset.
func (assetUseCase *AssetUseCase) SetAssetInternalStatus(ctx context.Context, assetID string, isInternal bool) (*assets.Asset, error) {
	tx, err := assetUseCase.database.BeginTx(ctx, nil)
	if err != nil {
		return nil, err
	}

	err = assetUseCase.assetRepo.SetAssetInternalStatus(ctx, tx, assetID, isInternal)
	if err != nil {
		_ = tx.Rollback()
		return nil, err
	}

	updatedAsset, err := assetUseCase.assetRepo.GetAsset(ctx, tx, assetID)
	if err != nil {
		_ = tx.Rollback()
		return nil, err
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}

	return updatedAsset, nil
}

// ListAssetsByPhoneNumber returns all assets associated with a phone number.
func (assetUseCase *AssetUseCase) ListAssetsByPhoneNumber(ctx context.Context, phoneNumber string) ([]*assets.Asset, error) {
	return assetUseCase.assetRepo.ListAssetsByPhoneNumber(ctx, nil, phoneNumber)
}

// SearchAssets performs advanced search on assets with comprehensive filtering and search capabilities.
// This method provides a high-level interface for asset search functionality, supporting:
// - Text search across multiple fields (id, name, contact_no, contact_email)
// - Exact filtering by asset type and status
// - Date range filtering on all timestamp fields
// - Geographic bounding box searches
// - Configurable ordering and pagination
// - Search result highlighting
//
// The method validates input parameters and delegates to the repository layer for execution.
// No transaction is needed for read-only search operations.
func (assetUseCase *AssetUseCase) SearchAssets(ctx context.Context, searchRequest *assets.SearchAssetsRequest) (*assets.SearchAssetsResponse, error) {
	// Validate search request parameters
	if searchRequest == nil {
		return nil, fmt.Errorf("search request cannot be nil")
	}

	// Set default page size if not specified or invalid
	if searchRequest.PageSize <= 0 {
		searchRequest.PageSize = 20 // Default page size for reasonable response times
	}
	if searchRequest.PageSize > 100 {
		searchRequest.PageSize = 100 // Maximum page size to prevent resource exhaustion
	}

	// Delegate to repository layer for search execution
	// No transaction needed for read-only search operations
	return assetUseCase.assetRepo.SearchAssets(ctx, nil, searchRequest)
}
