package connect

import (
	"context"
	// "errors"

	// "middleware"

	commonUtils "common/utils"
	assets "proto/hero/assets/v2"
	"workflow/internal/assets/usecase"

	"fmt"

	"connectrpc.com/connect"
)

// AssetServer implements the asset service.
type AssetServer struct {
	assetUseCase *usecase.AssetUseCase
}

// NewAssetServer creates a new AssetServer.
func NewAssetServer(assetUseCase *usecase.AssetUseCase) *AssetServer {
	return &AssetServer{
		assetUseCase: assetUseCase,
	}
}

// CreateAsset creates a new asset.
func (assetServer *AssetServer) CreateAsset(
	ctx context.Context,
	createAssetRequest *connect.Request[assets.CreateAssetRequest],
) (*connect.Response[assets.CreateAssetResponse], error) {
	err := assetServer.assetUseCase.CreateAsset(ctx, createAssetRequest.Msg.Asset)
	if err != nil {
		// Check for validation errors
		if err == commonUtils.ErrEmptyPhoneNumber || err == commonUtils.ErrInvalidUSPhoneNumber {
			return nil, connect.NewError(connect.CodeInvalidArgument, err)
		}
		// Check for is_internal validation error
		if err.Error() == "is_internal field cannot be set through create API" {
			return nil, connect.NewError(connect.CodeInvalidArgument, err)
		}
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&assets.CreateAssetResponse{
		Asset: createAssetRequest.Msg.Asset,
	})
	return response, nil
}

// GetAsset retrieves an asset by its ID.
func (assetServer *AssetServer) GetAsset(
	ctx context.Context,
	getAssetRequest *connect.Request[assets.GetAssetRequest],
) (*connect.Response[assets.GetAssetResponse], error) {
	assetRecord, err := assetServer.assetUseCase.GetAsset(ctx, getAssetRequest.Msg.Id)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&assets.GetAssetResponse{
		Asset: assetRecord,
	})
	return response, nil
}

// TODO - put this back in after figuring out how to handle local dev
// func (assetServer *AssetServer) isOwner(ctx context.Context, requestedId string) bool {
// 	username := middleware.GetUsername(ctx)
// 	// in local dev, username is undefined
// 	// what can we do about this?
// 	requester, err := assetServer.assetUseCase.GetAssetByCognitoSub(ctx, username)
// 	if err != nil {
// 		return false
// 	}
// 	return requester.Id == requestedId
// }

func (assetServer *AssetServer) GetZelloChannels(
	ctx context.Context,
	req *connect.Request[assets.GetZelloChannelsRequest],
) (*connect.Response[assets.GetZelloChannelsResponse], error) {
	zelloChannels, err := assetServer.assetUseCase.GetZelloChannels(ctx)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&assets.GetZelloChannelsResponse{
		ZelloChannels: zelloChannels,
	})
	return response, nil
}

func (assetServer *AssetServer) GetAssetPrivate(
	ctx context.Context,
	req *connect.Request[assets.GetAssetPrivateRequest],
) (*connect.Response[assets.GetAssetPrivateResponse], error) {
	// if !assetServer.isOwner(ctx, req.Msg.AssetId) {
	// // if middleware.isOwner(ctx, req.Msg.AssetId)
	// 	return nil, connect.NewError(connect.CodePermissionDenied, errors.New("user does not have permission to access this asset"))
	// }

	assetResponse, err := assetServer.assetUseCase.GetAssetPrivate(ctx, req.Msg.AssetId)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}

	res := connect.NewResponse(assetResponse)
	return res, nil
}

func (assetServer *AssetServer) CreateResponderAsset(
	ctx context.Context,
	req *connect.Request[assets.CreateAssetRequest],
) (*connect.Response[assets.CreateAssetResponse], error) {
	err := assetServer.assetUseCase.CreateResponderAsset(ctx, req.Msg.Asset)
	if err != nil {
		// Check for validation errors
		if err == commonUtils.ErrEmptyPhoneNumber || err == commonUtils.ErrInvalidUSPhoneNumber {
			return nil, connect.NewError(connect.CodeInvalidArgument, err)
		}
		// Check for is_internal validation error
		if err.Error() == "is_internal field cannot be set through create API" {
			return nil, connect.NewError(connect.CodeInvalidArgument, err)
		}
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	res := connect.NewResponse(&assets.CreateAssetResponse{
		Asset: req.Msg.Asset,
	})
	return res, nil
}

// GetAssetByCognitoSub retrieves an asset by its Cognito JWT sub.
func (assetServer *AssetServer) GetAssetByCognitoSub(
	ctx context.Context,
	getAssetByCognitoSubRequest *connect.Request[assets.GetAssetByCognitoSubRequest],
) (*connect.Response[assets.GetAssetByCognitoSubResponse], error) {
	assetRecord, err := assetServer.assetUseCase.GetAssetByCognitoSub(ctx, getAssetByCognitoSubRequest.Msg.CognitoJwtSub)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&assets.GetAssetByCognitoSubResponse{
		Asset: assetRecord,
	})
	return response, nil
}

// DeleteAsset deletes an asset.
func (assetServer *AssetServer) DeleteAsset(
	ctx context.Context,
	deleteAssetRequest *connect.Request[assets.DeleteAssetRequest],
) (*connect.Response[assets.DeleteAssetResponse], error) {
	err := assetServer.assetUseCase.DeleteAsset(ctx, deleteAssetRequest.Msg.Id)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&assets.DeleteAssetResponse{})
	return response, nil
}

// ListAssets returns a paginated list of assets.
func (assetServer *AssetServer) ListAssets(
	ctx context.Context,
	listAssetsRequest *connect.Request[assets.ListAssetsRequest],
) (*connect.Response[assets.ListAssetsResponse], error) {
	assetList, nextPageToken, err := assetServer.assetUseCase.ListAssets(
		ctx,
		int(listAssetsRequest.Msg.PageSize),
		listAssetsRequest.Msg.PageToken,
		listAssetsRequest.Msg.Type,
		listAssetsRequest.Msg.Status,
		listAssetsRequest.Msg.OrderBy,
	)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&assets.ListAssetsResponse{
		Assets:        assetList,
		NextPageToken: nextPageToken,
	})
	return response, nil
}

// UpdateAsset updates the provided asset fields.
func (assetServer *AssetServer) UpdateAsset(
	ctx context.Context,
	updateAssetRequest *connect.Request[assets.UpdateAssetRequest],
) (*connect.Response[assets.UpdateAssetResponse], error) {
	updatedAsset, err := assetServer.assetUseCase.UpdateAsset(ctx, updateAssetRequest.Msg.Asset)
	if err != nil {
		// Check for validation errors
		if err == commonUtils.ErrEmptyPhoneNumber || err == commonUtils.ErrInvalidUSPhoneNumber {
			return nil, connect.NewError(connect.CodeInvalidArgument, err)
		}
		// Check for is_internal validation error
		if err.Error() == "is_internal field cannot be modified through update API" {
			return nil, connect.NewError(connect.CodeInvalidArgument, err)
		}
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&assets.UpdateAssetResponse{
		Asset: updatedAsset,
	})
	return response, nil
}

// AddAdditionalInfo adds additional JSON info to an asset by merging the provided JSON.
func (assetServer *AssetServer) AddAdditionalInfo(
	ctx context.Context,
	additionalInfoRequest *connect.Request[assets.AddAdditionalInfoRequest],
) (*connect.Response[assets.AddAdditionalInfoResponse], error) {
	id, updatedJSON, err := assetServer.assetUseCase.AddAdditionalInfo(ctx, additionalInfoRequest.Msg.Id, additionalInfoRequest.Msg.AdditionalInfoJson)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&assets.AddAdditionalInfoResponse{
		Id:                 id,
		AdditionalInfoJson: updatedJSON,
	})
	return response, nil
}

// ListAssetsByPhoneNumber returns all assets associated with a phone number.
func (assetServer *AssetServer) ListAssetsByPhoneNumber(
	ctx context.Context,
	req *connect.Request[assets.ListAssetsByPhoneNumberRequest],
) (*connect.Response[assets.ListAssetsByPhoneNumberResponse], error) {
	assetRecords, err := assetServer.assetUseCase.ListAssetsByPhoneNumber(ctx, req.Msg.PhoneNumber)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&assets.ListAssetsByPhoneNumberResponse{
		Assets: assetRecords,
	})
	return response, nil
}

// SearchAssets performs advanced search on assets with comprehensive filtering capabilities.
// This endpoint provides powerful search functionality including:
// - Text search across multiple asset fields
// - Exact filtering by type and status
// - Date range filtering on timestamps
// - Geographic bounding box searches
// - Configurable ordering and pagination
// - Search result highlighting for UI enhancement
//
// The endpoint validates the search request and returns structured results with pagination metadata.
func (assetServer *AssetServer) SearchAssets(
	ctx context.Context,
	searchRequest *connect.Request[assets.SearchAssetsRequest],
) (*connect.Response[assets.SearchAssetsResponse], error) {
	// Validate that the search request is not nil
	if searchRequest.Msg == nil {
		return nil, connect.NewError(connect.CodeInvalidArgument, fmt.Errorf("search request cannot be empty"))
	}

	// Execute the search through the use case layer
	searchResponse, err := assetServer.assetUseCase.SearchAssets(ctx, searchRequest.Msg)
	if err != nil {
		// Return appropriate error codes based on error type
		if err.Error() == "search request cannot be nil" {
			return nil, connect.NewError(connect.CodeInvalidArgument, err)
		}
		return nil, connect.NewError(connect.CodeInternal, err)
	}

	// Return the successful search response
	response := connect.NewResponse(searchResponse)
	return response, nil
}

// SetAssetInternalStatus updates only the isInternal flag of an asset.
func (assetServer *AssetServer) SetAssetInternalStatus(
	ctx context.Context,
	req *connect.Request[assets.SetAssetInternalStatusRequest],
) (*connect.Response[assets.SetAssetInternalStatusResponse], error) {
	updatedAsset, err := assetServer.assetUseCase.SetAssetInternalStatus(ctx, req.Msg.Id, req.Msg.IsInternal)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	response := connect.NewResponse(&assets.SetAssetInternalStatusResponse{
		Asset: updatedAsset,
	})
	return response, nil
}
