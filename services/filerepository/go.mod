module filerepository

go 1.23.8

require common v0.0.0-00010101000000-000000000000

require (
	github.com/aws/aws-sdk-go-v2/aws/protocol/eventstream v1.6.7 // indirect
	github.com/aws/aws-sdk-go-v2/credentials v1.17.47 // indirect
	github.com/aws/aws-sdk-go-v2/feature/ec2/imds v1.16.21 // indirect
	github.com/aws/aws-sdk-go-v2/internal/configsources v1.3.25 // indirect
	github.com/aws/aws-sdk-go-v2/internal/endpoints/v2 v2.6.25 // indirect
	github.com/aws/aws-sdk-go-v2/internal/ini v1.8.1 // indirect
	github.com/aws/aws-sdk-go-v2/internal/v4a v1.3.25 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/accept-encoding v1.12.1 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/checksum v1.4.6 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/presigned-url v1.12.6 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/s3shared v1.18.6 // indirect
	github.com/aws/aws-sdk-go-v2/service/secretsmanager v1.24.1 // indirect
	github.com/aws/aws-sdk-go-v2/service/sso v1.24.7 // indirect
	github.com/aws/aws-sdk-go-v2/service/ssooidc v1.28.6 // indirect
	github.com/aws/aws-sdk-go-v2/service/sts v1.33.2 // indirect
	github.com/aws/smithy-go v1.22.2 // indirect
	github.com/getsentry/sentry-go v0.34.0 // indirect
	golang.org/x/sys v0.31.0 // indirect
)

require (
	github.com/aws/aws-sdk-go-v2 v1.36.3
	github.com/aws/aws-sdk-go-v2/config v1.28.6
	github.com/aws/aws-sdk-go-v2/service/s3 v1.71.0
	github.com/google/uuid v1.6.0
	github.com/sony/gobreaker v1.0.0
	golang.org/x/net v0.38.0 // indirect
	google.golang.org/protobuf v1.36.6
)

require (
	connectrpc.com/connect v1.18.1
	connectrpc.com/grpchealth v1.3.0 // indirect
	connectrpc.com/grpcreflect v1.3.0 // indirect
	github.com/golang-jwt/jwt/v4 v4.5.1 // indirect
	github.com/lib/pq v1.10.9
	github.com/rs/cors v1.11.1 // indirect
	golang.org/x/text v0.23.0 // indirect
	proto v0.0.0-00010101000000-000000000000
)

replace proto => ../../lib/proto

replace common => ../../lib/common
