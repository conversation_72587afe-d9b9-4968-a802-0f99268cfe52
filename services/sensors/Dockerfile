# Build stage
ARG AWS_ACCOUNT_ID=************
ARG AWS_REGION=us-west-2

FROM ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/hero-core/sensors-build-base-service:latest AS builder

# Build the Go application
WORKDIR /build

# keep the same directory structure as the repo itself,
# which makes the relative import for the proto library work
COPY lib/proto /lib/proto
COPY lib/common /lib/common
WORKDIR /services/sensors

# Copy the Go modules files and download dependencies
COPY services/sensors/go.mod services/sensors/go.sum ./
RUN go mod download

# Copy the application code
COPY services/sensors .

# Build the Go binary with CGO enabled
ENV CGO_ENABLED=1
RUN go build -o server ./cmd/server

# Final stage
FROM ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/hero-core/sensors-runtime-base-service:latest

# Copy the built plugins from builder
COPY --from=builder /opt/kvs-webrtc-sdk/build/libkvsWebrtcClient.so /usr/local/lib/
COPY --from=builder /opt/kvs-webrtc-sdk/build/libkvsWebrtcSignalingClient.so /usr/local/lib/
COPY --from=builder /opt/gst-plugins-rs/target/release/ /opt/gst-plugins-rs/target/release/

# Copy entrypoint script
COPY infra/edge/artifacts/CameraStreamer/1.0.0/entrypoint.sh /opt/entrypoint.sh
RUN chmod +x /opt/entrypoint.sh

# Set working directory
WORKDIR /app

# Copy the Go binary from the builder
COPY --from=builder /services/sensors/server .

# Expose the port the app runs on
EXPOSE 8080

# Add NET_ADMIN capability
LABEL io.hero.capabilities=NET_ADMIN

# Set the entrypoint and default command
ENTRYPOINT [ "/opt/entrypoint.sh" ]
CMD ["./server"]
