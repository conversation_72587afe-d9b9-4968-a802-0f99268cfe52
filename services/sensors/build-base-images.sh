#!/bin/bash
set -e

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Get AWS account ID and region from profile
AWS_ACCOUNT_ID=************
AWS_REGION=${AWS_REGION:-us-west-2}
ECR_URL="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"

# Function to check if repository exists
check_repo_exists() {
    local repo_name=$1
    aws ecr describe-repositories --repository-names "$repo_name" --region "$AWS_REGION" >/dev/null 2>&1
    return $?
}

# Function to create repository
create_repo() {
    local repo_name=$1
    echo "Creating ECR repository: $repo_name"
    aws ecr create-repository \
        --repository-name "$repo_name" \
        --region "$AWS_REGION" \
        --image-scanning-configuration scanOnPush=true \
        --encryption-configuration encryptionType=AES256
}

# Function to get ECR login token and login using SSO
login_to_ecr() {
    echo "Logging in to Amazon ECR using SSO..."
    # Get SSO token
    aws sso login --profile SWE-me

    # Get ECR token using SSO credentials
    aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${ECR_URL}
}

# Function to build and push an image
build_and_push() {
    local image_name=$1
    local dockerfile=$2
    local repo_name="hero-core/${image_name}"
    local full_image_name="${ECR_URL}/${repo_name}:latest"
    
    echo "Building ${image_name} image..."
    docker buildx build \
        --platform linux/amd64,linux/arm64 \
        --push \
        -t ${full_image_name} \
        -f "$SCRIPT_DIR/${dockerfile}" \
        "$SCRIPT_DIR"
}

# Login to ECR using SSO
login_to_ecr

# Build and push base images
build_and_push "sensors-build-base-service" "Dockerfile.build-base" &
build_and_push "sensors-runtime-base-service" "Dockerfile.runtime-base" &
wait

echo "Base images built and pushed successfully!" 