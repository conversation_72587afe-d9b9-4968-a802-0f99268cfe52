-- +goose Up
-- +goose StatementBegin
CREATE TABLE pre_registration_user_mappings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL,
    org_id INTEGER NOT NULL REFERENCES orgs(id) ON DELETE CASCADE,
    asset_type_label VARCHAR(100) NOT NULL,
    asset_type INTEGER DEFAULT 3, -- Default to ASSET_TYPE_RESPONDER
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    used_at TIMESTAMPTZ NULL,
    created_by VARCHAR(255) NOT NULL DEFAULT 'system',
    UNIQUE(email, org_id)
);
-- +goose StatementEnd

-- +goose StatementBegin
-- Enable row level security
ALTER TABLE pre_registration_user_mappings ENABLE ROW LEVEL SECURITY;

-- Create policy for organization access
CREATE POLICY "pre_registration_user_mappings_organization_access" ON pre_registration_user_mappings
    USING (org_id::text = current_setting('app.org_id'))
    WITH CHECK (org_id::text = current_setting('app.org_id'));
-- +goose StatementEnd

-- +goose StatementBegin
-- Create indexes for efficient lookups
CREATE INDEX pre_registration_user_mappings_email_org_idx ON pre_registration_user_mappings (email, org_id);
CREATE INDEX pre_registration_user_mappings_org_id_idx ON pre_registration_user_mappings (org_id);
CREATE INDEX pre_registration_user_mappings_used_at_idx ON pre_registration_user_mappings (used_at);
CREATE INDEX pre_registration_user_mappings_created_at_idx ON pre_registration_user_mappings (created_at);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS pre_registration_user_mappings;
-- +goose StatementEnd
