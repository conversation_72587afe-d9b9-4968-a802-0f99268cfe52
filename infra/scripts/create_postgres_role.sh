#!/bin/bash

# Exit on error
set -e

# Check if required arguments are provided
if [ "$#" -lt 2 ]; then
    echo "Usage: $0 <admin-secret-arn> <role-secret-arn>"
    echo "Example: $0 arn:aws:secretsmanager:us-west-2:123456789012:secret:myapp/db-admin-creds arn:aws:secretsmanager:us-west-2:123456789012:secret:myapp/db-role-creds"
    exit 1
fi

ADMIN_SECRET_NAME=$1
ROLE_SECRET_NAME=$2

# Extract region from ARN
AWS_REGION=$(echo "$ADMIN_SECRET_NAME" | cut -d: -f4)

# Get the admin credentials from AWS Secrets Manager
echo "Retrieving admin credentials from AWS Secrets Manager..."
ADMIN_SECRET_VALUE=$(aws secretsmanager get-secret-value \
    --secret-id "$ADMIN_SECRET_NAME" \
    --region "$AWS_REGION" \
    --query 'SecretString' \
    --output text)

# Extract admin credentials from the secret
ADMIN_USERNAME=$(echo "$ADMIN_SECRET_VALUE" | jq -r '.username')
ADMIN_PASSWORD=$(echo "$ADMIN_SECRET_VALUE" | jq -r '.password')
DB_HOST=$(echo "$ADMIN_SECRET_VALUE" | jq -r '.host')
DB_PORT=$(echo "$ADMIN_SECRET_VALUE" | jq -r '.port')
DB_NAME=$(echo "$ADMIN_SECRET_VALUE" | jq -r '.dbname')

if [ -z "$ADMIN_USERNAME" ] || [ -z "$ADMIN_PASSWORD" ] || [ -z "$DB_HOST" ] || [ -z "$DB_PORT" ]; then
    echo "Error: Could not extract all required admin credentials (username, password, host, port) from secret"
    exit 1
fi

# Get the new role credentials from AWS Secrets Manager
echo "Retrieving new role credentials from AWS Secrets Manager..."
ROLE_SECRET_VALUE=$(aws secretsmanager get-secret-value \
    --secret-id "$ROLE_SECRET_NAME" \
    --region "$AWS_REGION" \
    --query 'SecretString' \
    --output text)

# Extract username and password for the new role
ROLE_USERNAME=$(echo "$ROLE_SECRET_VALUE" | jq -r '.username')
ROLE_PASSWORD=$(echo "$ROLE_SECRET_VALUE" | jq -r '.password')

if [ -z "$ROLE_USERNAME" ] || [ -z "$ROLE_PASSWORD" ]; then
    echo "Error: Could not extract username or password for new role from secret"
    exit 1
fi

# Create a temporary SQL file with the new role's values
echo "Creating SQL file with new role credentials..."
sed "s/{{username}}/$ROLE_USERNAME/g; s/{{password}}/$ROLE_PASSWORD/g; s/{{database}}/$DB_NAME/g" create-db-role.sql.template > create-db-role.sql

# Construct connection string
DB_CONNECTION_STRING="postgresql://${ADMIN_USERNAME}:${ADMIN_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"

# Execute the SQL file
echo "Creating Postgres role..."
psql "${DB_CONNECTION_STRING}" -f create-db-role.sql

# Clean up
rm create-db-role.sql

echo "Postgres role created successfully!"