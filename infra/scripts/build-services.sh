#!/bin/bash
set -e

TAG=temp

# Loop through each folder in the ./services directory
for service in ./services/*; do
    if [ -d "$service" ]; then
        service_name=$(basename "$service")
        # Skip services listed in SERVICES_TO_SKIP
        if [[ -n "$SERVICES_TO_SKIP" && "$SERVICES_TO_SKIP" == *"$service_name"* ]]; then
            echo "Skipping $service_name service"
            continue
        fi
        repo_name=${service_name}-service
        docker build -t ${repo_name}:$TAG -f ${service}/Dockerfile .
        docker tag ${repo_name}:$TAG 099048140956.dkr.ecr.us-west-2.amazonaws.com/${repo_name}:$TAG
    fi
done
