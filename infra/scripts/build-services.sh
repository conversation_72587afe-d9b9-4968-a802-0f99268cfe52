#!/bin/bash
set -e

TAG=temp

build_service() {
    service_path=$1
    service_name=$(basename "$service_path")

    # Skip services listed in SERVICES_TO_SKIP
    if [[ -n "$SERVICES_TO_SKIP" && "$SERVICES_TO_SKIP" == *"$service_name"* ]]; then
        echo "Skipping $service_name service"
        return
    fi

    echo "Building $service_name service"
    repo_name=${service_name}-service
    docker build -t ${repo_name}:$TAG -f ${service_path}/Dockerfile . > /dev/null 2>&1
    docker tag ${repo_name}:$TAG 099048140956.dkr.ecr.us-west-2.amazonaws.com/${repo_name}:$TAG
    echo "Finished building $service_name service"
}

# Loop through each folder in the ./services directory
for service in ./services/*; do
    if [ -d "$service" ]; then
        build_service "$service" &
    fi
done

# Wait for all background jobs to complete
wait

echo "All services built."
