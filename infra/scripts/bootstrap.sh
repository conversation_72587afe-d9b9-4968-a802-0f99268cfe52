#!/bin/bash

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# Get the project root directory (two levels up from the script)
PROJECT_ROOT="$( cd "$SCRIPT_DIR/../.." && pwd )"

# Function to display usage
usage() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -r <recipe_name>    Run a specific recipe"
    echo "  -e <environment>    Optional: Environment to use (local or prod, defaults to local)"
    echo "  -a                  Optional: Run as admin, using the admin secret ARN"
    echo "  validate            Validate all recipes"
    echo ""
    echo "Available recipes:"
    ls -1 "$PROJECT_ROOT/bootstrap/config/recipes" | sed 's/\.yaml$//' | sed 's/^/  /'
}

# Initialize variables
RECIPE=""
ENV="local"
ADMIN=false

# Parse command line arguments
while getopts "r:e:a" opt; do
    case $opt in
        r) RECIPE="$OPTARG";;
        e) ENV="$OPTARG";;
        a) ADMIN=true;;
        \?) usage; exit 1;;
    esac
done

# Check if we have any arguments
if [ $# -eq 0 ]; then
    usage
    exit 1
fi

# Change to the bootstrap directory
cd "$PROJECT_ROOT/bootstrap/cmd/bootstrap"

# Build the command with all arguments
CMD="go run main.go -r $RECIPE -e $ENV"

if [ "$ADMIN" = true ]; then
    CMD="$CMD -a"
fi

# Run the bootstrap command
eval $CMD 