#!/bin/bash
set -e

CONFIG_FILE="./infra/config/servers.json"

# Check if a specific service name is provided
SPECIFIC_SERVICE=""
if [ $# -gt 0 ]; then
    SPECIFIC_SERVICE="$1"
    echo "Building specific service: $SPECIFIC_SERVICE"
else
    echo "Building all services"
fi

# Get the highest existing tag in the vX format
HIGHEST_TAG=$(git tag -l 'v*' | sort -V | tail -n 1)

# Echo the highest tag
echo "Highest tag: $HIGHEST_TAG"

# Extract the number from the highest tag
if [[ $HIGHEST_TAG =~ ^v([0-9]+)$ ]]; then
    CURRENT_VERSION=${BASH_REMATCH[1]}
else
    CURRENT_VERSION=0
fi

# Increment the version number
NEW_VERSION=$((CURRENT_VERSION + 1))
NEW_TAG="v$NEW_VERSION"

# Echo the new tag
echo "New tag: $NEW_TAG"

# Create the new tag
git tag $NEW_TAG
git push origin $NEW_TAG

# Update the server config file with the new image tag
# jq --arg newTag "$NEW_TAG" 'map(.imageTag = $newTag)' "$CONFIG_FILE" > tmp.json && mv tmp.json "$CONFIG_FILE"
# echo "Updated $CONFIG_FILE with new imageTag: $NEW_TAG"

# Use the new tag for the Docker image
TAG=$NEW_TAG

# Login to ECR
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 099048140956.dkr.ecr.us-west-2.amazonaws.com

# Function to build a specific service
build_service() {
    local service_path="$1"
    local service_name=$(basename "$service_path")
    local repo_name=${service_name}-service
    
    # Skip services listed in SERVICES_TO_SKIP
    if [[ -n "$SERVICES_TO_SKIP" && "$SERVICES_TO_SKIP" == *"$service_name"* ]]; then
        echo "Skipping build for $service_name service"
        # Try to retag the existing image with the new tag
        echo "Attempting to retag existing image for $repo_name:$TAG"
        if docker pull 099048140956.dkr.ecr.us-west-2.amazonaws.com/${repo_name}:$HIGHEST_TAG 2>/dev/null; then
            docker tag 099048140956.dkr.ecr.us-west-2.amazonaws.com/${repo_name}:$HIGHEST_TAG 099048140956.dkr.ecr.us-west-2.amazonaws.com/${repo_name}:$TAG
            docker push 099048140956.dkr.ecr.us-west-2.amazonaws.com/${repo_name}:$TAG
            echo "Successfully retagged $repo_name from $HIGHEST_TAG to $TAG"
        else
            echo "Previous image $repo_name:$HIGHEST_TAG not found, building new image instead"
            docker build --platform linux/amd64 -t ${repo_name}:$TAG -f ${service_path}/Dockerfile .
            docker tag ${repo_name}:$TAG 099048140956.dkr.ecr.us-west-2.amazonaws.com/${repo_name}:$TAG
            docker push 099048140956.dkr.ecr.us-west-2.amazonaws.com/${repo_name}:$TAG
        fi
        return
    fi
    
    echo "Building $repo_name:$TAG for linux/amd64 platform"
    docker build --platform linux/amd64 -t ${repo_name}:$TAG -f ${service_path}/Dockerfile .
    docker tag ${repo_name}:$TAG 099048140956.dkr.ecr.us-west-2.amazonaws.com/${repo_name}:$TAG
    docker push 099048140956.dkr.ecr.us-west-2.amazonaws.com/${repo_name}:$TAG
}

# Build specific service or all services
if [ -n "$SPECIFIC_SERVICE" ]; then
    # Build only the specified service
    service_path="./services/$SPECIFIC_SERVICE"
    if [ -d "$service_path" ]; then
        echo "Found service directory: $service_path"
        build_service "$service_path"
    else
        echo "Error: Service directory '$service_path' not found!"
        echo "Available services:"
        ls -1 ./services/
        exit 1
    fi
else
    # Loop through each folder in the ./services directory
    for service in ./services/*; do
        if [ -d "$service" ]; then
            build_service "$service"
        fi
    done
fi
