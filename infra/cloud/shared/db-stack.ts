import * as cdk from "aws-cdk-lib";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import * as rds from "aws-cdk-lib/aws-rds";
import * as secretsmanager from "aws-cdk-lib/aws-secretsmanager";
import { Construct } from "constructs";
import { AppConfig } from "../../cloud-config/config";

export interface PostgresRdsStackProps extends cdk.StackProps {
  dbName: string;
  username: string;
  vpc: ec2.Vpc;
  config: AppConfig;
}

export class PostgresRdsStack extends cdk.Stack {
  public readonly dbSecret: secretsmanager.ISecret;
  public readonly userSecret: secretsmanager.ISecret;
  constructor(scope: Construct, id: string, props: PostgresRdsStackProps) {
    super(scope, id, props);

    const { dbName, username, vpc, config } = props;


    // Create Security Group for the RDS Instance
    const dbSecurityGroup = new ec2.SecurityGroup(this, `${id}RdsSecurityGroup`, {
      vpc,
      description: "Allow PostgreSQL access",
      allowAllOutbound: true,
    });

    // Open port 5432 for database connections
    dbSecurityGroup.addIngressRule(ec2.Peer.ipv4(vpc.vpcCidrBlock), ec2.Port.tcp(5432), "Allow Postgres access from within the VPC");

    // Create Secrets Manager credentials for RDS
    let secretName = 'DBCredentials';
    if (dbName !== 'hero') {
      secretName = dbName + 'DBCredentials';
    }
    const dbSecret = new secretsmanager.Secret(this, secretName, {
      secretName: "Postgres" + secretName,
      generateSecretString: {
        secretStringTemplate: JSON.stringify({ username: "postgres" }),
        generateStringKey: "password",
        excludePunctuation: true,
        includeSpace: false,
        passwordLength: 16,
      },
    });

    // Create a new RDS instance
    const rdsInstance = new rds.DatabaseInstance(this, `${id}RdsInstance`, {
      engine: rds.DatabaseInstanceEngine.postgres({
        version: rds.PostgresEngineVersion.VER_17,
      }),
      instanceType: ec2.InstanceType.of(
        config.db[dbName as keyof typeof config.db].instanceClass as ec2.InstanceClass,
        config.db[dbName as keyof typeof config.db].instanceSize as ec2.InstanceSize,
      ),
      vpc,
      vpcSubnets: {
        subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
      },
      credentials: rds.Credentials.fromSecret(dbSecret),
      databaseName: dbName,
      securityGroups: [dbSecurityGroup],
      publiclyAccessible: false,
      storageEncrypted: true,
      autoMinorVersionUpgrade: true,
      iamAuthentication: true,
      multiAz: config.db[dbName as keyof typeof config.db].multiAz,
      allowMajorVersionUpgrade: false,
      backupRetention: cdk.Duration.days(7),
      deleteAutomatedBackups: false,
      removalPolicy: cdk.RemovalPolicy.SNAPSHOT,
    });

    // HACK - force the server secret name to be the same as it was before
    // this stack became parameterized. otherwise, the secret will be replaced
    const usernameFirstLetterCapitalized = username.charAt(0).toUpperCase() + username.slice(1);
    // Create a new secret for the server user
    const userSecret = new secretsmanager.Secret(this, usernameFirstLetterCapitalized + 'UserSecret', {
      secretName: usernameFirstLetterCapitalized + 'UserSecret',
      generateSecretString: {
        secretStringTemplate: cdk.Stack.of(this).toJsonString({
          username,
          host: rdsInstance.instanceEndpoint.hostname,
          port: rdsInstance.instanceEndpoint.port,
          dbname: dbName,
        }),
        generateStringKey: 'password',
        excludePunctuation: true,
        includeSpace: false,
        passwordLength: 16,
      },
    });

    // 6. Output the RDS Endpoint
    new cdk.CfnOutput(this, "DBEndpoint", {
      value: rdsInstance.instanceEndpoint.hostname,
      description: "RDS PostgreSQL Endpoint",
    });

    // 7. Output the server user secret
    new cdk.CfnOutput(this, "ServerUserSecretOutput", {
      value: userSecret.secretArn,
      description: "User Secret ARN",
    });

    this.dbSecret = dbSecret;
    this.userSecret = userSecret;
  }
}