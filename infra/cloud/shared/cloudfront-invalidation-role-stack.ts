import * as cdk from 'aws-cdk-lib';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';

export interface CloudFrontInvalidationRoleStackProps extends cdk.StackProps {
    distributionId: string;
    githubOrg: string;
    githubRepo: string;
    githubBranch: string;
}

export class CloudFrontInvalidationRoleStack extends cdk.Stack {
    constructor(scope: Construct, id: string, props: CloudFrontInvalidationRoleStackProps) {
        super(scope, id, props);

        const { distributionId, githubOrg, githubRepo, githubBranch } = props;

        const githubProvider = new iam.OpenIdConnectProvider(this, 'GitHubOidcProvider', {
            url: 'https://token.actions.githubusercontent.com',
            thumbprints: ['6938fd4d98bab03faadb97b34396831e3780aea1'],
            clientIds: ['sts.amazonaws.com'],
        });

        const role = new iam.Role(this, 'GitHubCloudFrontInvalidationRole', {
            roleName: 'GitHubCloudFrontInvalidationRole',
            assumedBy: new iam.FederatedPrincipal(
                githubProvider.openIdConnectProviderArn,
                {
                    StringLike: {
                        'token.actions.githubusercontent.com:sub': `repo:${githubOrg}/${githubRepo}:ref:refs/heads/${githubBranch}`,
                    },
                    StringEquals: {
                        'token.actions.githubusercontent.com:aud': 'sts.amazonaws.com',
                    },
                },
                'sts:AssumeRoleWithWebIdentity'
            ),
        });

        role.addToPolicy(
            new iam.PolicyStatement({
                sid: 'AllowCloudFrontInvalidation',
                effect: iam.Effect.ALLOW,
                actions: ['cloudfront:CreateInvalidation'],
                resources: [`arn:aws:cloudfront::${cdk.Aws.ACCOUNT_ID}:distribution/${distributionId}`],
            })
        );
    }
} 