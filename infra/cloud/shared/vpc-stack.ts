import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import { Construct } from 'constructs';

export interface VPCStackProps extends cdk.StackProps {
  cidr?: string;
}

export class VPCStack extends cdk.Stack {
  public readonly vpc: ec2.Vpc;
  public readonly cluster: ecs.Cluster;

  constructor(scope: Construct, id: string, props?: VPCStackProps) {
    super(scope, id, props);

    this.vpc = new ec2.Vpc(this, 'VPC', {
      ipAddresses: props?.cidr ? ec2.IpAddresses.cidr(props.cidr) : undefined,
      maxAzs: 2,
      defaultInstanceTenancy: ec2.DefaultInstanceTenancy.DEFAULT,
      subnetConfiguration: [
        {
          cidrMask: 24,
          name: 'ingress',
          subnetType: ec2.SubnetType.PUBLIC,
        },
        {
          cidrMask: 24,
          name: 'application',
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        {
          cidrMask: 28,
          name: 'dbs',
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        }
      ]
    });

    this.vpc.addGatewayEndpoint('S3Endpoint', {
      service: ec2.GatewayVpcEndpointAwsService.S3,
    });

    this.cluster = new ecs.Cluster(this, 'FargateCluster', {
      vpc: this.vpc,
    });
  }
}
