import { S3Stack } from '../s3bucket/s3-stack';
import { CognitoStack } from './cognito-stack/cognito-stack';
import { PostgresRdsStack } from './db-stack';
import { GlobalCertsStack } from './global-certs-stack';
import { LocalCertsStack } from './local-certs-stack';
import { PasswordStack } from './password-stack';
import { SnsStack } from './sns/sns-stack';
import { VPCStack } from './vpc-stack';

export interface SharedStacks {
    vpc: VPCStack;
    s3Stack: S3Stack;
    cognitoStack: CognitoStack;
    certStack: GlobalCertsStack;
    localCertStack: LocalCertsStack;
    dbStack: PostgresRdsStack;
    permsDbStack: PostgresRdsStack;
    snsStack: SnsStack;
    passwordStack: PasswordStack;
}