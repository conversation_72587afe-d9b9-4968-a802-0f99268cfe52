import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { KeyPair } from 'cdk-ec2-key-pair';
import { Construct } from 'constructs';
import { AppConfig } from '../../cloud-config/config';
import { SharedStacks } from './shared-config';

export interface BastionStackProps extends cdk.StackProps {
    sharedStacks: SharedStacks;
    config: AppConfig;
}

export class BastionStack extends cdk.Stack {
    constructor(scope: Construct, id: string, props: BastionStackProps) {
        super(scope, id, props);

        const { sharedStacks, config } = props;

        const vpc = sharedStacks.vpc.vpc;

        // Security Group for the EC2 instance
        const securityGroup = new ec2.SecurityGroup(this, 'BastionSecurityGroup', {
            vpc,
            description: 'Allow SSH traffic',
            allowAllOutbound: true,
        });

        // Allow SSH only from Tailscale subnet range (**********/10)
        securityGroup.addIngressRule(
            ec2.Peer.ipv4('**********/10'),
            ec2.Port.tcp(22),
            'Allow SSH from Tailscale'
        );

        // Create a Key Pair for the EC2 instance
        const keyPair = new KeyPair(this, 'TailscaleKeyPair', {
            keyPairName: 'TailscaleKeyPair',
            description: 'Key pair for Tailscale EC2 instance',
            storePublicKey: true, // Stores the public key in Secrets Manager
        });

        // Retrieve the Tailscale auth key from Secrets Manager
        const tailscaleAuthKey = secretsmanager.Secret.fromSecretNameV2(this, 'TailscaleAuthKey', 'tailscale/authkey');

        // Create an EC2 instance
        const instance = new ec2.Instance(this, 'TailscaleInstance', {
            instanceType: ec2.InstanceType.of(
                config.bastion.instanceClass as ec2.InstanceClass,
                config.bastion.instanceSize as ec2.InstanceSize,
            ),
            machineImage: ec2.MachineImage.latestAmazonLinux2(),
            vpc,
            securityGroup,
            vpcSubnets: {
                subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
            },
            keyPair: keyPair,
        });

        // Grant the instance permission to read the secret
        tailscaleAuthKey.grantRead(instance.role);

        // Calculate subnet CIDRs to advertise via Tailscale
        const advertisedRoutes: string[] = [];

        // Get all subnets from the VPC
        const privateSubnets = vpc.privateSubnets;
        const isolatedSubnets = vpc.isolatedSubnets;

        // Add private subnet CIDRs
        privateSubnets.forEach(subnet => {
            advertisedRoutes.push(subnet.ipv4CidrBlock);
        });

        // Add isolated subnet CIDRs (database subnets)
        isolatedSubnets.forEach(subnet => {
            advertisedRoutes.push(subnet.ipv4CidrBlock);
        });

        // Build the advertise-routes string
        const routesString = advertisedRoutes.join(',');

        // User data script to install and start Tailscale
        instance.addUserData(
            `#!/bin/bash`,
            `curl -fsSL https://tailscale.com/install.sh | sh`,
            `echo 'net.ipv4.ip_forward = 1' | tee -a /etc/sysctl.d/99-tailscale.conf`,
            `echo 'net.ipv6.conf.all.forwarding = 1' | tee -a /etc/sysctl.d/99-tailscale.conf`,
            `sysctl -p /etc/sysctl.d/99-tailscale.conf`,
            'systemctl enable --now tailscaled',
            `tailscale up --authkey $(aws secretsmanager get-secret-value --secret-id ${tailscaleAuthKey.secretArn} --query SecretString --output text --region ${this.region}) --advertise-routes=${routesString}`,
        );

        // Output the public IP of the instance
        new cdk.CfnOutput(this, 'InstancePrivateIP', {
            value: instance.instancePrivateIp,
            description: 'The Private IP of the Tailscale EC2 instance',
        });
    }
}