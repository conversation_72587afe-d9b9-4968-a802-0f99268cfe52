import * as cdk from 'aws-cdk-lib';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as cognitoIdentity from 'aws-cdk-lib/aws-cognito';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as route53_targets from 'aws-cdk-lib/aws-route53-targets';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as CustomResources from 'aws-cdk-lib/custom-resources';
import { Construct } from 'constructs';
import * as path from 'path';
import { AppConfig } from '../../../cloud-config/config';
import { VPCStack } from '../vpc-stack';

export interface CognitoStackProps extends cdk.StackProps {
    cert: acm.ICertificate;
    vpcStack: VPCStack;
    config: AppConfig;
    hostedZone: route53.IHostedZone;
}

export class CognitoStack extends cdk.Stack {
    public readonly userPool: cognito.UserPool;
    public readonly userPoolDomain: cognito.UserPoolDomain;
    public readonly memberClient: cognito.UserPoolClient;
    public readonly internalClient: cognito.UserPoolClient;
    public readonly adminClient: cognito.UserPoolClient;
    public readonly internalIdentityPoolId: string;
    constructor(scope: Construct, id: string, props: CognitoStackProps) {
        super(scope, id, props);

        const { cert, vpcStack, config, hostedZone } = props;

        const postConfirmationLambdaSecret = new secretsmanager.Secret(this, 'PostConfirmationLambdaSecret', {
            secretName: `bots/bot-post-confirmation-lambda-secret`,
            generateSecretString: {
                passwordLength: 30,
                excludePunctuation: true,
            },
        });

        const preSignUpLambdaSecret = new secretsmanager.Secret(this, 'PreSignUpLambdaSecret', {
            secretName: `bots/bot-pre-signup-lambda-secret`,
            generateSecretString: {
                passwordLength: 30,
                excludePunctuation: true,
            },
        });

        new secretsmanager.Secret(this, 'CameraListenerLambdaSecret', {
            secretName: `bots/bot-camera-listener-lambda-secret`,
            generateSecretString: {
                passwordLength: 30,
                excludePunctuation: true,
            },
        });

        // Create a User Pool
        const userPool = new cognito.UserPool(this, 'UserPool', {
            userPoolName: 'MyUserPool',
            selfSignUpEnabled: config.enableUsernameLogin, // Disable "Create an account" on Hosted UI
            signInAliases: { email: true },
            signInCaseSensitive: false,
            // autoVerify: { email: true }, // this must be set below :(
            // see https://github.com/aws/aws-cdk/issues/10002#issuecomment-**********
        });
        this.userPool = userPool;

        const membersGroup = new cognito.CfnUserPoolGroup(this, 'MembersGroup', {
            userPoolId: userPool.userPoolId,
            groupName: 'members',
            description: 'Group for members',
        });

        const internalGroup = new cognito.CfnUserPoolGroup(this, 'InternalGroup', {
            userPoolId: userPool.userPoolId,
            groupName: 'internal',
            description: 'Group for internal employees',
        });

        // Create a User Pool Client
        const memberClient = new cognito.UserPoolClient(this, 'MemberClient', {
            userPool,
            generateSecret: false,
            supportedIdentityProviders: [
                cognito.UserPoolClientIdentityProvider.COGNITO,
            ],
            authFlows: {
                user: true
            }
        });
        this.memberClient = memberClient;

        // Create Okta providers based on configuration
        const oktaProviders = config.oktaProviders.map(providerConfig => {
            return new cognito.UserPoolIdentityProviderSaml(this, `${providerConfig.name}Provider`, {
                userPool: this.userPool,
                name: providerConfig.name,
                metadata: cognito.UserPoolIdentityProviderSamlMetadata.url(providerConfig.metadataUrl),
                // encryptedResponses: true,
                // requestSigningAlgorithm: cognito.SigningAlgorithm.RSA_SHA256,
                attributeMapping: {
                    email: cognito.ProviderAttribute.other('email'),
                    familyName: cognito.ProviderAttribute.other('family_name'),
                    givenName: cognito.ProviderAttribute.other('given_name'),
                },
            });
        });

        const supportedIdentityProviders = oktaProviders.map(provider => 
            cognito.UserPoolClientIdentityProvider.custom(provider.providerName)
        )
        if (config.enableUsernameLogin) {
            supportedIdentityProviders.push(cognito.UserPoolClientIdentityProvider.COGNITO)
        }

        // Create a User Pool Client
        const internalClient = new cognito.UserPoolClient(this, 'InternalClient', {
            userPool,
            generateSecret: false,
            enableTokenRevocation: true,
            authFlows: {
                // user: true,
                userPassword: true,
                userSrp: true
            },
            supportedIdentityProviders,
            oAuth: {
                flows: {
                    authorizationCodeGrant: true
                },
                callbackUrls: [
                    `https://command.${config.environment.domain}`,
                    `https://${config.environment.domain}`,
                    'myapp://callback/',

                ],
                logoutUrls: [
                    `https://command.${config.environment.domain}`,
                    `https://${config.environment.domain}`,
                    'myapp://callback/'
                ],
            },
        });
        this.internalClient = internalClient;
        oktaProviders.forEach(provider => {
            internalClient.node.addDependency(provider);
        });

        // create a admin client, for running bootstrap as admin
        const adminClient = new cognito.UserPoolClient(this, 'AdminClient', {
            userPool,
            generateSecret: false,
            enableTokenRevocation: true,
            authFlows: {
                userPassword: true,
                userSrp: true
            },
            supportedIdentityProviders: [
                cognito.UserPoolClientIdentityProvider.COGNITO,
            ],
        });
        this.adminClient = adminClient;

        const postConfirmationLambda = new lambda.Function(this, 'PostConfirmationLambda', {
            functionName: 'PostConfirmationLambda',
            runtime: lambda.Runtime.PROVIDED_AL2023,
            handler: 'main',
            timeout: cdk.Duration.seconds(10),
            vpc: vpcStack.vpc,
            code: lambda.Code.fromAsset(path.join(__dirname, '../cognito-stack/postConfirmationLambda/bootstrap.zip')), // Path to Go Lambda zip 
            environment: {
                PARAMETERS_SECRETS_EXTENSION_LOG_LEVEL: 'debug',
                PARAMETERS_SECRETS_EXTENSION_CACHE_SIZE: '10',
                SECRETS_MANAGER_TTL: '300',
                USER_POOL_ID: userPool.userPoolId,
                MEMBER_CLIENT_ID: memberClient.userPoolClientId,
                INTERNAL_CLIENT_ID: internalClient.userPoolClientId,
                MEMBER_ROLE_NAME: 'Member',
                DEFAULT_ROLE_NAME: 'Responder',
                WORKFLOW_SERVICE_URL: `https://workflow.lb.api.${config.environment.domain}`,
                ORGS_SERVICE_URL: `https://orgs.lb.api.${config.environment.domain}`,
                PERMS_SERVICE_URL: `https://perms.lb.api.${config.environment.domain}`,
                OKTA_ORG_MAPPING: (() => {
                    const mapping: { [issuer: string]: string } = {};
                    config.oktaProviders.forEach(provider => {
                        mapping[provider.issuer] = provider.orgId;
                    });
                    return JSON.stringify(mapping);
                })(),
                AUTH_SECRET_ARN: postConfirmationLambdaSecret.secretArn,
            },
            paramsAndSecrets: lambda.ParamsAndSecretsLayerVersion.fromVersion(
                lambda.ParamsAndSecretsVersions.V1_0_103,
                {
                    cacheSize: 10,
                    logLevel: lambda.ParamsAndSecretsLogLevel.DEBUG,
                }
            ),
        });

        postConfirmationLambdaSecret.grantRead(postConfirmationLambda);

        // Give Cognito permission to invoke the Lambda
        postConfirmationLambda.addPermission('AllowCognitoInvokePost', {
            principal: new iam.ServicePrincipal('cognito-idp.amazonaws.com'),
            action: 'lambda:InvokeFunction',
            sourceArn: userPool.userPoolArn,
        });

        const preSignUpLambda = new lambda.Function(this, 'PreSignUpLambda', {
            functionName: 'PreSignUpLambda',
            runtime: lambda.Runtime.PROVIDED_AL2023,
            handler: 'main',
            timeout: cdk.Duration.seconds(10),
            vpc: vpcStack.vpc,
            code: lambda.Code.fromAsset(path.join(__dirname, '../cognito-stack/preSignUpLambda/bootstrap.zip')), // Path to Go Lambda zip 
            environment: {
                PARAMETERS_SECRETS_EXTENSION_LOG_LEVEL: 'debug',
                PARAMETERS_SECRETS_EXTENSION_CACHE_SIZE: '10',
                SECRETS_MANAGER_TTL: '300',
                ORGS_SERVICE_URL: `https://orgs.lb.api.${config.environment.domain}`,
                PERMS_SERVICE_URL: `https://perms.lb.api.${config.environment.domain}`,
                AUTH_SECRET_ARN: preSignUpLambdaSecret.secretArn,
            },
            paramsAndSecrets: lambda.ParamsAndSecretsLayerVersion.fromVersion(
                lambda.ParamsAndSecretsVersions.V1_0_103,
                {
                    cacheSize: 10,
                    logLevel: lambda.ParamsAndSecretsLogLevel.DEBUG,
                }
            ),
        });

        preSignUpLambdaSecret.grantRead(preSignUpLambda);

        // Give Cognito permission to invoke the Lambda
        preSignUpLambda.addPermission('AllowCognitoInvokePre', {
            principal: new iam.ServicePrincipal('cognito-idp.amazonaws.com'),
            action: 'lambda:InvokeFunction',
            sourceArn: userPool.userPoolArn,
        });

        // HACK - comment out this custom resource, deploy the userpool and lambda, uncomment, deploy again
        // fix for circular dependency when trying to do userPool.addTrigger(cognito.UserPoolOperation.POST_CONFIRMATION, postConfirmationLambda);
        // found here - https://github.com/aws/aws-cdk/issues/10002#issuecomment-854169838
        new CustomResources.AwsCustomResource(this, "UpdateUserPool", {
            resourceType: "Custom::UpdateUserPool",
            // see https://docs.aws.amazon.com/cognito-user-identity-pools/latest/APIReference/API_UpdateUserPool.html for parameters
            onCreate: {
                region: this.region,
                service: "CognitoIdentityServiceProvider",
                action: "updateUserPool",
                parameters: {
                    UserPoolId: userPool.userPoolId,
                    AutoVerifiedAttributes: ['email'],
                    LambdaConfig: {
                        PostConfirmation: postConfirmationLambda.functionArn,
                        PreSignUp: preSignUpLambda.functionArn,
                        // Uncomment to override the default triggers
                        //   DefineAuthChallenge: defineAuthChallengeHandler.functionArn,
                        //   CreateAuthChallenge: createAuthChallengeHandler.functionArn,
                        //   VerifyAuthChallengeResponse: verifyAuthChallengeResponseHandler.functionArn,
                    },
                },
                physicalResourceId: CustomResources.PhysicalResourceId.of(userPool.userPoolId),
            },
            policy: CustomResources.AwsCustomResourcePolicy.fromSdkCalls({ resources: CustomResources.AwsCustomResourcePolicy.ANY_RESOURCE }),
        });

        // Grant Lambda permissions to manage Cognito groups
        postConfirmationLambda.addToRolePolicy(new iam.PolicyStatement({
            actions: ['cognito-idp:AdminAddUserToGroup'],
            resources: [userPool.userPoolArn]
        }));

        const authDomainName = `auth.${config.environment.domain}`;



        // CUSTOM DOMAIN
        // Add a domain to the User Pool
        const userPoolDomain = new cognito.UserPoolDomain(this, 'UserPoolDomain', {
            userPool,
            customDomain: {
                domainName: authDomainName,
                certificate: cert
            }
        });
        this.userPoolDomain = userPoolDomain;

        new route53.ARecord(this, 'UserPoolCloudFrontAliasRecord', {
            zone: hostedZone,
            recordName: authDomainName,
            target: route53.RecordTarget.fromAlias(new route53_targets.UserPoolDomainTarget(userPoolDomain)),
        });
        // CUSTOM DOMAIN



        // Create an Identity Pool
        const memberIdentityPool = new cognitoIdentity.CfnIdentityPool(this, 'MemberIdentityPool', {
            allowUnauthenticatedIdentities: false,
            cognitoIdentityProviders: [
                {
                    clientId: memberClient.userPoolClientId,
                    providerName: userPool.userPoolProviderName,
                },
            ],
        });

        const internalIdentityPool = new cognitoIdentity.CfnIdentityPool(this, 'InternalIdentityPool', {
            allowUnauthenticatedIdentities: false,
            cognitoIdentityProviders: [
                {
                    clientId: internalClient.userPoolClientId,
                    providerName: userPool.userPoolProviderName,
                },
            ],
        });
        this.internalIdentityPoolId = internalIdentityPool.ref;

        // Authenticated Role
        const memberAuthenticatedRole = new iam.Role(this, 'MemberAuthenticatedRole', {
            assumedBy: new iam.FederatedPrincipal(
                'cognito-identity.amazonaws.com',
                {
                    'StringEquals': { 'cognito-identity.amazonaws.com:aud': memberIdentityPool.ref },
                    'ForAnyValue:StringLike': { 'cognito-identity.amazonaws.com:amr': 'authenticated' },
                },
                'sts:AssumeRoleWithWebIdentity'
            ),
            managedPolicies: [
                // iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
            ],
        });

        // Add KVS permissions to member role
        memberAuthenticatedRole.addToPolicy(new iam.PolicyStatement({
            actions: [
                'kinesisvideo:DescribeSignalingChannel',
                'kinesisvideo:GetSignalingChannelEndpoint',
                'kinesisvideo:GetIceServerConfig',
                'kinesisvideo:ConnectAsViewer',
                'kinesisvideo:JoinStorageSession',
                'kinesisvideo:GetDataEndpoint'
            ],
            resources: ['*']
        }));

        // Authenticated Role
        const internalAuthenticatedRole = new iam.Role(this, 'InternalAuthenticatedRole', {
            assumedBy: new iam.FederatedPrincipal(
                'cognito-identity.amazonaws.com',
                {
                    'StringEquals': { 'cognito-identity.amazonaws.com:aud': internalIdentityPool.ref },
                    'ForAnyValue:StringLike': { 'cognito-identity.amazonaws.com:amr': 'authenticated' },
                },
                'sts:AssumeRoleWithWebIdentity'
            ),
            managedPolicies: [
                // iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
            ],
        });

        // Add KVS permissions to internal role
        internalAuthenticatedRole.addToPolicy(new iam.PolicyStatement({
            actions: [
                'kinesisvideo:DescribeSignalingChannel',
                'kinesisvideo:GetSignalingChannelEndpoint',
                'kinesisvideo:GetIceServerConfig',
                'kinesisvideo:ConnectAsViewer',
                'kinesisvideo:JoinStorageSession',
                'kinesisvideo:GetDataEndpoint'
            ],
            resources: ['*']
        }));

        // Attach roles to the Identity Pool
        new cognitoIdentity.CfnIdentityPoolRoleAttachment(this, 'MemberIdentityPoolRoleAttachment', {
            identityPoolId: memberIdentityPool.ref,
            roles: {
                authenticated: memberAuthenticatedRole.roleArn,
            },
        });

        new cognitoIdentity.CfnIdentityPoolRoleAttachment(this, 'InternalIdentityPoolRoleAttachment', {
            identityPoolId: internalIdentityPool.ref,
            roles: {
                authenticated: internalAuthenticatedRole.roleArn,
            },
        });

        // Export values for use by SSM Parameters stack
        new cdk.CfnOutput(this, 'UserPoolIdOutput', {
            value: this.userPool.userPoolId,
            exportName: `${config.environment.envName}-UserPoolId`,
        });

        new cdk.CfnOutput(this, 'InternalUserPoolClientIdOutput', {
            value: this.internalClient.userPoolClientId,
            exportName: `${config.environment.envName}-InternalUserPoolClientId`,
        });

        new cdk.CfnOutput(this, 'InternalIdentityPoolIdOutput', {
            value: this.internalIdentityPoolId,
            exportName: `${config.environment.envName}-InternalIdentityPoolId`,
        });
    }
}