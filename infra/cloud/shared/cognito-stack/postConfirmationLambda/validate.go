package main

import (
	"fmt"
	"os"
)

func main() {
	fmt.Println("🧪 PostConfirmation Lambda Validation")
	fmt.Println("=====================================")

	// Test 1: Parse identities
	identitiesJSON := `[{"dateCreated":"1753134442462","userId":"<EMAIL>","providerName":"Okta","providerType":"SAML","issuer":"http://www.okta.com/exk1wx7pr3rQqDomN1d8","primary":"true"}]`
	
	issuer, err := parseIdentitiesForIssuer(identitiesJSON)
	if err != nil {
		fmt.Printf("❌ Failed to parse issuer: %v\n", err)
		return
	}
	fmt.Printf("✅ Parsed issuer: %s\n", issuer)

	// Test 2: Map issuer to org
	os.Setenv("OKTA_ORG_MAPPING", `{"http://www.okta.com/exk1wx7pr3rQqDomN1d8": "1"}`)
	
	orgID, err := getOrgFromIssuer(issuer)
	if err != nil {
		fmt.Printf("❌ Failed to map issuer to org: %v\n", err)
		return
	}
	fmt.Printf("✅ Mapped to org ID: %d\n", orgID)

	// Test 3: Environment variables
	fmt.Println("\n📋 Environment Variables Check:")
	envVars := []string{
		"AUTH_SECRET_ARN",
		"ORGS_SERVICE_URL", 
		"WORKFLOW_SERVICE_URL",
		"PERMS_SERVICE_URL",
		"USER_POOL_ID",
		"DEFAULT_ROLE_NAME",
		"DEFAULT_ASSET_TYPE",
		"OKTA_ORG_MAPPING",
	}

	allSet := true
	for _, envVar := range envVars {
		value := os.Getenv(envVar)
		if value == "" {
			fmt.Printf("❌ %s: NOT SET\n", envVar)
			allSet = false
		} else {
			fmt.Printf("✅ %s: %s\n", envVar, value)
		}
	}

	if allSet {
		fmt.Println("\n🎉 All validations passed! Lambda should work correctly.")
	} else {
		fmt.Println("\n⚠️  Some environment variables are missing. Set them before deploying.")
	}

	// Test 4: Show what would happen
	fmt.Println("\n🔄 Expected Lambda Flow:")
	fmt.Printf("1. Extract issuer: %s\n", issuer)
	fmt.Printf("2. Map to org ID: %d\n", orgID)
	fmt.Println("3. Look up pre-registration for: <EMAIL> in org", orgID)
	fmt.Println("4. Assign permission role from DEFAULT_ROLE_NAME")
	fmt.Println("5. Create asset with determined asset type")
	fmt.Println("6. Add to Cognito group: org:1")
}
