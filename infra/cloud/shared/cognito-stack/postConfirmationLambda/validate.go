package main

import (
	"fmt"
	"os"
)

func main() {
	fmt.Println("🧪 PostConfirmation Lambda Mock Test")
	fmt.Println("====================================")
	fmt.Println("Testing with <PERSON><PERSON><PERSON><PERSON>'s real event...")

	// Test 1: Parse identities from real event
	identitiesJSON := `[{"dateCreated":"1753129116893","userId":"<EMAIL>","providerName":"Okta","providerType":"SAML","issuer":"http://www.okta.com/exk1wx7pr3rQqDomN1d8","primary":"true"}]`

	issuer, err := parseIdentitiesForIssuer(identitiesJSON)
	if err != nil {
		fmt.Printf("❌ Failed to parse issuer: %v\n", err)
		return
	}
	fmt.Printf("✅ Parsed issuer: %s\n", issuer)

	// Test 2: Map issuer to org 2 (as per your scenario)
	os.Setenv("OKTA_ORG_MAPPING", `{"http://www.okta.com/exk1wx7pr3rQqDomN1d8": "2"}`)

	orgID, err := getOrgFromIssuer(issuer)
	if err != nil {
		fmt.Printf("❌ Failed to map issuer to org: %v\n", err)
		return
	}
	fmt.Printf("✅ Mapped to org ID: %d\n", orgID)

	// Test 3: Environment variables
	fmt.Println("\n📋 Environment Variables Check:")
	envVars := []string{
		"AUTH_SECRET_ARN",
		"ORGS_SERVICE_URL",
		"WORKFLOW_SERVICE_URL",
		"PERMS_SERVICE_URL",
		"USER_POOL_ID",
		"DEFAULT_ROLE_NAME",
		"DEFAULT_ASSET_TYPE",
		"OKTA_ORG_MAPPING",
	}

	allSet := true
	for _, envVar := range envVars {
		value := os.Getenv(envVar)
		if value == "" {
			fmt.Printf("❌ %s: NOT SET\n", envVar)
			allSet = false
		} else {
			fmt.Printf("✅ %s: %s\n", envVar, value)
		}
	}

	if allSet {
		fmt.Println("\n🎉 All validations passed! Lambda should work correctly.")
	} else {
		fmt.Println("\n⚠️  Some environment variables are missing. Set them before deploying.")
	}

	// Test 4: Simulate the full lambda flow
	fmt.Println("\n🔄 Simulating Full Lambda Flow:")
	fmt.Printf("1. ✅ Extract issuer: %s\n", issuer)
	fmt.Printf("2. ✅ Map to org ID: %d\n", orgID)

	userEmail := "<EMAIL>"
	fmt.Printf("3. 🔍 Look up pre-registration for: %s in org %d\n", userEmail, orgID)

	// Simulate pre-registration lookup (your database data)
	fmt.Println("   📋 Pre-registration data found:")
	fmt.Println("   - email: <EMAIL>")
	fmt.Println("   - org_id: 2")
	fmt.Println("   - asset_type_label: supervisor")
	fmt.Println("   - asset_type: 5 (ASSET_TYPE_BOT)")

	// Simulate what lambda would do
	assetType := 5 // From pre-registration
	permissionRole := os.Getenv("DEFAULT_ROLE_NAME")
	if permissionRole == "" {
		permissionRole = "Responder" // fallback
	}

	fmt.Printf("4. ✅ Assign permission role: %s (from DEFAULT_ROLE_NAME)\n", permissionRole)
	fmt.Printf("5. ✅ Create asset with type: %d (from pre-registration)\n", assetType)
	fmt.Printf("6. ✅ Add to Cognito group: org:%d\n", orgID)

	fmt.Println("\n🎯 Expected Results:")
	fmt.Printf("   - User: <EMAIL>\n")
	fmt.Printf("   - Email: %s\n", userEmail)
	fmt.Printf("   - Org: %d\n", orgID)
	fmt.Printf("   - Permission Role: %s\n", permissionRole)
	fmt.Printf("   - Asset Type: %d (supervisor)\n", assetType)
	fmt.Printf("   - Cognito Group: org:%d\n", orgID)

	fmt.Println("\n🎉 Lambda should successfully process this event!")
	fmt.Println("   The user will get supervisor asset type from pre-registration")
	fmt.Printf("   but %s permission role from environment variable.\n", permissionRole)
}
