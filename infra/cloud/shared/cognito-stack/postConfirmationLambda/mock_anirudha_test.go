package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
)

func TestAnirudhaRealEvent(t *testing.T) {
	fmt.Println("🧪 Testing Anirudha's Real Event")
	fmt.Println("=================================")

	// Create mock servers that simulate your database state
	orgsServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/hero.orgs.v1.OrgsService/ListOrgs" {
			// Mock ListOrgs response
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(200)
			fmt.Fprint(w, `{
				"orgs": [
					{
						"id": 2,
						"name": "Test Org 2",
						"domains": ["gethero.com"]
					}
				]
			}`)
		} else if r.URL.Path == "/hero.orgs.v1.OrgsService/GetPreRegistrationMapping" {
			// Mock your database state: <EMAIL> in org 2 as supervisor
			w.<PERSON>er().Set("Content-Type", "application/json")
			w.WriteHeader(200)
			fmt.Fprint(w, `{
				"mapping": {
					"id": "test-mapping-id",
					"email": "<EMAIL>",
					"orgId": 2,
					"assetTypeLabel": "supervisor",
					"assetType": 5
				}
			}`)
		} else if r.URL.Path == "/hero.orgs.v1.OrgsService/MarkMappingAsUsed" {
			// Mock mark as used response
			w.WriteHeader(200)
			fmt.Fprint(w, `{}`)
		}
	}))
	defer orgsServer.Close()

	workflowServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/hero.assets.v2.AssetRegistryService/CreateAsset" {
			// Mock CreateAsset response - should receive asset type 5
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(200)
			fmt.Fprint(w, `{
				"asset": {
					"id": "anirudha-asset-id",
					"name": "anirudha",
					"type": 5,
					"cognitoJwtSub": "88d1d380-40e1-7033-f9e3-d27d8cd17ee2",
					"orgId": 2
				}
			}`)
			t.Log("✅ CreateAsset called with asset type 5 (supervisor)")
		}
	}))
	defer workflowServer.Close()

	permsServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/hero.permissions.v1.PermissionService/AddCognitoUserToRole" {
			// Mock AddCognitoUserToRole response - should receive "Responder" role
			w.WriteHeader(200)
			fmt.Fprint(w, `{}`)
			t.Log("✅ AddCognitoUserToRole called with Responder role")
		}
	}))
	defer permsServer.Close()

	// Set environment variables to match your scenario
	os.Setenv("ORGS_SERVICE_URL", orgsServer.URL)
	os.Setenv("WORKFLOW_SERVICE_URL", workflowServer.URL)
	os.Setenv("PERMS_SERVICE_URL", permsServer.URL)
	os.Setenv("AUTH_SECRET_ARN", "mock-secret")
	os.Setenv("USER_POOL_ID", "us-west-2_88f247NGw")
	os.Setenv("DEFAULT_ROLE_NAME", "Responder")  // Permission role from env var
	os.Setenv("DEFAULT_ASSET_TYPE", "3")         // Fallback (won't be used due to pre-registration)
	os.Setenv("OKTA_ORG_MAPPING", `{"http://www.okta.com/exk1wx7pr3rQqDomN1d8": "2"}`) // Maps to org 2

	// Create Anirudha's exact event
	event := CognitoUserEvent{
		Version:       "1",
		TriggerSource: "PostConfirmation_ConfirmSignUp",
		Region:        "us-west-2",
		UserName:      "<EMAIL>",
		Sub:           "",
		UserPoolID:    "us-west-2_88f247NGw",
		CallerContext: struct {
			ClientID string `json:"clientId"`
		}{
			ClientID: "3fv7mur82c02q30o7oflj0u5ub",
		},
		Request: struct {
			UserAttributes map[string]string `json:"userAttributes"`
		}{
			UserAttributes: map[string]string{
				"cognito:user_status": "EXTERNAL_PROVIDER",
				"email":               "<EMAIL>",
				"email_verified":      "false",
				"family_name":         "Paul",
				"given_name":          "Anirudha",
				"identities":          `[{"dateCreated":"1753129116893","userId":"<EMAIL>","providerName":"Okta","providerType":"SAML","issuer":"http://www.okta.com/exk1wx7pr3rQqDomN1d8","primary":"true"}]`,
				"sub":                 "88d1d380-40e1-7033-f9e3-d27d8cd17ee2",
			},
		},
	}

	// Print the test scenario
	fmt.Printf("📧 Email: %s\n", event.Request.UserAttributes["email"])
	fmt.Printf("👤 Username: %s\n", event.UserName)
	fmt.Printf("🏢 Expected Org: 2 (from SAML issuer)\n")
	fmt.Printf("📋 Pre-registration: supervisor (asset type 5)\n")
	fmt.Printf("🔑 Permission Role: Responder (from env var)\n")

	// Run the handler
	ctx := context.Background()
	result, err := handler(ctx, event)

	if err != nil {
		t.Fatalf("❌ Handler failed: %v", err)
	}

	t.Logf("✅ Handler succeeded: %+v", result)

	fmt.Println("\n🎯 Expected Results:")
	fmt.Println("✅ User mapped to org 2 via SAML issuer")
	fmt.Println("✅ Pre-registration found: supervisor asset type")
	fmt.Println("✅ Permission role assigned: Responder")
	fmt.Println("✅ Asset created with type 5 (supervisor)")
	fmt.Println("✅ Added to Cognito group: org:2")
	fmt.Println("\n🎉 Lambda successfully processed Anirudha's event!")
}

func TestPreRegistrationLogic(t *testing.T) {
	fmt.Println("\n🔍 Testing Pre-Registration Logic")
	fmt.Println("=================================")

	// Test the core logic without full lambda execution
	userEmail := "<EMAIL>"
	orgID := int32(2)

	fmt.Printf("Looking up: %s in org %d\n", userEmail, orgID)
	fmt.Println("Database contains:")
	fmt.Println("  email: <EMAIL>")
	fmt.Println("  org_id: 2")
	fmt.Println("  asset_type_label: supervisor")
	fmt.Println("  asset_type: 5")

	fmt.Println("\nLambda should:")
	fmt.Println("✅ Find the pre-registration mapping")
	fmt.Println("✅ Use asset_type: 5 (from database)")
	fmt.Println("✅ Use permission role: Responder (from env var)")
	fmt.Println("✅ Create asset with type 5")
	fmt.Println("✅ Assign Responder role for permissions")
}
