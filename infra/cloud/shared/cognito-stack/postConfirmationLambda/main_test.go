package main

import (
	"context"
	"encoding/json"
	"os"
	"testing"
)

func TestPostConfirmationHandler(t *testing.T) {
	// Set up environment variables for testing
	os.Setenv("AUTH_SECRET_ARN", "mock-secret-arn")
	os.Setenv("ORGS_SERVICE_URL", "http://localhost:8080") // Point to your local services
	os.Setenv("WORKFLOW_SERVICE_URL", "http://localhost:8081")
	os.Setenv("PERMS_SERVICE_URL", "http://localhost:8082")
	os.Setenv("USER_POOL_ID", "us-west-2_88f247NGw")
	os.Setenv("DEFAULT_ROLE_NAME", "Responder")
	os.Setenv("DEFAULT_ASSET_TYPE", "3")
	os.Setenv("OKTA_ORG_MAPPING", `{"http://www.okta.com/exk1wx7pr3rQqDomN1d8": "1"}`)

	// Create the test event (your real event)
	event := CognitoUserEvent{
		Version:       "1",
		TriggerSource: "PostConfirmation_ConfirmSignUp",
		Region:        "us-west-2",
		UserName:      "<EMAIL>",
		Sub:           "",
		UserPoolID:    "us-west-2_88f247NGw",
		CallerContext: struct {
			ClientID string `json:"clientId"`
		}{
			ClientID: "3fv7mur82c02q30o7oflj0u5ub",
		},
		Request: struct {
			UserAttributes map[string]string `json:"userAttributes"`
		}{
			UserAttributes: map[string]string{
				"cognito:user_status": "EXTERNAL_PROVIDER",
				"email":               "<EMAIL>",
				"email_verified":      "false",
				"family_name":         "Vaules",
				"given_name":          "Alec",
				"identities":          `[{"dateCreated":"1753134442462","userId":"<EMAIL>","providerName":"Okta","providerType":"SAML","issuer":"http://www.okta.com/exk1wx7pr3rQqDomN1d8","primary":"true"}]`,
				"sub":                 "e8517370-00d1-70a2-4e3d-69c68fb591b7",
			},
		},
	}

	// Run the handler
	ctx := context.Background()
	result, err := handler(ctx, event)

	// Check results
	if err != nil {
		t.Logf("Handler returned error: %v", err)
		// Don't fail the test immediately - might be expected if services aren't running
	}

	t.Logf("Handler result: %+v", result)
	
	// Print the event for debugging
	eventJSON, _ := json.MarshalIndent(event, "", "  ")
	t.Logf("Test event: %s", eventJSON)
}

func TestParseIdentitiesForIssuer(t *testing.T) {
	identitiesJSON := `[{"dateCreated":"1753134442462","userId":"<EMAIL>","providerName":"Okta","providerType":"SAML","issuer":"http://www.okta.com/exk1wx7pr3rQqDomN1d8","primary":"true"}]`
	
	issuer, err := parseIdentitiesForIssuer(identitiesJSON)
	if err != nil {
		t.Fatalf("Failed to parse issuer: %v", err)
	}
	
	expected := "http://www.okta.com/exk1wx7pr3rQqDomN1d8"
	if issuer != expected {
		t.Errorf("Expected issuer %s, got %s", expected, issuer)
	}
	
	t.Logf("Successfully parsed issuer: %s", issuer)
}

func TestGetOrgFromIssuer(t *testing.T) {
	os.Setenv("OKTA_ORG_MAPPING", `{"http://www.okta.com/exk1wx7pr3rQqDomN1d8": "1"}`)
	
	orgID, err := getOrgFromIssuer("http://www.okta.com/exk1wx7pr3rQqDomN1d8")
	if err != nil {
		t.Fatalf("Failed to get org from issuer: %v", err)
	}
	
	if orgID != 1 {
		t.Errorf("Expected org ID 1, got %d", orgID)
	}
	
	t.Logf("Successfully mapped issuer to org ID: %d", orgID)
}
