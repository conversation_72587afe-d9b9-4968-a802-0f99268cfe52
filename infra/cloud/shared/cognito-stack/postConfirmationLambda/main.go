package main

import (
	clients "common/clients/services"
	"common/utils"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	assetpb "proto/hero/assets/v2"
	orgspb "proto/hero/orgs/v1"
	permspb "proto/hero/permissions/v1"
	"slices"
	"strconv"
	"strings"

	"connectrpc.com/connect"

	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/cognitoidentityprovider"
)

// CognitoUserEvent represents the event passed to the Lambda function
type CognitoUserEvent struct {
	Version       string `json:"version"`
	TriggerSource string `json:"triggerSource"`
	Region        string `json:"region"`
	UserName      string `json:"userName"`
	Sub           string `json:"sub"`
	UserPoolID    string `json:"userPoolId"`
	CallerContext struct {
		ClientID string `json:"clientId"`
	} `json:"callerContext"`
	Request struct {
		UserAttributes map[string]string `json:"userAttributes"`
	} `json:"request"`
}

// EmailDomainConfig holds configuration for each email domain
type EmailDomainConfig struct {
	OrgID string
}

// parseIdentitiesForIssuer extracts issuer from identities JSON
func parseIdentitiesForIssuer(identitiesJSON string) (string, error) {
	var identities []map[string]interface{}
	err := json.Unmarshal([]byte(identitiesJSON), &identities)
	if err != nil {
		return "", err
	}

	if len(identities) > 0 {
		if issuer, exists := identities[0]["issuer"]; exists {
			if issuerStr, ok := issuer.(string); ok {
				return issuerStr, nil
			}
			return "", fmt.Errorf("issuer field is not a string type")
		}
	}
	return "", fmt.Errorf("issuer not found in identities")
}

// getOrgFromIssuer maps issuer to org ID using JSON configuration
func getOrgFromIssuer(issuer string) (int32, error) {
	oktaOrgMappingJSON := os.Getenv("OKTA_ORG_MAPPING")
	if oktaOrgMappingJSON == "" {
		return 0, fmt.Errorf("OKTA_ORG_MAPPING environment variable not set")
	}

	var orgMapping map[string]string
	err := json.Unmarshal([]byte(oktaOrgMappingJSON), &orgMapping)
	if err != nil {
		return 0, fmt.Errorf("failed to parse OKTA_ORG_MAPPING JSON: %v", err)
	}

	orgIDStr, exists := orgMapping[issuer]
	if !exists {
		return 0, fmt.Errorf("unknown issuer: %s", issuer)
	}

	orgID, err := strconv.Atoi(orgIDStr)
	if err != nil {
		return 0, fmt.Errorf("invalid org ID %s for issuer %s: %v", orgIDStr, issuer, err)
	}

	return int32(orgID), nil
}

func handler(ctx context.Context, event CognitoUserEvent) (CognitoUserEvent, error) {
	log.Printf("Received event: %+v\n", event)
	log.Printf("TriggerSource: %s", event.TriggerSource)

	authSecretARN := os.Getenv("AUTH_SECRET_ARN")
	if authSecretARN == "" {
		return event, fmt.Errorf("AUTH_SECRET_ARN environment variable not set")
	}
	botSecret, err := utils.GetSecret(authSecretARN)
	if err != nil {
		return event, fmt.Errorf("failed to get bot secret: %w", err)
	}

	// Only run user setup logic for initial signup, not password reset
	// Common trigger sources: "PostConfirmation_ConfirmSignUp" and "PostConfirmation_ConfirmForgotPassword"
	if event.TriggerSource == "PostConfirmation_ConfirmForgotPassword" {
		log.Printf("Skipping user setup for password reset trigger: %s", event.TriggerSource)
		return event, nil
	}

	orgsServiceURL := os.Getenv("ORGS_SERVICE_URL")
	orgsClient := clients.NewOrgsClient(
		orgsServiceURL,
		clients.AuthHeaderSecretKeyInterceptor(botSecret))

	// list orgs
	orgs, err := orgsClient.ListOrgs(ctx, connect.NewRequest(&orgspb.ListOrgsRequest{}))
	if err != nil {
		return event, err
	}

	// create a mapping of domain to org id
	domainToOrgID := make(map[string]int32)
	for _, org := range orgs.Msg.Orgs {
		for _, domain := range org.Domains {
			domainToOrgID[domain] = org.Id
		}
	}

	availableOrgIds := []int32{}
	for _, org := range orgs.Msg.Orgs {
		availableOrgIds = append(availableOrgIds, org.Id)
	}

	sub := event.Request.UserAttributes["sub"]
	userEmail := event.Request.UserAttributes["email"]
	userEmailPrefix := ""
	userEmailDomain := ""
	orgID := int32(0)

	// First try to get org from SAML issuer
	identitiesJSON := event.Request.UserAttributes["identities"]
	if identitiesJSON != "" {
		issuer, err := parseIdentitiesForIssuer(identitiesJSON)
		if err != nil {
			log.Printf("Error parsing issuer: %v", err)
		} else {
			log.Printf("Extracted issuer: %s", issuer)
			orgIDFromIssuer, err := getOrgFromIssuer(issuer)
			if err != nil {
				log.Printf("Error mapping issuer to org: %v", err)
			} else {
				orgID = orgIDFromIssuer
				log.Printf("Mapped to org ID: %d", orgID)
			}
		}
	}

	// Fallback to email domain mapping if issuer-based routing failed
	if orgID == 0 && userEmail != "" {
		if atIndex := strings.Index(userEmail, "@"); atIndex != -1 {
			userEmailPrefix = userEmail[:atIndex]
			userEmailDomain = userEmail[atIndex+1:] // +1 to skip @
			orgID = domainToOrgID[userEmailDomain]
		}
		// allow @gethero.com emails to choose their own org (for testing purposes)
		// example: johndoe+org:<EMAIL> should get org:2
		if userEmailDomain == "gethero.com" {
			if orgIndex := strings.Index(userEmailPrefix, "+org"); orgIndex != -1 {
				requestedOrgID := userEmailPrefix[orgIndex+4:]
				requestedOrgIDInt, err := strconv.Atoi(requestedOrgID)
				if err != nil {
					return event, fmt.Errorf("invalid organization ID format: %s", requestedOrgID)
				}
				// if the requested org id is in the list of orgs, use it
				if slices.Contains(availableOrgIds, int32(requestedOrgIDInt)) {
					orgID = int32(requestedOrgIDInt)
				} else {
					return event, fmt.Errorf("invalid organization ID: %s", requestedOrgID)
				}
				log.Printf("injecting responder asset for orgID: %d\n", orgID)
			}
		}
	}

	// Set userEmailPrefix if not already set
	if userEmailPrefix == "" && userEmail != "" {
		if atIndex := strings.Index(userEmail, "@"); atIndex != -1 {
			userEmailPrefix = userEmail[:atIndex]
		}
	}

	assetServiceURL := os.Getenv("WORKFLOW_SERVICE_URL")
	assetClient := clients.NewAssetsClient(
		assetServiceURL,
		clients.AuthHeaderSecretKeyInterceptor(botSecret))

	permsServiceURL := os.Getenv("PERMS_SERVICE_URL")
	permsClient := clients.NewPermissionClient(
		permsServiceURL,
		clients.AuthHeaderSecretKeyInterceptor(botSecret))

	// Check for pre-registration mapping to determine asset type
	var assetType assetpb.AssetType

	mappingResp, err := orgsClient.GetPreRegistrationMapping(ctx, connect.NewRequest(&orgspb.GetPreRegistrationMappingRequest{
		Email: userEmail,
		OrgId: orgID,
	}))

	if err == nil && mappingResp.Msg.Mapping != nil {
		// Use pre-registered asset type
		assetType = mappingResp.Msg.Mapping.AssetType
		log.Printf("Found pre-registration mapping for %s: assetType=%v", userEmail, assetType)

		// Mark as used
		_, markErr := orgsClient.MarkMappingAsUsed(ctx, connect.NewRequest(&orgspb.MarkMappingAsUsedRequest{
			MappingId: mappingResp.Msg.Mapping.Id,
		}))
		if markErr != nil {
			log.Printf("Warning: failed to mark mapping as used: %v", markErr)
		}
	} else {
		// Fallback to default asset type
		defaultAssetTypeStr := os.Getenv("DEFAULT_ASSET_TYPE")
		if defaultAssetTypeStr != "" {
			if assetTypeInt, parseErr := strconv.Atoi(defaultAssetTypeStr); parseErr == nil {
				assetType = assetpb.AssetType(assetTypeInt)
			} else {
				log.Printf("Warning: invalid DEFAULT_ASSET_TYPE '%s', using RESPONDER", defaultAssetTypeStr)
				assetType = assetpb.AssetType_ASSET_TYPE_RESPONDER
			}
		} else {
			assetType = assetpb.AssetType_ASSET_TYPE_RESPONDER // fallback default
		}
		log.Printf("No pre-registration mapping found for %s, using default assetType=%v", userEmail, assetType)
	}

	// Get permission role from environment variable (separate from asset type)
	userPoolID := os.Getenv("USER_POOL_ID")
	permissionRoleName := os.Getenv("DEFAULT_ROLE_NAME") // For permission assignment
	if permissionRoleName == "" {
		log.Println("No DEFAULT_ROLE_NAME found, skipping permission role assignment")
		return event, nil
	}

	// Create Cognito service client
	sess := session.Must(session.NewSession())
	svc := cognitoidentityprovider.New(sess)

	// Assign user to the appropriate organization
	orgGroupName := "org:" + strconv.Itoa(int(orgID))
	_, err = svc.AdminAddUserToGroup(&cognitoidentityprovider.AdminAddUserToGroupInput{
		UserPoolId: aws.String(userPoolID),
		Username:   aws.String(event.UserName),
		GroupName:  aws.String(orgGroupName),
	})
	if err != nil {
		log.Printf("Error adding user to org group: %v", err)
		return event, err
	}

	// Assign user to the permission role
	_, err = permsClient.AddCognitoUserToRole(ctx, connect.NewRequest(&permspb.AddCognitoUserToRoleRequest{
		RoleName:      permissionRoleName,
		CognitoSubId:  sub,
		OrgIdOverride: orgID,
	}))
	if err != nil {
		log.Printf("Error assigning permission role '%s': %v", permissionRoleName, err)
		return event, err
	}

	// Create an asset (our internal user record) with the specified asset type
	_, err = assetClient.CreateAsset(
		ctx,
		connect.NewRequest(&assetpb.CreateAssetRequest{
			Asset: &assetpb.Asset{
				Type:          assetType,
				Name:          userEmailPrefix,
				CognitoJwtSub: sub,
				OrgId:         orgID,
			},
		}),
	)
	if err != nil {
		log.Printf("Error creating asset with type '%v': %v", assetType, err)
		return event, err
	}

	log.Printf("User %s successfully added to permission role '%s' and created as asset type '%v'", event.UserName, permissionRoleName, assetType)
	return event, nil
}

func main() {
	lambda.Start(handler)
}
