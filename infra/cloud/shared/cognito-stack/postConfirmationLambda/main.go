package main

import (
	clients "common/clients/services"
	"common/utils"
	"context"
	"fmt"
	"log"
	"os"
	assetpb "proto/hero/assets/v2"
	orgspb "proto/hero/orgs/v1"
	permspb "proto/hero/permissions/v1"
	"slices"
	"strconv"
	"strings"

	"connectrpc.com/connect"

	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/cognitoidentityprovider"
)

// CognitoUserEvent represents the event passed to the Lambda function
type CognitoUserEvent struct {
	Version       string `json:"version"`
	TriggerSource string `json:"triggerSource"`
	Region        string `json:"region"`
	UserName      string `json:"userName"`
	Sub           string `json:"sub"`
	UserPoolID    string `json:"userPoolId"`
	CallerContext struct {
		ClientID string `json:"clientId"`
	} `json:"callerContext"`
	Request struct {
		UserAttributes map[string]string `json:"userAttributes"`
	} `json:"request"`
}

// EmailDomainConfig holds configuration for each email domain
type EmailDomainConfig struct {
	OrgID string
}

func handler(ctx context.Context, event CognitoUserEvent) (CognitoUserEvent, error) {
	log.Printf("Received event: %+v\n", event)
	log.Printf("TriggerSource: %s", event.TriggerSource)

	authSecretARN := os.Getenv("AUTH_SECRET_ARN")
	if authSecretARN == "" {
		return event, fmt.Errorf("AUTH_SECRET_ARN environment variable not set")
	}
	botSecret, err := utils.GetSecret(authSecretARN)
	if err != nil {
		return event, fmt.Errorf("failed to get bot secret: %w", err)
	}

	// Only run user setup logic for initial signup, not password reset
	// Common trigger sources: "PostConfirmation_ConfirmSignUp" and "PostConfirmation_ConfirmForgotPassword"
	if event.TriggerSource == "PostConfirmation_ConfirmForgotPassword" {
		log.Printf("Skipping user setup for password reset trigger: %s", event.TriggerSource)
		return event, nil
	}

	orgsServiceURL := os.Getenv("ORGS_SERVICE_URL")
	orgsClient := clients.NewOrgsClient(
		orgsServiceURL,
		clients.AuthHeaderSecretKeyInterceptor(botSecret))

	// list orgs
	orgs, err := orgsClient.ListOrgs(ctx, connect.NewRequest(&orgspb.ListOrgsRequest{}))
	if err != nil {
		return event, err
	}

	// create a mapping of domain to org id
	domainToOrgID := make(map[string]int32)
	for _, org := range orgs.Msg.Orgs {
		for _, domain := range org.Domains {
			domainToOrgID[domain] = org.Id
		}
	}

	availableOrgIds := []int32{}
	for _, org := range orgs.Msg.Orgs {
		availableOrgIds = append(availableOrgIds, org.Id)
	}

	sub := event.Request.UserAttributes["sub"]
	userEmail := event.Request.UserAttributes["email"]
	userEmailPrefix := ""
	userEmailDomain := ""
	orgID := int32(0)
	if userEmail != "" {
		if atIndex := strings.Index(userEmail, "@"); atIndex != -1 {
			userEmailPrefix = userEmail[:atIndex]
			userEmailDomain = userEmail[atIndex+1:] // +1 to skip @
			orgID = domainToOrgID[userEmailDomain]
		}
		// allow @gethero.com emails to choose their own org (for testing purposes)
		// example: johndoe+org:<EMAIL> should get org:2
		if userEmailDomain == "gethero.com" {
			if orgIndex := strings.Index(userEmailPrefix, "+org"); orgIndex != -1 {
				requestedOrgID := userEmailPrefix[orgIndex+4:]
				requestedOrgIDInt, err := strconv.Atoi(requestedOrgID)
				if err != nil {
					return event, fmt.Errorf("invalid organization ID format: %s", requestedOrgID)
				}
				// if the requested org id is in the list of orgs, use it
				if slices.Contains(availableOrgIds, int32(requestedOrgIDInt)) {
					orgID = int32(requestedOrgIDInt)
				} else {
					return event, fmt.Errorf("invalid organization ID: %s", requestedOrgID)
				}
				log.Printf("injecting responder asset for orgID: %s\n", orgID)
			}
		}
	}

	assetServiceURL := os.Getenv("WORKFLOW_SERVICE_URL")
	assetClient := clients.NewAssetsClient(
		assetServiceURL,
		clients.AuthHeaderSecretKeyInterceptor(botSecret))

	permsServiceURL := os.Getenv("PERMS_SERVICE_URL")
	permsClient := clients.NewPermissionClient(
		permsServiceURL,
		clients.AuthHeaderSecretKeyInterceptor(botSecret))

	// Get environment variables
	userPoolID := os.Getenv("USER_POOL_ID")
	internalRoleName := os.Getenv("RESPONDER_ROLE_NAME")
	role := internalRoleName
	if role == "" {
		log.Println("No matching role found, skipping user assignment")
		return event, nil
	}

	// Create Cognito service client
	sess := session.Must(session.NewSession())
	svc := cognitoidentityprovider.New(sess)

	// Assign user to the appropriate organization
	orgGroupName := "org:" + strconv.Itoa(int(orgID))
	_, err = svc.AdminAddUserToGroup(&cognitoidentityprovider.AdminAddUserToGroupInput{
		UserPoolId: aws.String(userPoolID),
		Username:   aws.String(event.UserName),
		GroupName:  aws.String(orgGroupName),
	})
	if err != nil {
		log.Printf("Error adding user to org group: %v", err)
		return event, err
	}

	if role == internalRoleName {
		// Assign user to the appropriate role// Assign user to the appropriate role
		_, err = permsClient.AddCognitoUserToRole(ctx, connect.NewRequest(&permspb.AddCognitoUserToRoleRequest{
			RoleName:      role,
			CognitoSubId:  sub,
			OrgIdOverride: orgID,
		}))
		if err != nil {
			log.Printf("Error assigning role: %v", err)
			return event, err
		}
		// create an asset (our internal user record) in the assets service
		_, err = assetClient.CreateAsset(
			ctx,
			connect.NewRequest(&assetpb.CreateAssetRequest{
				Asset: &assetpb.Asset{
					Type:          assetpb.AssetType_ASSET_TYPE_RESPONDER,
					Name:          userEmailPrefix,
					CognitoJwtSub: sub,
					OrgId:         orgID,
				},
			}),
		)
		if err != nil {
			log.Printf("Error creating asset for user: %v", err)
			return event, err
		}
	}

	if err != nil {
		log.Printf("Error adding user to group: %v", err)
		return event, err
	}

	log.Printf("User %s successfully added to role %s", event.UserName, role)
	return event, nil
}

func main() {
	lambda.Start(handler)
}
