package main

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
)

func TestWithMockServices(t *testing.T) {
	// Create mock servers
	orgsServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/hero.orgs.v1.OrgsService/ListOrgs" {
			// Mock ListOrgs response
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(200)
			fmt.Fprint(w, `{
				"orgs": [
					{
						"id": 1,
						"name": "Test Org",
						"domains": ["gethero.com"]
					}
				]
			}`)
		} else if r.URL.Path == "/hero.orgs.v1.OrgsService/GetPreRegistrationMapping" {
			// Mock pre-registration mapping response
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(200)
			fmt.Fprint(w, `{
				"mapping": {
					"id": "test-id",
					"email": "<EMAIL>",
					"orgId": 1,
					"assetTypeLabel": "Supervisor",
					"assetType": 6
				}
			}`)
		} else if r.URL.Path == "/hero.orgs.v1.OrgsService/MarkMappingAsUsed" {
			// Mock mark as used response
			w.WriteHeader(200)
			fmt.Fprint(w, `{}`)
		}
	}))
	defer orgsServer.Close()

	workflowServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/hero.assets.v2.AssetRegistryService/CreateAsset" {
			// Mock CreateAsset response
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(200)
			fmt.Fprint(w, `{
				"asset": {
					"id": "test-asset-id",
					"name": "alec",
					"type": 6,
					"cognitoJwtSub": "e8517370-00d1-70a2-4e3d-69c68fb591b7",
					"orgId": 1
				}
			}`)
		}
	}))
	defer workflowServer.Close()

	permsServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/hero.permissions.v1.PermissionService/AddCognitoUserToRole" {
			// Mock AddCognitoUserToRole response
			w.WriteHeader(200)
			fmt.Fprint(w, `{}`)
		}
	}))
	defer permsServer.Close()

	// Set environment variables to point to mock servers
	os.Setenv("ORGS_SERVICE_URL", orgsServer.URL)
	os.Setenv("WORKFLOW_SERVICE_URL", workflowServer.URL)
	os.Setenv("PERMS_SERVICE_URL", permsServer.URL)
	os.Setenv("AUTH_SECRET_ARN", "mock-secret")
	os.Setenv("USER_POOL_ID", "us-west-2_88f247NGw")
	os.Setenv("DEFAULT_ROLE_NAME", "Responder")
	os.Setenv("DEFAULT_ASSET_TYPE", "3")
	os.Setenv("OKTA_ORG_MAPPING", `{"http://www.okta.com/exk1wx7pr3rQqDomN1d8": "1"}`)

	// Create test event
	event := CognitoUserEvent{
		Version:       "1",
		TriggerSource: "PostConfirmation_ConfirmSignUp",
		Region:        "us-west-2",
		UserName:      "<EMAIL>",
		Request: struct {
			UserAttributes map[string]string `json:"userAttributes"`
		}{
			UserAttributes: map[string]string{
				"email":      "<EMAIL>",
				"identities": `[{"issuer":"http://www.okta.com/exk1wx7pr3rQqDomN1d8","providerName":"Okta"}]`,
				"sub":        "e8517370-00d1-70a2-4e3d-69c68fb591b7",
			},
		},
	}

	// Run the handler
	ctx := context.Background()
	result, err := handler(ctx, event)

	if err != nil {
		t.Fatalf("Handler failed: %v", err)
	}

	t.Logf("Handler succeeded: %+v", result)
	t.Log("✅ Lambda would successfully process this event!")
}
