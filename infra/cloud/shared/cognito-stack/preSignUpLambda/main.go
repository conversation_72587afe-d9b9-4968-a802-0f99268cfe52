package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"

	clients "common/clients/services"
	"common/utils"
	orgs "proto/hero/orgs/v1"

	"connectrpc.com/connect"
	"github.com/aws/aws-lambda-go/lambda"
)

// CognitoPreSignupEvent represents the event passed to the Lambda function
type CognitoPreSignupEvent struct {
	Version       string `json:"version"`
	TriggerSource string `json:"triggerSource"`
	Region        string `json:"region"`
	UserName      string `json:"userName"`
	UserPoolID    string `json:"userPoolId"`
	CallerContext struct {
		ClientID string `json:"clientId"`
	} `json:"callerContext"`
	Request struct {
		UserAttributes map[string]string `json:"userAttributes"`
	} `json:"request"`
	Response struct {
		AutoConfirmUser bool `json:"autoConfirmUser"`
		AutoVerifyEmail bool `json:"autoVerifyEmail"`
	} `json:"response"`
}

func handler(ctx context.Context, event CognitoPreSignupEvent) (CognitoPreSignupEvent, error) {
	log.Printf("Received event: %+v\n", event)

	authSecretARN := os.Getenv("AUTH_SECRET_ARN")
	if authSecretARN == "" {
		return event, fmt.Errorf("AUTH_SECRET_ARN environment variable not set")
	}
	botSecret, err := utils.GetSecret(authSecretARN)
	if err != nil {
		return event, fmt.Errorf("failed to get bot secret: %w", err)
	}

	// Extract email from user attributes
	email, exists := event.Request.UserAttributes["email"]
	if !exists {
		err := fmt.Errorf("email attribute is missing")
		log.Println(err)
		return event, err
	}

	orgsServiceURL := os.Getenv("ORGS_SERVICE_URL")
	orgsClient := clients.NewOrgsClient(
		orgsServiceURL,
		clients.AuthHeaderSecretKeyInterceptor(botSecret))

	// list orgs
	orgs, err := orgsClient.ListOrgs(ctx, connect.NewRequest(&orgs.ListOrgsRequest{}))
	if err != nil {
		return event, err
	}

	// create a mapping of domain to org id
	domainToOrgID := make(map[string]int32)
	for _, org := range orgs.Msg.Orgs {
		for _, domain := range org.Domains {
			domainToOrgID[domain] = org.Id
		}
	}

	userEmailDomain := strings.Split(email, "@")[1]
	orgID, ok := domainToOrgID[userEmailDomain]
	if !ok {
		err := fmt.Errorf("signup is not allowed for this email domain")
		log.Println(err)
		return event, err
	}

	// Allow signup to proceed
	event.Response.AutoConfirmUser = true
	event.Response.AutoVerifyEmail = true

	log.Printf("User %s with email %s is allowed to sign up and will be added to org %s at postConfirmation", event.UserName, email, orgID)
	return event, nil
}

func main() {
	lambda.Start(handler)
}
