import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AppConfig } from '../cloud-config/config';
import { ReactFrontendCloudFrontStack } from './apps/react-frontend-stack';
import { LambdasStack } from './lambdas/lambdas-stack';
import { OpenFgaFargateServiceStack } from './openfga/openfga-stack';
import { S3Stack } from './s3bucket/s3-stack';
import { defineServers } from './servers/servers';
import { BastionStack } from './shared/bastion-stack';
import { CognitoStack } from './shared/cognito-stack/cognito-stack';
import { PostgresRdsStack } from './shared/db-stack';
import { GlobalCertsStack } from './shared/global-certs-stack';
import { GreengrassSetupStack } from './shared/greengrass-stack';
import { LocalCertsStack } from './shared/local-certs-stack';
import { PasswordStack } from './shared/password-stack';
import { SharedStacks } from './shared/shared-config';
import { SnsStack } from './shared/sns/sns-stack';
import { SSMParametersStack } from './shared/ssm-parameters-stack';
import { VPCStack } from './shared/vpc-stack';

export interface EntryStackProps extends cdk.StackProps {
  config: AppConfig;
}

export class EntryStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: EntryStackProps) {
    super(scope, id, props);

    const { config } = props;

    const vpcStack = new VPCStack(scope, 'VPCStack', {
      cidr: config.vpc?.cidr,
      env: props.env,
      crossRegionReferences: true
    });

    const certStack = new GlobalCertsStack(scope, 'GlobalCertsStack', {
      config,
      env: { account: props.env?.account, region: 'us-east-1' }, // Certificate must be in us-east-1 for CloudFront
      crossRegionReferences: true
    });

    const localCertStack = new LocalCertsStack(scope, 'LocalCertsStack', {
      config,
      hostedZone: certStack.hostedZone,
      env: props.env,
      crossRegionReferences: true,
    });

    const cognitoStack = new CognitoStack(scope, 'CognitoStack', {
      cert: certStack.authCert,
      vpcStack: vpcStack,
      config: config,
      hostedZone: certStack.hostedZone,
      crossRegionReferences: true,
      env: props.env
    });

    // Create SSM Parameters stack in the global region
    // Used to pass the cognito information into the Command UI stack build
    const ssmParametersStack = new SSMParametersStack(scope, 'SSMParametersStack', {
      config,
      userPoolId: cognitoStack.userPool.userPoolId,
      internalUserPoolClientId: cognitoStack.internalClient.userPoolClientId,
      internalIdentityPoolId: cognitoStack.internalIdentityPoolId,
      env: { account: props.env?.account, region: 'us-east-1' },
      crossRegionReferences: true
    });

    const s3Stack = new S3Stack(scope, 'S3Stack', { config, env: props.env, crossRegionReferences: true });

    const dbStack = new PostgresRdsStack(scope, 'PostgresRdsStack', {
      dbName: 'hero',
      username: 'server',
      vpc: vpcStack.vpc,
      config: config,
      env: props.env,
      crossRegionReferences: true
    });
    const permsDbStack = new PostgresRdsStack(scope, 'PermsPostgresRdsStack', {
      dbName: 'perms',
      username: 'perms',
      vpc: vpcStack.vpc,
      config: config,
      env: props.env,
      crossRegionReferences: true
    });

    const snsStack = new SnsStack(scope, 'SnsStack', config, { env: props.env, crossRegionReferences: true });

    const passwordStack = new PasswordStack(scope, 'PasswordStack', {
      config,
      env: props.env,
      crossRegionReferences: true
    });

    const sharedStacks: SharedStacks = {
      vpc: vpcStack,
      s3Stack: s3Stack,
      cognitoStack: cognitoStack,
      certStack: certStack,
      localCertStack: localCertStack,
      dbStack: dbStack,
      permsDbStack: permsDbStack,
      snsStack: snsStack,
      passwordStack: passwordStack,
    };

    new BastionStack(scope, 'BastionStack', {
      sharedStacks: sharedStacks,
      config: config,
      env: props.env,
      crossRegionReferences: true
    });

    new GreengrassSetupStack(scope, 'GreengrassStack', { env: props.env, crossRegionReferences: true });

    // NOTE - we deploy this stack to the global region (us-east-1)
    // to ensure that the edge function it contains is deployed as part of the same stack,
    // to avoid cross-region references.
    // This is deemed okay since the primary resources in this stack (cloudfront and lambda@edge) are already global 
    const commandUIStack = new ReactFrontendCloudFrontStack(scope, 'CommandUIStack', {
      sharedStacks: sharedStacks,
      config: config,
      env: { account: props.env?.account, region: 'us-east-1' },
      crossRegionReferences: true
    });
    commandUIStack.addDependency(ssmParametersStack);

    new LambdasStack(scope, 'LambdasStack', {
      sharedStacks,
      config,
      env: props.env,
      crossRegionReferences: true
    });

    new OpenFgaFargateServiceStack(scope, 'OpenFgaStack', {
      sharedStacks: sharedStacks,
      config: config,
      dbUrlSecretName: 'PostgrespermsDBUrl',
      env: props.env,
      crossRegionReferences: true
    });

    defineServers(scope, sharedStacks, config, props.env);
  }
}
