package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	// "os"
	"log"

	clients "common/clients/services"

	"common/utils"
	situationspb "proto/hero/situations/v2"

	"camera-listener/common"

	"connectrpc.com/connect"

	"github.com/aws/aws-lambda-go/events"
	// "github.com/aws/aws-lambda-go/lambda"
	// "github.com/aws/aws-sdk-go/aws"
	// "github.com/aws/aws-sdk-go/aws/credentials"
	// "github.com/aws/aws-sdk-go/aws/session"
	// "github.com/aws/aws-sdk-go/service/sts"
	// "github.com/aws/aws-sdk-go/aws/signer/v4"
	"github.com/aws/aws-lambda-go/lambda"
)

// Potentially remove the .lb once move to prod
const (
	situationsServiceURL = "https://workflow.lb.api.gethero.com"
	assetsServiceURL     = "https://workflow.lb.api.gethero.com"
)

func handler(ctx context.Context, snsEvent events.SNSEvent) error {
	for _, record := range snsEvent.Records {
		snsRecord := record.SNS

		var snsPayload common.SNSPayload
		err := json.Unmarshal([]byte(snsRecord.Message), &snsPayload)
		if err != nil {
			return fmt.Errorf("failed to unmarshal SNS message: %v", err)
		}

		log.Printf("Stream Name: %s, Alert Labels: %v", snsPayload.StreamName, snsPayload.AlertLabels)

		err = pushToSituationService(snsPayload)
		if err != nil {
			return fmt.Errorf("failed to call API: %v", err)
		}
	}
	return nil
}

func mapAlertToSituationType(alertLabels []common.AlertLabel) situationspb.SituationType {
	for _, label := range alertLabels {
		switch label.Label {
		case "Fighting":
			return situationspb.SituationType_SITUATION_TYPE_FIGHT
		case "Knife", "Blade", "Gun", "Weapon":
			return situationspb.SituationType_SITUATION_TYPE_WEAPON_DETECTED
		}
	}
	// Fallback to OTHER if no match is found.
	return situationspb.SituationType_SITUATION_TYPE_OTHER
}

func pushToSituationService(payload common.SNSPayload) error {
	secretARN := os.Getenv("BOT_SECRET_ARN")
	if secretARN == "" {
		return fmt.Errorf("BOT_SECRET_ARN environment variable not set")
	}

	botSecret, err := utils.GetSecret(secretARN)
	if err != nil {
		return fmt.Errorf("failed to get secret: %w", err)
	}

	client := clients.NewSituationsClient(
		situationsServiceURL,
		// TODO in March/April 2025 - replace with a real secret key or find a better way to authenticate
		clients.AuthHeaderSecretKeyInterceptor(botSecret),
	)

	// Prepare additional info JSON that includes the alert labels and producer timestamp.
	additionalInfo := map[string]interface{}{
		"alert_labels":       payload.AlertLabels,
		"producer_timestamp": payload.ProducerTimestamp,
	}
	additionalInfoJSON, err := json.Marshal(additionalInfo)
	if err != nil {
		return fmt.Errorf("failed to marshal additional info: %v", err)
	}

	// Convert StreamName to a reporter ID.
	reporterID, lat, long, err := common.StreamNameToIDLatAndLong(payload.StreamName)
	if err != nil {
		return fmt.Errorf("failed to get asset ID: %v", err)
	}

	// Construct a Situation object.
	situation := &situationspb.Situation{
		ReporterId:         reporterID,
		ReporterName:       payload.StreamName, // Adjust if needed.
		Title:              fmt.Sprintf("Alert from %s", payload.StreamName),
		Type:               mapAlertToSituationType(payload.AlertLabels), // Adjust based on your context.
		Latitude:           lat,
		Longitude:          long,
		Description:        fmt.Sprintf("Situation triggered from alert on %s", payload.StreamName),
		Status:             situationspb.SituationStatus_SITUATION_STATUS_CREATED,
		TriggerSource:      situationspb.TriggerSource_TRIGGER_SOURCE_CAMERA_EVENT,
		AutomationEnabled:  true,
		AdditionalInfoJson: string(additionalInfoJSON),
	}

	req := &situationspb.CreateSituationRequest{
		Situation: situation,
	}

	_, err = client.CreateSituation(context.Background(), connect.NewRequest(req))
	if err != nil {
		log.Println(err)
		return err
	}

	return nil
}

// func mapStringReportTypeToEnum(reportType string) pb.ReportType {
// 	switch reportType {
// 	case "motion_detected":
// 		return pb.ReportType_REPORT_TYPE_EDGE_ALERT
// 	default:
// 		return pb.ReportType_REPORT_TYPE_UNSPECIFIED
// 	}
// }

func main() {
	lambda.Start(handler)
}
