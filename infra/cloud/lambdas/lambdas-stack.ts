import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as snsSubscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import { Construct } from 'constructs';
import * as path from 'path';
import { AppConfig } from '../../cloud-config/config';
import { SharedStacks } from '../shared/shared-config';

export interface LambdasStackProps extends cdk.StackProps {
    sharedStacks: SharedStacks;
    config: AppConfig;
}

export class LambdasStack extends cdk.Stack {
    constructor(scope: Construct, id: string, props: LambdasStackProps) {
        super(scope, id, props);

        const { sharedStacks, config } = props;

        // Use the shared VPC
        const vpc = sharedStacks.vpc.vpc;

        const s3Bucket = sharedStacks.s3Stack.getCameraStreamStorageBucket();
        if (!s3Bucket) {
            throw new Error('Camera stream storage bucket not found');
        }

        const dbSecret = sharedStacks.dbStack.dbSecret;
        if (!dbSecret) {
            throw new Error('DB secret not found');
        }

        const securityGroup = new ec2.SecurityGroup(this, 'LambdaSecurityGroup', {
            vpc,
            description: 'Security group for Lambdas in VPC',
            allowAllOutbound: true
        });

        // ARN of the secret for DB credentials
        const secretArn = dbSecret.secretArn;

        // SNS topic for the Camera events
        const snsTopic = sharedStacks.snsStack.topics['cameraStreamObjectDetectionAlerts'];
        if (!snsTopic) {
            throw new Error('Camera stream object detection alerts topic not found');
        }

        const cameraListenerSecret = secretsmanager.Secret.fromSecretNameV2(
            this,
            'CameraListenerSecret',
            `bots/bot-camera-listener-lambda-secret`
        );

        // SNS-to-FieldReport Lambda
        const snsToSituationLambda = new lambda.Function(this, 'CameraListenerSituationLambda', {
            functionName: 'CameraListenerSituationLambda',
            runtime: lambda.Runtime.PROVIDED_AL2023,
            handler: 'main',
            code: lambda.Code.fromAsset(path.join(__dirname, './camera-listener/sns-to-situation/bootstrap.zip')),
            vpc,
            vpcSubnets: { subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS },
            securityGroups: [securityGroup],
            environment: {
                BOT_SECRET_ARN: cameraListenerSecret.secretArn,
                PARAMETERS_SECRETS_EXTENSION_LOG_LEVEL: 'debug',
                PARAMETERS_SECRETS_EXTENSION_CACHE_SIZE: '10',
                SECRETS_MANAGER_TTL: '300',
            },
            paramsAndSecrets: lambda.ParamsAndSecretsLayerVersion.fromVersion(
                lambda.ParamsAndSecretsVersions.V1_0_103,
                {
                    cacheSize: 10,
                    logLevel: lambda.ParamsAndSecretsLogLevel.DEBUG,
                }
            ),
        });

        // SNS-to-DB Lambda
        const snsToDBLambda = new lambda.Function(this, 'CameraListenerDBLambda', {
            functionName: 'CameraListenerDBLambda',
            runtime: lambda.Runtime.PROVIDED_AL2023,
            handler: 'main',
            code: lambda.Code.fromAsset(path.join(__dirname, './camera-listener/sns-to-db/bootstrap.zip')),
            vpc,
            vpcSubnets: { subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS },
            securityGroups: [securityGroup],
            environment: {
                SECRET_ARN: secretArn,
                DB_SSL_MODE: 'require'
            }
        });

        // SNS-to-S3 Lambda
        const snsToS3Lambda = new lambda.Function(this, 'CameraListenerS3Lambda', {
            functionName: 'CameraListenerS3Lambda',
            runtime: lambda.Runtime.PROVIDED_AL2023,
            handler: 'main',
            code: lambda.Code.fromAsset(path.join(__dirname, './camera-listener/sns-to-s3/bootstrap.zip')),
            vpc,
            vpcSubnets: { subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS },
            securityGroups: [securityGroup],
            timeout: cdk.Duration.seconds(60),
            environment: {
                S3_BUCKET: s3Bucket.bucketName
            }
        });

        // Grant Lambda permissions to AWS Secrets Manager for DB Lambda
        snsToDBLambda.addToRolePolicy(new iam.PolicyStatement({
            actions: ['secretsmanager:GetSecretValue'],
            resources: [secretArn]
        }));

        cameraListenerSecret.grantRead(snsToSituationLambda);

        // Allow Lambda to describe RDS instances
        snsToDBLambda.addToRolePolicy(new iam.PolicyStatement({
            actions: ['rds:DescribeDBInstances', 'rds:DescribeDBClusters'],
            resources: ['*']
        }));

        // Grant IAM permissions for SNS-to-S3 Lambda to access Kinesis Video and S3
        snsToS3Lambda.addToRolePolicy(new iam.PolicyStatement({
            actions: [
                "kinesisvideo:*",
                "kinesisvideoarchivedmedia:*"
            ],
            resources: [
                `arn:aws:kinesisvideo:us-west-2:816069150268:stream/*/*`
            ]
        }));

        snsToS3Lambda.addToRolePolicy(new iam.PolicyStatement({
            actions: ['s3:PutObject'],
            resources: [`${s3Bucket.bucketArn}/*`]
        }));

        // Attach SNS Topic to Lambdas (Subscribe them to receive SNS events)
        snsTopic.addSubscription(new snsSubscriptions.LambdaSubscription(snsToSituationLambda));
        snsTopic.addSubscription(new snsSubscriptions.LambdaSubscription(snsToDBLambda));
        snsTopic.addSubscription(new snsSubscriptions.LambdaSubscription(snsToS3Lambda));
    }
}