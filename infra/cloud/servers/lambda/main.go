package main

import (
	clients "common/clients/services"
	"context"
	"encoding/base64"
	"fmt"
	"log"
	"os"
	orgs "proto/hero/orgs/v1"
	"strings"

	"connectrpc.com/connect"

	"common/utils"
	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
)

type AuthResponse struct {
	Context struct {
		OrgID string `json:"org_id"`
	} `json:"context"`
	PrincipalID    string `json:"principalId"`
	PolicyDocument *struct {
		Version   string `json:"Version"`
		Statement []struct {
			Action   string   `json:"Action"`
			Effect   string   `json:"Effect"`
			Resource []string `json:"Resource"`
		} `json:"Statement"`
	} `json:"policyDocument,omitempty"`
}

// Perms service will handle fine grained permissions, so we allow all resources
var allowedResources []string

func init() {
	resourcesStr := os.Getenv("ALLOWED_RESOURCES")
	if resourcesStr == "" {
		log.Fatalf("FATAL: ALLOWED_RESOURCES environment variable not set.")
	}

	allowedResources = strings.Split(resourcesStr, ",")
}

func generatePolicy(principalID string, effect string, resources []string, orgID int32, username string) events.APIGatewayCustomAuthorizerResponse {
	response := events.APIGatewayCustomAuthorizerResponse{
		PrincipalID: principalID,
		Context: map[string]interface{}{
			"org_id":   orgID,
			"username": username,
		},
	}

	if effect != "" && len(resources) > 0 {
		response.PolicyDocument = events.APIGatewayCustomAuthorizerPolicy{
			Version: "2012-10-17",
			Statement: []events.IAMPolicyStatement{
				{
					Action:   []string{"execute-api:Invoke"},
					Effect:   effect,
					Resource: resources,
				},
			},
		}
	}

	return response
}

func generateAllow(principalID string, resource []string, orgID int32, username string) events.APIGatewayCustomAuthorizerResponse {
	return generatePolicy(principalID, "Allow", resource, orgID, username)
}

func generateDeny(principalID string, resource []string, orgID int32, username string) events.APIGatewayCustomAuthorizerResponse {
	return generatePolicy(principalID, "Deny", resource, orgID, username)
}

func handleRequest(ctx context.Context, event events.APIGatewayCustomAuthorizerRequestTypeRequest) (events.APIGatewayCustomAuthorizerResponse, error) {
	// Get the Authorization header
	authHeader := event.Headers["Authorization"]

	if !strings.HasPrefix(authHeader, "Basic ") {
		response := generateDeny("me", []string{event.MethodArn}, 0, "")
		return response, nil
	}

	// Extract and decode the credentials
	base64Credentials := strings.TrimPrefix(authHeader, "Basic ")
	credentials, err := base64.StdEncoding.DecodeString(base64Credentials)
	if err != nil {
		log.Printf("error decoding credentials: %v", err)
		return events.APIGatewayCustomAuthorizerResponse{}, fmt.Errorf("error decoding credentials: %v", err)
	}

	parts := strings.SplitN(string(credentials), ":", 2)
	if len(parts) != 2 {
		log.Printf("invalid credentials format")
		// log the credentials
		log.Printf("credentials: %v", string(credentials))
		return events.APIGatewayCustomAuthorizerResponse{}, fmt.Errorf("invalid credentials format")
	}

	secretARN := os.Getenv("BOT_SECRET_ARN")
	if secretARN == "" {
		return events.APIGatewayCustomAuthorizerResponse{}, fmt.Errorf("BOT_SECRET_ARN environment variable not set")
	}
	botSecret, err := utils.GetSecret(secretARN)
	if err != nil {
		return events.APIGatewayCustomAuthorizerResponse{}, fmt.Errorf("failed to get secret: %w", err)
	}
	orgsClient := clients.NewOrgsClient(os.Getenv("ORGS_SERVICE_URL"),
		clients.AuthHeaderSecretKeyInterceptor(botSecret))

	resp, err := orgsClient.ValidateOrgCreds(context.Background(), &connect.Request[orgs.ValidateOrgCredsRequest]{
		Msg: &orgs.ValidateOrgCredsRequest{
			Username: parts[0],
			Password: parts[1],
		},
	})
	if err != nil {
		log.Printf("error validating org credentials: %v", err)
		return events.APIGatewayCustomAuthorizerResponse{}, fmt.Errorf("error validating org credentials: %v", err)
	}

	log.Printf("org credentials validated: %v", resp.Msg.Valid)
	// log whole response
	log.Printf("org credentials response: %v", resp)

	if resp.Msg.Valid {
		orgID := resp.Msg.OrgApiUser.OrgId
		username := resp.Msg.OrgApiUser.Username

		// Validate credentials
		response := generateAllow("me", allowedResources, orgID, username)
		log.Printf("request arn: %s – allowed: %+v", event.MethodArn, allowedResources)
		return response, nil
	}

	response := generateDeny("me", []string{event.MethodArn}, 0, "")
	return response, nil
}

func main() {
	lambda.Start(handleRequest)
}
