import * as cdk from 'aws-cdk-lib';
import { execSync } from 'child_process';
import { Construct } from 'constructs';
import { AppConfig } from '../../cloud-config/config';
import { SharedStacks } from '../shared/shared-config';
import { FargateServiceStack } from './fargate-service-stack';
import { ServerConfig } from '../../cloud-config/server-config';


function defineServers(scope: Construct, sharedStacks: SharedStacks, config: AppConfig, env: cdk.StackProps['env']): void {
    const getLatestGitTag = (): string => {
        const tag = execSync(`git tag -l 'v*' | sort -V | tail -n 1`).toString().trim();
        return tag;
    };

    // Allow overriding image tag via CDK context
    const contextImageTag = scope.node.tryGetContext('imageTag');
    const imageTag = contextImageTag || getLatestGitTag();

    if (contextImageTag) {
        console.log('Using context image tag:', contextImageTag);
    } else {
        console.log('Using latest git tag:', imageTag);
    }

    // Generate all server domain names
    const envVars = Object.values(config.servers).reduce((acc, serverConfig_) => {
        const serverConfig = serverConfig_ as ServerConfig;
        const domainName = `https://${serverConfig.serviceName.toLocaleLowerCase()}.lb.api.${config.environment.domain}`;
        acc[`${serverConfig.serviceName.toUpperCase()}_SERVICE_URL`] = domainName;
        return acc;
    }, {} as Record<string, string>);

    if (!env || !env.region) {
        throw new Error('CDK environment or region is not defined.');
    }

    envVars['COGNITO_USER_POOL_ID'] = sharedStacks.cognitoStack.userPool.userPoolId;
    envVars['AWS_REGION'] = env.region;
    envVars['SENTRY_DSN'] = config.sentryDsn;
    envVars['ENVIRONMENT'] = config.environment.envName;

    for (const serverConfig_ of Object.values(config.servers)) {
        const serverConfig = serverConfig_ as ServerConfig;
        new FargateServiceStack(
            scope,
            serverConfig.serviceName,
            {
                serverConfig,
                sharedStacks,
                sharedEnvVars: envVars,
                imageTag,
                config,
                env: env,
                crossRegionReferences: true,
                globalCertsStack: sharedStacks.certStack,
                localCertsStack: sharedStacks.localCertStack,
            }
        );
    }
}

export { defineServers };
