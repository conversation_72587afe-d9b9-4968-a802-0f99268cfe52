import { commandServerConfig } from "./command";
import { filerepositoryServerConfig } from "./filerepository";
import { communicationsServerConfig } from "./communications";
import { orgsServerConfig } from "./orgs";
import { permsServerConfig } from "./perms";
import { sensorsServerConfig } from "./sensors";
import { workflowServerConfig } from "./workflow";
import { ServerConfig } from "../../server-config";

export const servers: Record<string, ServerConfig> = { 
    perms: permsServerConfig,
    orgs: orgsServerConfig,
    workflow: workflowServerConfig,
    command: commandServerConfig,
    sensors: sensorsServerConfig,
    communications: communicationsServerConfig,
    filerepository: filerepositoryServerConfig,
}