{"name": "infra", "version": "0.1.0", "bin": {"deploy-env": "bin/deploy-env.js", "deploy-shared": "bin/deploy-shared.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "bootstrap:secrets": "npx ts-node scripts/bootstrap-secrets.ts"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "22.7.9", "aws-cdk": "2.176.0", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "~5.6.3"}, "dependencies": {"@aws-sdk/client-ecr": "^3.734.0", "@aws-sdk/client-s3": "^3.832.0", "@aws-sdk/client-secrets-manager": "^3.529.0", "aws-cdk-lib": "2.176.0", "cdk-ec2-key-pair": "^4.0.1", "constructs": "^10.0.0"}}