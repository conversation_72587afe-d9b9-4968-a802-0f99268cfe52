package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"bootstrap/internal/config"
	"bootstrap/internal/engine"
)

func main() {
	recipe := flag.String("r", "", "Recipe name to run (e.g., initial_setup)")
	configDir := flag.String("config-dir", "../../config", "Directory containing config.yaml (default: ../../config)")
	env := flag.String("e", "local", "Environment to use (e.g. local or prod)")
	admin := flag.Bool("a", false, "Run as admin")
	flag.Parse()

	// Get the absolute path to the config directory
	absConfigDir, err := filepath.Abs(*configDir)
	if err != nil {
		log.Fatalf("Failed to get absolute path for config directory: %v", err)
	}

	// Check if the config directory exists
	if _, err := os.Stat(absConfigDir); os.IsNotExist(err) {
		log.Fatalf("Config directory does not exist: %s", absConfigDir)
	}

	// Get the recipes directory path
	recipesDir := filepath.Join("../../", "config", "recipes")
	absRecipesDir, err := filepath.Abs(recipesDir)
	if err != nil {
		log.Fatalf("Failed to get absolute path for recipes directory: %v", err)
	}

	// Check if the recipes directory exists
	if _, err := os.Stat(absRecipesDir); os.IsNotExist(err) {
		log.Fatalf("Recipes directory does not exist: %s", absRecipesDir)
	}

	if len(flag.Args()) > 0 && flag.Arg(0) == "validate" {
		// Validate all recipes
		recipes, err := filepath.Glob(filepath.Join(absRecipesDir, "*.yaml"))
		if err != nil {
			log.Fatalf("Failed to read recipes directory: %v", err)
		}

		if len(recipes) == 0 {
			log.Fatalf("No recipe files found in %s", absRecipesDir)
		}

		// Validate each recipe
		for _, recipe := range recipes {
			fmt.Printf("Validating %s...\n", filepath.Base(recipe))
			cfg, err := config.Load(recipe, absConfigDir, *env)
			if err != nil {
				log.Fatalf("Failed to validate %s: %v", filepath.Base(recipe), err)
			}

			eng := engine.New(cfg, *admin)
			if err := eng.Validate(); err != nil {
				log.Fatalf("Validation failed for %s: %v", filepath.Base(recipe), err)
			}
		}
		fmt.Printf("\nAll recipes are valid!\n")
		return
	}

	// If no recipe is specified, show usage
	if *recipe == "" {
		fmt.Println("Usage:")
		fmt.Println("  Run a specific recipe:")
		fmt.Println("    bootstrap -r <recipe_name> [-s <secret_arn>] [-env <environment>]")
		fmt.Println("  Validate all recipes:")
		fmt.Println("    bootstrap validate")
		fmt.Println("\nAvailable recipes:")
		recipes, _ := filepath.Glob(filepath.Join(absRecipesDir, "*.yaml"))
		for _, r := range recipes {
			fmt.Printf("  %s\n", strings.TrimSuffix(filepath.Base(r), ".yaml"))
		}
		os.Exit(1)
	}

	// Load config
	configDirPath := filepath.Join("../../", "config")
	recipePath := filepath.Join(configDirPath, "recipes", fmt.Sprintf("%s.yaml", *recipe))
	cfg, err := config.Load(recipePath, configDirPath, *env)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Check for Cognito credentials in environment variables
	if username := os.Getenv("COGNITO_USERNAME"); username != "" {
		if password := os.Getenv("COGNITO_PASSWORD"); password != "" {
			// Set Cognito credentials in the config
			cfg.CognitoUsername = username
			cfg.CognitoPassword = password
		}
	}

	eng := engine.New(cfg, *admin)
	if err := eng.Run(); err != nil {
		log.Fatalf("Bootstrap failed: %v", err)
	}
	log.Println("Bootstrap completed successfully")
}
