package engine

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"strings"

	"bootstrap/internal/auth"
	"bootstrap/internal/config"
	"common/clients/services"

	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/reflect/protoregistry"
	"google.golang.org/protobuf/types/dynamicpb"
)

// Engine executes the bootstrap steps
type Engine struct {
	config  *config.Config
	clients map[string]interface{} // Stores typed clients (OrgsClient, AssetsClient, etc.)
	state   map[string]interface{}
	authCfg *auth.CognitoConfig
}

// New creates a new bootstrap engine
func New(cfg *config.Config, admin bool) *Engine {
	authCfg := auth.DefaultConfig()

	authCfg.ClientID = cfg.Env.Auth.ClientID
	authCfg.Region = cfg.Env.Region

	// If a secret ARN is provided, fetch the secret from AWS Secrets Manager
	log.Printf("Admin: %v", admin)
	if cfg.Env.Auth.AdminSecretARN != "" && admin {
		secret, err := auth.GetSecret(cfg.Env.Auth.AdminSecretARN, cfg.Env.Region)
		if err != nil {
			log.Fatalf("Failed to fetch secret: %v", err)
		}
		authCfg.Username = secret.Username
		authCfg.Password = secret.Password
	}

	return &Engine{
		config:  cfg,
		clients: make(map[string]interface{}),
		state:   make(map[string]interface{}),
		authCfg: authCfg,
	}
}

// Run executes the bootstrap steps
func (e *Engine) Run() error {

	// Initialize the state map for each step
	for _, step := range e.config.Steps {
		e.state[step.Name] = make(map[string]interface{})
	}

	// Initialize the auth state map
	e.state["auth"] = make(map[string]interface{})

	// Get the token and decode it
	token, err := auth.GetTokenFromEnvOrAuth(e.authCfg)
	if err != nil {
		return fmt.Errorf("failed to get authentication token: %w", err)
	}

	// Decode the JWT token
	claims, err := auth.DecodeJWTToken(token)
	if err != nil {
		return fmt.Errorf("failed to decode JWT token: %w", err)
	}

	// Store the token and claims in the auth state
	authMap := e.state["auth"].(map[string]interface{})
	authMap["token"] = map[string]interface{}{
		"raw":      token,
		"sub":      claims.Sub,
		"username": claims.Username,
		"email":    claims.Email,
	}

	if err := e.Validate(); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	// Execute steps in dependency order
	executed := make(map[string]bool)
	for _, step := range e.config.Steps {
		if err := e.executeStep(context.Background(), &step, executed); err != nil {
			return fmt.Errorf("failed to execute step %s: %w", step.Name, err)
		}
	}

	return nil
}

// getClient gets or creates a client for a service
func (e *Engine) getClient(step *config.Step) (interface{}, error) {
	// Extract service name from RPC string
	parts := strings.Split(step.RPC, ".")
	if len(parts) < 2 {
		return nil, fmt.Errorf("invalid RPC format: %s", step.RPC)
	}
	serviceName := parts[1] // hero.orgs.v1 -> orgs

	// Check if client already exists
	if client, ok := e.clients[serviceName]; ok {
		return client, nil
	}

	// Get endpoint
	endpoint := e.config.GetEndpoint(step)
	if endpoint == "" {
		return nil, fmt.Errorf("no endpoint found for service %s", serviceName)
	}

	// Create the appropriate client based on service name
	var client interface{}
	switch serviceName {
	case "orgs":
		client = services.NewOrgsClient(endpoint)
	case "assets":
		client = services.NewAssetsClient(endpoint)
	case "situations":
		client = services.NewSituationsClient(endpoint)
	case "permissions":
		client = services.NewPermissionClient(endpoint)
	default:
		return nil, fmt.Errorf("unsupported service: %s", serviceName)
	}

	// Store the client
	e.clients[serviceName] = client
	return client, nil
}

// executeStep executes a single step
func (e *Engine) executeStep(ctx context.Context, step *config.Step, executed map[string]bool) error {
	// Check if the step has already been executed
	if executed[step.Name] {
		return nil
	}

	// Execute dependencies first
	for _, dep := range step.DependsOn {
		if !executed[dep] {
			depStep, err := e.findStep(dep)
			if err != nil {
				return fmt.Errorf("failed to find dependency step %s: %v", dep, err)
			}
			if err := e.executeStep(ctx, depStep, executed); err != nil {
				return fmt.Errorf("failed to execute dependency step %s: %v", dep, err)
			}
		}
	}

	// Get the client for this step
	// we don't actualy use this directly, but it loads the proto registry
	_, err := e.getClient(step)
	if err != nil {
		return fmt.Errorf("failed to get client for step %s: %v", step.Name, err)
	}

	// Parse the RPC to get the method name
	parts := strings.Split(step.RPC, "/")
	if len(parts) != 2 {
		return fmt.Errorf("invalid RPC format %q, expected 'package.Service/Method'", step.RPC)
	}
	method := parts[1]

	// Create the request message
	requestMsg, err := e.createRequestMessage(step)
	if err != nil {
		return fmt.Errorf("failed to create request message for step %s: %v", step.Name, err)
	}

	// Invoke the client method
	result, err := e.invokeClientMethod(ctx, method, requestMsg, step.RPC)
	if err != nil {
		return fmt.Errorf("failed to invoke client method for step %s: %v", step.Name, err)
	}

	// Store the result in state
	// Create a map for the step if it doesn't exist
	if _, ok := e.state[step.Name]; !ok {
		e.state[step.Name] = make(map[string]interface{})
	}

	// Store the result in the step's map
	stepMap := e.state[step.Name].(map[string]interface{})

	// If the result has a response field, store it directly
	if response, ok := result["response"]; ok {
		stepMap["response"] = response
	} else {
		// Otherwise, throw an error
		return fmt.Errorf("no response field found in result for step %s", step.Name)
	}

	executed[step.Name] = true

	return nil
}

// invokeClientMethod invokes the appropriate method on the client using HTTP/JSON
func (e *Engine) invokeClientMethod(ctx context.Context, method string, requestMsg proto.Message, rpc string) (map[string]interface{}, error) {
	log.Printf("Invoking method %s on %s", method, rpc)
	// Create a result map to store the response
	result := make(map[string]interface{})

	// Parse the RPC to get service name
	parts := strings.Split(rpc, ".")
	if len(parts) < 2 {
		return nil, fmt.Errorf("invalid RPC format: %s", rpc)
	}
	serviceName := parts[1] // hero.orgs.v1 -> orgs

	// Get the endpoint from the config
	endpoint := e.config.GetEndpoint(&config.Step{RPC: rpc})
	if endpoint == "" {
		return nil, fmt.Errorf("no endpoint found for service %s", serviceName)
	}

	// Convert the request message to JSON
	var requestData map[string]interface{}
	if requestMsg != nil {
		requestReflect := requestMsg.ProtoReflect()
		requestData = make(map[string]interface{})
		requestReflect.Range(func(fd protoreflect.FieldDescriptor, v protoreflect.Value) bool {
			fieldName := string(fd.Name())
			if fd.IsList() {
				// Handle repeated fields
				list := v.List()
				items := make([]interface{}, list.Len())
				for i := 0; i < list.Len(); i++ {
					items[i] = convertValue(list.Get(i), fd)
				}
				requestData[fieldName] = items
			} else {
				requestData[fieldName] = convertValue(v, fd)
			}
			return true
		})
	}

	// Determine if we should use HTTPS based on the port
	scheme := "http"
	if strings.HasSuffix(endpoint, ":443") {
		scheme = "https"
	}

	// Create HTTP request - use the full method path as part of the URL
	url := fmt.Sprintf("%s://%s/%s", scheme, endpoint, rpc)

	// Create HTTP client
	httpClient := &http.Client{}

	// Marshal request data to JSON
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request data: %w", err)
	}

	// Create request
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")

	// Get token from the auth state
	authMap, ok := e.state["auth"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("auth state not found")
	}

	tokenMap, ok := authMap["token"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("token not found in auth state")
	}

	token, ok := tokenMap["raw"].(string)
	if !ok {
		return nil, fmt.Errorf("token raw value not found")
	}

	req.Header.Set("Authorization", "Bearer "+token)

	// Make the request
	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Read the response body first
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Error reading response body: %v", err)
		// We might still be able to decode if only part was read, so continue
	} else {
		// Log the response body as a string
		log.Printf("Response Body: %s", string(bodyBytes))
	}
	// Close the original body now that we've read it
	resp.Body.Close()
	// Replace the body with a new reader so it can be decoded
	resp.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// Parse response
	var responseData map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&responseData); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// Store request and response in result
	result["request"] = requestData
	result["response"] = responseData
	result["status"] = "success"
	result["method"] = method

	return result, nil
}

// convertValue converts a protoreflect.Value to an interface{} based on its type
func convertValue(v protoreflect.Value, fd protoreflect.FieldDescriptor) interface{} {
	switch fd.Kind() {
	case protoreflect.MessageKind:
		msg := v.Message()
		if msg == nil {
			return nil
		}
		// Handle nested messages
		data := make(map[string]interface{})
		msg.Range(func(fd protoreflect.FieldDescriptor, v protoreflect.Value) bool {
			fieldName := string(fd.Name())
			if fd.IsList() {
				list := v.List()
				items := make([]interface{}, list.Len())
				for i := 0; i < list.Len(); i++ {
					items[i] = convertValue(list.Get(i), fd)
				}
				data[fieldName] = items
			} else {
				data[fieldName] = convertValue(v, fd)
			}
			return true
		})
		return data
	case protoreflect.EnumKind:
		return fd.Enum().Values().ByNumber(v.Enum()).Name()
	default:
		return v.Interface()
	}
}

// substituteVariable substitutes a variable reference with its value from the state
func (e *Engine) substituteVariable(path string) (interface{}, error) {
	parts := strings.Split(path, ".")

	// First part is the step name
	stepName := parts[0]

	// Check if the step exists in the state map
	if _, exists := e.state[stepName]; !exists {
		return nil, fmt.Errorf("step %s not found in state", stepName)
	}

	stepMap, ok := e.state[stepName].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("step %s not found in state or not a map", stepName)
	}

	// If there's only one part, return the entire step map
	if len(parts) == 1 {
		return stepMap, nil
	}

	// Special handling for the "auth" step, which doesn't have a "response" field
	if stepName == "auth" {
		// For auth step, navigate directly from the step map
		value := stepMap
		for i := 1; i < len(parts); i++ {
			part := parts[i]

			if i == len(parts)-1 {
				// This is the last part, get the actual value
				if val, ok := value[part]; ok {
					return val, nil
				} else {
					return nil, fmt.Errorf("variable %s not found in state", path)
				}
			} else {
				// This is a path part, navigate to the next level
				if m, ok := value[part].(map[string]interface{}); ok {
					value = m
				} else {
					return nil, fmt.Errorf("path part %s not found or not a map in state", part)
				}
			}
		}
		return nil, fmt.Errorf("empty variable path")
	}

	// For other steps, check if response field exists
	responseField, ok := stepMap["response"]
	if !ok {
		return nil, fmt.Errorf("response field not found in step %s", stepName)
	}

	// Check if response is a map
	responseMap, ok := responseField.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("response field in step %s is not a map", stepName)
	}

	// Navigate through the path
	value := responseMap
	for i := 1; i < len(parts); i++ {
		part := parts[i]

		if i == len(parts)-1 {
			// This is the last part, get the actual value
			if val, ok := value[part]; ok {
				return val, nil
			} else {
				return nil, fmt.Errorf("variable %s not found in state", path)
			}
		} else {
			// This is a path part, navigate to the next level
			if m, ok := value[part].(map[string]interface{}); ok {
				value = m
			} else {
				return nil, fmt.Errorf("path part %s not found or not a map in state", part)
			}
		}
	}
	return nil, fmt.Errorf("empty variable path")
}

// convertValueToProto converts a Go value to a protobuf value
func (e *Engine) convertValueToProto(field protoreflect.FieldDescriptor, value interface{}) (protoreflect.Value, error) {
	// Check if the value is a string that might be a variable reference
	if str, ok := value.(string); ok {
		if strings.HasPrefix(str, "${") && strings.HasSuffix(str, "}") {
			// This is a variable reference, substitute it
			path := strings.TrimSuffix(strings.TrimPrefix(str, "${"), "}")
			substitutedValue, err := e.substituteVariable(path)
			if err != nil {
				return protoreflect.Value{}, fmt.Errorf("failed to substitute variable %s: %w", str, err)
			}
			value = substitutedValue
		}
	}

	// Handle repeated fields
	if field.Cardinality() == protoreflect.Repeated {
		// For repeated fields, we expect a slice
		slice, ok := value.([]interface{})
		if !ok {
			// Try to convert a single value to a slice
			slice = []interface{}{value}
		}

		// Special handling for string lists
		if field.Kind() == protoreflect.StringKind {
			// Create a new dynamic message to get a proper list value
			msg := dynamicpb.NewMessage(field.ContainingMessage())
			list := msg.NewField(field).List()

			// Add each element to the list
			for _, item := range slice {
				if str, ok := item.(string); ok {
					list.Append(protoreflect.ValueOfString(str))
				} else {
					// Try to convert to string
					if str, ok := item.(interface{}); ok {
						list.Append(protoreflect.ValueOfString(fmt.Sprintf("%v", str)))
					}
				}
			}

			return protoreflect.ValueOfList(list), nil
		}

		// Create a new dynamic message to get a proper list value
		msg := dynamicpb.NewMessage(field.ContainingMessage())
		list := msg.NewField(field).List()

		// Add each element to the list
		for _, item := range slice {
			var itemValue protoreflect.Value
			var err error

			switch field.Kind() {
			case protoreflect.StringKind:
				if str, ok := item.(string); ok {
					itemValue = protoreflect.ValueOfString(str)
				} else {
					err = fmt.Errorf("expected string for repeated field, got %T", item)
				}
			case protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind:
				if num, ok := item.(float64); ok {
					itemValue = protoreflect.ValueOfInt32(int32(num))
				} else if str, ok := item.(string); ok {
					// Try to convert string to int32
					num, err := strconv.ParseInt(str, 10, 32)
					if err != nil {
						return protoreflect.Value{}, fmt.Errorf("failed to convert string to int32: %w", err)
					}
					itemValue = protoreflect.ValueOfInt32(int32(num))
				} else if num, ok := item.(int); ok {
					itemValue = protoreflect.ValueOfInt32(int32(num))
				} else if num, ok := item.(int32); ok {
					itemValue = protoreflect.ValueOfInt32(num)
				} else {
					err = fmt.Errorf("expected number for repeated field, got %T", item)
				}
			case protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
				if num, ok := item.(float64); ok {
					itemValue = protoreflect.ValueOfInt64(int64(num))
				} else {
					err = fmt.Errorf("expected number for repeated field, got %T", item)
				}
			case protoreflect.BoolKind:
				if b, ok := item.(bool); ok {
					itemValue = protoreflect.ValueOfBool(b)
				} else {
					err = fmt.Errorf("expected bool for repeated field, got %T", item)
				}
			case protoreflect.EnumKind:
				if str, ok := item.(string); ok {
					enum := field.Enum()
					enumValue := enum.Values().ByName(protoreflect.Name(str))
					if enumValue == nil {
						err = fmt.Errorf("enum value %s not found in enum %s", str, enum.FullName())
					} else {
						itemValue = protoreflect.ValueOfEnum(enumValue.Number())
					}
				} else {
					err = fmt.Errorf("expected string for enum repeated field, got %T", item)
				}
			case protoreflect.MessageKind:
				if m, ok := item.(map[string]interface{}); ok {
					msgType := field.Message()
					msgName := string(msgType.Name())
					msgPackage := string(msgType.FullName().Parent())

					// Get the concrete type for this field
					fullName := protoreflect.FullName(msgPackage + "." + msgName)
					concreteType, err := protoregistry.GlobalTypes.FindMessageByName(fullName)
					if err != nil {
						return protoreflect.Value{}, fmt.Errorf("failed to find concrete type for message: %w", err)
					}

					// Create a new instance of the concrete type
					concreteMsg := concreteType.New()

					// Create a dynamic message for intermediate conversion
					dynamicMsg := dynamicpb.NewMessage(msgType)

					// Set fields in the dynamic message
					for k, v := range m {
						field := msgType.Fields().ByName(protoreflect.Name(k))
						if field == nil {
							return protoreflect.Value{}, fmt.Errorf("field %s not found in message %s", k, msgName)
						}

						val, err := e.convertValueToProto(field, v)
						if err != nil {
							return protoreflect.Value{}, fmt.Errorf("failed to convert value for field %s: %w", k, err)
						}

						dynamicMsg.Set(field, val)
					}

					// Copy fields from dynamic message to concrete message
					proto.Merge(concreteMsg.Interface(), dynamicMsg)

					itemValue = protoreflect.ValueOfMessage(concreteMsg)
				} else {
					err = fmt.Errorf("expected map for message repeated field, got %T", item)
				}
			default:
				err = fmt.Errorf("unsupported field type %v for repeated field", field.Kind())
			}

			if err != nil {
				return protoreflect.Value{}, err
			}

			list.Append(itemValue)
		}

		return protoreflect.ValueOfList(list), nil
	}

	// Handle non-repeated fields
	switch field.Kind() {
	case protoreflect.StringKind:
		if str, ok := value.(string); ok {
			return protoreflect.ValueOfString(str), nil
		}
	case protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind:
		if num, ok := value.(float64); ok {
			return protoreflect.ValueOfInt32(int32(num)), nil
		} else if str, ok := value.(string); ok {
			// Try to convert string to int32
			num, err := strconv.ParseInt(str, 10, 32)
			if err != nil {
				return protoreflect.Value{}, fmt.Errorf("failed to convert string to int32: %w", err)
			}
			return protoreflect.ValueOfInt32(int32(num)), nil
		} else if num, ok := value.(int); ok {
			return protoreflect.ValueOfInt32(int32(num)), nil
		} else if num, ok := value.(int32); ok {
			return protoreflect.ValueOfInt32(num), nil
		}
	case protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
		if num, ok := value.(float64); ok {
			return protoreflect.ValueOfInt64(int64(num)), nil
		} else if str, ok := value.(string); ok {
			// Try to convert string to int64
			num, err := strconv.ParseInt(str, 10, 64)
			if err != nil {
				return protoreflect.Value{}, fmt.Errorf("failed to convert string to int64: %w", err)
			}
			return protoreflect.ValueOfInt64(num), nil
		}
	case protoreflect.BoolKind:
		if b, ok := value.(bool); ok {
			return protoreflect.ValueOfBool(b), nil
		}
	case protoreflect.EnumKind:
		// Handle enum values
		if str, ok := value.(string); ok {
			// Try to find the enum value by name
			enum := field.Enum()
			enumValue := enum.Values().ByName(protoreflect.Name(str))
			if enumValue == nil {
				return protoreflect.Value{}, fmt.Errorf("enum value %s not found in enum %s", str, enum.FullName())
			}
			return protoreflect.ValueOfEnum(enumValue.Number()), nil
		}
	case protoreflect.MessageKind:
		if m, ok := value.(map[string]interface{}); ok {
			// Fix the issue with FullName() and Name() methods
			msgType := field.Message()
			msgName := string(msgType.Name())
			msgPackage := string(msgType.FullName().Parent())

			// Get the concrete type for this field
			fullName := protoreflect.FullName(msgPackage + "." + msgName)
			concreteType, err := protoregistry.GlobalTypes.FindMessageByName(fullName)
			if err != nil {
				return protoreflect.Value{}, err
			}

			// Create a new instance of the concrete type
			concreteMsg := concreteType.New()

			// Create a dynamic message for intermediate conversion
			dynamicMsg := dynamicpb.NewMessage(msgType)

			// Set fields in the dynamic message
			for k, v := range m {
				field := msgType.Fields().ByName(protoreflect.Name(k))
				if field == nil {
					return protoreflect.Value{}, fmt.Errorf("field %s not found in message %s", k, msgName)
				}

				val, err := e.convertValueToProto(field, v)
				if err != nil {
					return protoreflect.Value{}, fmt.Errorf("failed to convert value for field %s: %w", k, err)
				}

				dynamicMsg.Set(field, val)
			}

			// Copy fields from dynamic message to concrete message
			proto.Merge(concreteMsg.Interface(), dynamicMsg)

			return protoreflect.ValueOfMessage(concreteMsg), nil
		}
	}

	return protoreflect.Value{}, fmt.Errorf("unsupported field type %v for value %v", field.Kind(), value)
}

// Validate validates the bootstrap configuration
func (e *Engine) Validate() error {
	// First check if all dependencies exist
	for _, step := range e.config.Steps {
		for _, dep := range step.DependsOn {
			depExists := false
			for _, s := range e.config.Steps {
				if s.Name == dep {
					depExists = true
					break
				}
			}
			if !depExists {
				return fmt.Errorf("dependency %s not found for step %s", dep, step.Name)
			}
		}
	}

	// Check for cycles in dependencies
	visited := make(map[string]bool)
	recStack := make(map[string]bool)

	// Validate each step
	for _, step := range e.config.Steps {
		// Check for circular dependencies
		if err := e.validateDependencies(step, visited, recStack); err != nil {
			return err
		}

		// Validate the payload can be converted to a proto message
		if err := e.validatePayload(step); err != nil {
			return fmt.Errorf("invalid payload for step %s: %w", step.Name, err)
		}

		// Validate parameterized fields in the payload
		if err := e.validateParameterizedFields(step); err != nil {
			return fmt.Errorf("invalid parameterized fields in step %s: %w", step.Name, err)
		}
	}

	return nil
}

// validateDependencies checks for circular dependencies
func (e *Engine) validateDependencies(step config.Step, visited, recStack map[string]bool) error {
	if recStack[step.Name] {
		return fmt.Errorf("circular dependency detected for step %s", step.Name)
	}

	if visited[step.Name] {
		return nil
	}

	visited[step.Name] = true
	recStack[step.Name] = true

	for _, dep := range step.DependsOn {
		var depStep *config.Step
		for _, s := range e.config.Steps {
			if s.Name == dep {
				depStep = &s
				break
			}
		}
		// We already checked that depStep is not nil in the Validate method
		if err := e.validateDependencies(*depStep, visited, recStack); err != nil {
			return err
		}
	}

	recStack[step.Name] = false
	return nil
}

// validatePayload validates that the step's payload can be converted to a proto message
func (e *Engine) validatePayload(step config.Step) error {
	// Parse RPC components
	parts := strings.Split(step.RPC, "/")
	if len(parts) != 2 {
		return fmt.Errorf("invalid RPC format %q, expected 'package.Service/Method'", step.RPC)
	}

	fullService := parts[0]
	method := parts[1]

	serviceParts := strings.Split(fullService, ".")
	if len(serviceParts) < 2 {
		return fmt.Errorf("invalid service format %q, expected at least 'package.Service'", fullService)
	}

	protoPackage := strings.Join(serviceParts[:len(serviceParts)-1], ".")

	// During validation, we only check that the message structure is valid
	// We don't try to resolve variables, as they won't be available until execution time
	// Find the message descriptor
	fullName := protoreflect.FullName(protoPackage + "." + method + "Request")

	desc, err := protoregistry.GlobalTypes.FindMessageByName(fullName)
	if err != nil {
		return fmt.Errorf("failed to find message descriptor: %w", err)
	}

	// We don't need to create the actual message or validate the values
	// Just check that the field names exist in the descriptor
	if step.Payload != nil {
		return e.validatePayloadFields(desc.Descriptor(), step.Payload, method+"Request")
	}

	return nil
}

// validatePayloadFields recursively validates fields in a payload against a message descriptor
func (e *Engine) validatePayloadFields(desc protoreflect.MessageDescriptor, payload map[string]interface{}, context string) error {
	for fieldName, value := range payload {
		// Find the field in the descriptor
		field := desc.Fields().ByName(protoreflect.Name(fieldName))
		if field == nil {
			return fmt.Errorf("field %s not found in message %s", fieldName, context)
		}

		// If the field is a message type and the value is a map, validate the nested fields
		if field.Kind() == protoreflect.MessageKind {
			if nestedMap, ok := value.(map[string]interface{}); ok {
				nestedDesc := field.Message()
				nestedContext := fmt.Sprintf("%s.%s", context, fieldName)
				if err := e.validatePayloadFields(nestedDesc, nestedMap, nestedContext); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

// findStep finds a step by name
func (e *Engine) findStep(name string) (*config.Step, error) {
	for _, step := range e.config.Steps {
		if step.Name == name {
			return &step, nil
		}
	}
	return nil, fmt.Errorf("step %s not found", name)
}

// createRequestMessage creates a request message for a step
func (e *Engine) createRequestMessage(step *config.Step) (proto.Message, error) {
	// Parse RPC components
	parts := strings.Split(step.RPC, "/")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid RPC format %q, expected 'package.Service/Method'", step.RPC)
	}

	fullService := parts[0]
	method := parts[1]

	serviceParts := strings.Split(fullService, ".")
	if len(serviceParts) < 2 {
		return nil, fmt.Errorf("invalid service format %q, expected at least 'package.Service'", fullService)
	}

	protoPackage := strings.Join(serviceParts[:len(serviceParts)-1], ".")

	// Find the message descriptor
	fullName := protoreflect.FullName(protoPackage + "." + method + "Request")

	desc, err := protoregistry.GlobalTypes.FindMessageByName(fullName)
	if err != nil {
		return nil, fmt.Errorf("failed to find message descriptor: %w", err)
	}

	// Create a new message
	msg := dynamicpb.NewMessage(desc.Descriptor())

	// If there's a payload, set the fields
	if step.Payload != nil {
		for fieldName, value := range step.Payload {

			field := desc.Descriptor().Fields().ByName(protoreflect.Name(fieldName))
			if field == nil {
				return nil, fmt.Errorf("field %s not found in message %s", fieldName, method+"Request")
			}

			// Convert the value to the appropriate type
			val, err := e.convertValueToProto(field, value)
			if err != nil {
				return nil, fmt.Errorf("failed to convert value for field %s: %w", fieldName, err)
			}

			msg.Set(field, val)
		}
	}

	return msg, nil
}

// validateParameterizedFields checks that all parameterized fields in a step's payload reference existing steps
func (e *Engine) validateParameterizedFields(step config.Step) error {
	if step.Payload == nil {
		return nil
	}

	// Create a map of all step names for quick lookup
	stepNames := make(map[string]bool)
	for _, s := range e.config.Steps {
		stepNames[s.Name] = true
	}

	// Add special states that are allowed to be referenced
	stepNames["auth"] = true

	// Recursively check all fields in the payload
	return e.validateParameterizedFieldsRecursive(step.Payload, stepNames)
}

// validateParameterizedFieldsRecursive recursively checks all fields in a map for parameterized values
func (e *Engine) validateParameterizedFieldsRecursive(value interface{}, stepNames map[string]bool) error {
	switch v := value.(type) {
	case string:
		// Check if this is a parameterized field
		if strings.HasPrefix(v, "${") && strings.HasSuffix(v, "}") {
			// Extract the path
			path := strings.TrimSuffix(strings.TrimPrefix(v, "${"), "}")
			parts := strings.Split(path, ".")

			// First part is the step name
			stepName := parts[0]

			// Check if the step exists
			if !stepNames[stepName] {
				return fmt.Errorf("parameterized field references non-existing step: %s", stepName)
			}
		}
	case map[string]interface{}:
		// Recursively check all fields in the map
		for _, fieldValue := range v {
			if err := e.validateParameterizedFieldsRecursive(fieldValue, stepNames); err != nil {
				return err
			}
		}
	case []interface{}:
		// Recursively check all elements in the slice
		for _, element := range v {
			if err := e.validateParameterizedFieldsRecursive(element, stepNames); err != nil {
				return err
			}
		}
	}

	return nil
}
