package auth

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"github.com/golang-jwt/jwt"
	"golang.org/x/term"
)

// Secret represents the structure of the secret stored in AWS Secrets Manager
type Secret struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// JWTClaims stores the decoded JWT token claims
type JWTClaims struct {
	jwt.StandardClaims
	Sub           string   `json:"sub"`
	Username      string   `json:"username"`
	Email         string   `json:"email"`
	CognitoGroups []string `json:"cognito:groups"`
}

// KeychainItem represents a credential stored in the macOS keychain
type KeychainItem struct {
	Username string
	Password string
}

// CognitoConfig holds the configuration for Cognito authentication
type CognitoConfig struct {
	UserPoolID      string
	ClientID        string
	Username        string
	Password        string
	Region          string
	CognitoPoolURL  string
	CognitoTokenURL string
}

// DefaultConfig creates a default configuration for Cognito
func DefaultConfig() *CognitoConfig {
	return &CognitoConfig{
		UserPoolID:      "us-east-1_LgmI46n3M",        // Default User Pool ID
		ClientID:        "7m6shsvb25265fvo02v8rf2s4u", // Default Client ID
		Region:          "us-east-1",
		CognitoPoolURL:  "https://cognito-idp.us-east-1.amazonaws.com/",
		CognitoTokenURL: "https://cognito-idp.us-east-1.amazonaws.com/",
	}
}

// AuthenticateUser authenticates the given username and password with Cognito.
// It returns the AccessToken on success.
func AuthenticateUser(cfg *CognitoConfig) (string, error) {
	// Load the AWS configuration.
	awsCfg, err := config.LoadDefaultConfig(context.TODO(), config.WithRegion(cfg.Region))
	if err != nil {
		return "", fmt.Errorf("unable to load SDK config, %v", err)
	}

	// Create a new Cognito Identity Provider client.
	cognitoClient := cognitoidentityprovider.NewFromConfig(awsCfg)

	// Build the authentication parameters.
	input := &cognitoidentityprovider.InitiateAuthInput{
		AuthFlow: "USER_PASSWORD_AUTH",
		AuthParameters: map[string]string{
			"USERNAME": cfg.Username,
			"PASSWORD": cfg.Password,
		},
		ClientId: &cfg.ClientID,
	}

	// Call the InitiateAuth API.
	resp, err := cognitoClient.InitiateAuth(context.TODO(), input)
	if err != nil {
		return "", fmt.Errorf("failed to initiate auth: %w", err)
	}

	// Extract the access token
	if resp.AuthenticationResult == nil || resp.AuthenticationResult.AccessToken == nil {
		return "", fmt.Errorf("authentication result or access token is nil")
	}
	token := *resp.AuthenticationResult.AccessToken

	return token, nil
}

// GetTokenFromEnvOrAuth retrieves a token from environment variables or by authenticating
func GetTokenFromEnvOrAuth(config *CognitoConfig) (string, error) {
	// Try to get the token from environment variables first
	if token := os.Getenv("COGNITO_ACCESS_TOKEN"); token != "" {
		return token, nil
	}

	// Validate that we have required credentials
	if config.ClientID == "" || config.Region == "" {
		return "", fmt.Errorf("missing required Cognito credentials. Set COGNITO_CLIENT_ID and COGNITO_REGION")
	}

	// If credentials are already provided in the config, use them directly
	if config.Username != "" && config.Password != "" {
		return AuthenticateUser(config)
	}

	// Check for credentials in keychain
	var usedKeychainCreds bool
	if keychainCreds, err := getKeychainCredentials(); err == nil && keychainCreds != nil {
		fmt.Print("Found credentials in keychain. Use them? (y/n): ")
		if strings.ToLower(promptForInput("")) == "y" {
			config.Username = keychainCreds.Username
			config.Password = keychainCreds.Password
			usedKeychainCreds = true
		}
	}

	// If username or password are not provided, prompt for them
	if config.Username == "" {
		config.Username = promptForInput("Enter Cognito username: ")
	}

	if config.Password == "" {
		config.Password = promptForPassword("Enter Cognito password: ")
	}

	// Only ask about saving if we didn't get credentials from keychain
	if !usedKeychainCreds {
		fmt.Print("Save credentials to macOS keychain? (y/n): ")
		if strings.ToLower(promptForInput("")) == "y" {
			if err := saveKeychainCredentials(config.Username, config.Password); err != nil {
				fmt.Printf("Warning: Failed to save credentials to keychain: %v\n", err)
			}
		}
	}

	// If no token, authenticate and get a new one
	return AuthenticateUser(config)
}

// DecodeJWTToken decodes a JWT token and returns the claims
func DecodeJWTToken(token string) (*JWTClaims, error) {
	// Split the token into parts
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return nil, fmt.Errorf("invalid token format")
	}

	// Decode the payload
	payload, err := jwt.DecodeSegment(parts[1])
	if err != nil {
		return nil, fmt.Errorf("failed to decode token payload: %w", err)
	}

	// Unmarshal the payload into the claims struct
	var claims JWTClaims
	if err := json.Unmarshal(payload, &claims); err != nil {
		return nil, fmt.Errorf("failed to unmarshal token claims: %w", err)
	}

	return &claims, nil
}

// promptForInput prompts the user for input at the terminal and returns the entered string
func promptForInput(prompt string) string {
	reader := bufio.NewReader(os.Stdin)
	fmt.Print(prompt)
	input, _ := reader.ReadString('\n')
	return strings.TrimSpace(input)
}

// promptForPassword securely prompts the user for a password at the terminal
func promptForPassword(prompt string) string {
	fmt.Print(prompt)
	bytePassword, err := term.ReadPassword(int(os.Stdin.Fd()))
	if err != nil {
		return ""
	}
	fmt.Println() // Add a newline after password input
	return strings.TrimSpace(string(bytePassword))
}

// getKeychainCredentials retrieves Cognito credentials from the macOS keychain
func getKeychainCredentials() (*KeychainItem, error) {
	cmd := exec.Command("security", "find-generic-password", "-w", "-s", "hero-core", "-a", "cognito")
	output, err := cmd.Output()
	if err != nil {
		return nil, nil // Return nil if credentials not found
	}

	// Split the output into username and password
	parts := strings.Split(strings.TrimSpace(string(output)), ":")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid keychain credential format")
	}

	return &KeychainItem{
		Username: parts[0],
		Password: parts[1],
	}, nil
}

// saveKeychainCredentials stores Cognito credentials in the macOS keychain
func saveKeychainCredentials(username, password string) error {
	// First delete any existing credentials
	exec.Command("security", "delete-generic-password", "-s", "hero-core", "-a", "cognito").Run()

	// Create the credential string
	credStr := fmt.Sprintf("%s:%s", username, password)

	// Store in keychain
	cmd := exec.Command("security", "add-generic-password", "-s", "hero-core", "-a", "cognito", "-w", credStr)
	return cmd.Run()
}

// GetSecret fetches a secret from AWS Secrets Manager
func GetSecret(secretARN, region string) (*Secret, error) {
	cfg, err := config.LoadDefaultConfig(context.TODO(), config.WithRegion(region))
	if err != nil {
		return nil, fmt.Errorf("unable to load SDK config, %v", err)
	}

	// Create a new Secrets Manager client
	svc := secretsmanager.NewFromConfig(cfg)

	// Get the secret value
	result, err := svc.GetSecretValue(context.TODO(), &secretsmanager.GetSecretValueInput{
		SecretId: aws.String(secretARN),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get secret value: %w", err)
	}

	// Unmarshal the secret string into the Secret struct
	var secret Secret
	if err := json.Unmarshal([]byte(*result.SecretString), &secret); err != nil {
		return nil, fmt.Errorf("failed to unmarshal secret string: %w", err)
	}

	return &secret, nil
}
