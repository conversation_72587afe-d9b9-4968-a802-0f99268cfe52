package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v3"
)

// ServiceEndpoint represents a service's connection details
type ServiceEndpoint struct {
	Host string `yaml:"host"`
	Port int    `yaml:"port"`
}

// AuthConfig represents the authentication configuration
type AuthConfig struct {
	ClientID       string `yaml:"client_id"`
	AdminSecretARN string `yaml:"admin_secret_arn"`
}

// EnvConfig represents the environment specific configuration
type EnvConfig struct {
	Services map[string]ServiceEndpoint `yaml:"services"`
	Region   string                     `yaml:"region"`
	Auth     AuthConfig                 `yaml:"auth"`
}

// Step represents a single bootstrap step
type Step struct {
	Name        string                 `yaml:"name"`
	Description string                 `yaml:"description"`
	RPC         string                 `yaml:"rpc"` // e.g., "hero.orgs.v1.OrgsService/CreateOrg"
	Payload     map[string]interface{} `yaml:"payload"`
	DependsOn   []string               `yaml:"depends_on"`
}

// parseRPC parses the RPC string into its components
func (s *Step) parseRPC() (protoPackage, service, method string, err error) {
	parts := strings.Split(s.RPC, "/")
	if len(parts) != 2 {
		return "", "", "", fmt.Errorf("invalid RPC format %q, expected 'package.Service/Method'", s.RPC)
	}

	fullService := parts[0]
	method = parts[1]

	serviceParts := strings.Split(fullService, ".")
	if len(serviceParts) < 2 {
		return "", "", "", fmt.Errorf("invalid service format %q, expected at least 'package.Service'", fullService)
	}

	service = serviceParts[len(serviceParts)-1]
	protoPackage = strings.Join(serviceParts[:len(serviceParts)-1], ".")

	return protoPackage, service, method, nil
}

// getServiceName extracts the service name from the proto package
// e.g., "hero.orgs.v1" -> "orgs"
func (s *Step) getServiceName() string {
	parts := strings.Split(s.RPC, ".")
	if len(parts) >= 2 {
		return parts[1] // hero.orgs.v1 -> orgs
	}
	return ""
}

// Config represents the entire bootstrap configuration
type Config struct {
	Steps           []Step    `yaml:"steps"`
	Env             EnvConfig `yaml:"-"`
	CognitoUsername string    `yaml:"-"`
	CognitoPassword string    `yaml:"-"`
}

// GetEndpoint returns the gRPC endpoint for a service
func (c *Config) GetEndpoint(step *Step) string {
	serviceName := step.getServiceName()
	if endpoint, ok := c.Env.Services[serviceName]; ok {
		return fmt.Sprintf("%s:%d", endpoint.Host, endpoint.Port)
	}
	return ""
}

// Load reads a single YAML configuration file and always loads endpoints from a separate folder
func Load(configFile string, configDir string, env string) (*Config, error) {
	var config Config
	config.Env.Services = make(map[string]ServiceEndpoint)

	// First load the env configuration from the separate folder
	envConfigPath := filepath.Join(configDir, fmt.Sprintf("config.%s.yaml", env))
	if data, err := os.ReadFile(envConfigPath); err == nil {
		if err := yaml.Unmarshal(data, &config.Env); err != nil {
			return nil, fmt.Errorf("failed to parse env config: %w", err)
		}
	} else {
		return nil, fmt.Errorf("failed to read env config file: %w", err)
	}

	// Read the main configuration file
	data, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// Parse the YAML
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// Validate RPC format for each step
	for _, step := range config.Steps {
		if _, _, _, err := step.parseRPC(); err != nil {
			return nil, fmt.Errorf("invalid step %q: %w", step.Name, err)
		}
	}

	return &config, nil
}
