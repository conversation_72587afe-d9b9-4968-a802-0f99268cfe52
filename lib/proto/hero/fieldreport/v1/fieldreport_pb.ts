// @generated by protoc-gen-es v2.6.1 with parameter "target=ts"
// @generated from file hero/fieldreport/v1/fieldreport.proto (package hero.fieldreport.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { FieldMask, Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_field_mask, file_google_protobuf_timestamp, file_google_protobuf_wrappers } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/fieldreport/v1/fieldreport.proto.
 */
export const file_hero_fieldreport_v1_fieldreport: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_timestamp, file_google_protobuf_field_mask, file_google_protobuf_wrappers]);

/**
 * @generated from message hero.fieldreport.v1.AddMessageToReportRequest
 */
export type AddMessageToReportRequest = Message<"hero.fieldreport.v1.AddMessageToReportRequest"> & {
  /**
   * @generated from field: hero.fieldreport.v1.TextMessage message = 1;
   */
  message?: TextMessage;
};

/**
 * Describes the message hero.fieldreport.v1.AddMessageToReportRequest.
 * Use `create(AddMessageToReportRequestSchema)` to create a new message.
 */
export const AddMessageToReportRequestSchema: GenMessage<AddMessageToReportRequest> = /*@__PURE__*/
  messageDesc(file_hero_fieldreport_v1_fieldreport, 0);

/**
 * @generated from message hero.fieldreport.v1.AddMessageToReportResponse
 */
export type AddMessageToReportResponse = Message<"hero.fieldreport.v1.AddMessageToReportResponse"> & {
  /**
   * @generated from field: hero.fieldreport.v1.TextMessage message = 1;
   */
  message?: TextMessage;
};

/**
 * Describes the message hero.fieldreport.v1.AddMessageToReportResponse.
 * Use `create(AddMessageToReportResponseSchema)` to create a new message.
 */
export const AddMessageToReportResponseSchema: GenMessage<AddMessageToReportResponse> = /*@__PURE__*/
  messageDesc(file_hero_fieldreport_v1_fieldreport, 1);

/**
 * @generated from message hero.fieldreport.v1.TextMessage
 */
export type TextMessage = Message<"hero.fieldreport.v1.TextMessage"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string sender_id = 3;
   */
  senderId: string;

  /**
   * @generated from field: string report_id = 4;
   */
  reportId: string;

  /**
   * @generated from field: string text = 5;
   */
  text: string;

  /**
   * @generated from field: string type = 6;
   */
  type: string;

  /**
   * @generated from field: google.protobuf.Timestamp create_time = 7;
   */
  createTime?: Timestamp;
};

/**
 * Describes the message hero.fieldreport.v1.TextMessage.
 * Use `create(TextMessageSchema)` to create a new message.
 */
export const TextMessageSchema: GenMessage<TextMessage> = /*@__PURE__*/
  messageDesc(file_hero_fieldreport_v1_fieldreport, 2);

/**
 * CREATE
 *
 * @generated from message hero.fieldreport.v1.CreateFieldReportRequest
 */
export type CreateFieldReportRequest = Message<"hero.fieldreport.v1.CreateFieldReportRequest"> & {
  /**
   * The user (or system) who is creating the FieldReport
   *
   * @generated from field: string created_by = 1;
   */
  createdBy: string;

  /**
   * Type/category of the report (ACCIDENT, ROBBERY, FIRE, etc.).
   *
   * @generated from field: hero.fieldreport.v1.ReportType report_type = 2;
   */
  reportType: ReportType;

  /**
   * Optional location details
   *
   * @generated from field: string location_string = 3;
   */
  locationString: string;

  /**
   * @generated from field: double latitude = 4;
   */
  latitude: number;

  /**
   * @generated from field: double longitude = 5;
   */
  longitude: number;

  /**
   * Flexible text describing the FieldReport
   *
   * @generated from field: string description = 6;
   */
  description: string;

  /**
   * Optional list of tasks associated
   *
   * @generated from field: repeated string task_ids = 7;
   */
  taskIds: string[];

  /**
   * Optional status at creation (could default to OPEN)
   *
   * @generated from field: hero.fieldreport.v1.FieldReportStatus status = 8;
   */
  status: FieldReportStatus;

  /**
   * Will be populated on escalation
   *
   * @generated from field: string situation_id = 9;
   */
  situationId: string;

  /**
   * Optional media attachments (images, videos, etc.)
   * TOOD - consolidate these
   *
   * @generated from field: repeated hero.fieldreport.v1.MediaAttachment media_attachments = 10;
   */
  mediaAttachments: MediaAttachment[];

  /**
   * Optional initial messages
   *
   * @generated from field: repeated hero.fieldreport.v1.TextMessage messages = 11;
   */
  messages: TextMessage[];

  /**
   * Whether this report should bypass automated processing
   *
   * @generated from field: google.protobuf.BoolValue need_manual_review = 12;
   */
  needManualReview?: boolean;

  /**
   * Additional JSON-based details for the report
   *
   * @generated from field: string report_details_json = 13;
   */
  reportDetailsJson: string;

  /**
   * Whether to use reporter’s live location instead of static coords
   *
   * @generated from field: google.protobuf.BoolValue use_reporter_live_location = 14;
   */
  useReporterLiveLocation?: boolean;
};

/**
 * Describes the message hero.fieldreport.v1.CreateFieldReportRequest.
 * Use `create(CreateFieldReportRequestSchema)` to create a new message.
 */
export const CreateFieldReportRequestSchema: GenMessage<CreateFieldReportRequest> = /*@__PURE__*/
  messageDesc(file_hero_fieldreport_v1_fieldreport, 3);

/**
 * @generated from message hero.fieldreport.v1.CreateFieldReportResponse
 */
export type CreateFieldReportResponse = Message<"hero.fieldreport.v1.CreateFieldReportResponse"> & {
  /**
   * @generated from field: hero.fieldreport.v1.FieldReport report = 1;
   */
  report?: FieldReport;
};

/**
 * Describes the message hero.fieldreport.v1.CreateFieldReportResponse.
 * Use `create(CreateFieldReportResponseSchema)` to create a new message.
 */
export const CreateFieldReportResponseSchema: GenMessage<CreateFieldReportResponse> = /*@__PURE__*/
  messageDesc(file_hero_fieldreport_v1_fieldreport, 4);

/**
 * READ (single)
 *
 * @generated from message hero.fieldreport.v1.GetFieldReportRequest
 */
export type GetFieldReportRequest = Message<"hero.fieldreport.v1.GetFieldReportRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;
};

/**
 * Describes the message hero.fieldreport.v1.GetFieldReportRequest.
 * Use `create(GetFieldReportRequestSchema)` to create a new message.
 */
export const GetFieldReportRequestSchema: GenMessage<GetFieldReportRequest> = /*@__PURE__*/
  messageDesc(file_hero_fieldreport_v1_fieldreport, 5);

/**
 * @generated from message hero.fieldreport.v1.GetFieldReportResponse
 */
export type GetFieldReportResponse = Message<"hero.fieldreport.v1.GetFieldReportResponse"> & {
  /**
   * @generated from field: hero.fieldreport.v1.FieldReport report = 1;
   */
  report?: FieldReport;
};

/**
 * Describes the message hero.fieldreport.v1.GetFieldReportResponse.
 * Use `create(GetFieldReportResponseSchema)` to create a new message.
 */
export const GetFieldReportResponseSchema: GenMessage<GetFieldReportResponse> = /*@__PURE__*/
  messageDesc(file_hero_fieldreport_v1_fieldreport, 6);

/**
 * READ (list) - with pagination & optional filtering
 *
 * @generated from message hero.fieldreport.v1.ListFieldReportsRequest
 */
export type ListFieldReportsRequest = Message<"hero.fieldreport.v1.ListFieldReportsRequest"> & {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  /**
   * @generated from field: hero.fieldreport.v1.FieldReportStatus filter_status = 3;
   */
  filterStatus: FieldReportStatus;

  /**
   * @generated from field: hero.fieldreport.v1.ReportType filter_report_type = 4;
   */
  filterReportType: ReportType;

  /**
   * @generated from field: string filter_created_by = 5;
   */
  filterCreatedBy: string;
};

/**
 * Describes the message hero.fieldreport.v1.ListFieldReportsRequest.
 * Use `create(ListFieldReportsRequestSchema)` to create a new message.
 */
export const ListFieldReportsRequestSchema: GenMessage<ListFieldReportsRequest> = /*@__PURE__*/
  messageDesc(file_hero_fieldreport_v1_fieldreport, 7);

/**
 * @generated from message hero.fieldreport.v1.ListFieldReportsResponse
 */
export type ListFieldReportsResponse = Message<"hero.fieldreport.v1.ListFieldReportsResponse"> & {
  /**
   * @generated from field: repeated hero.fieldreport.v1.FieldReport reports = 1;
   */
  reports: FieldReport[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.fieldreport.v1.ListFieldReportsResponse.
 * Use `create(ListFieldReportsResponseSchema)` to create a new message.
 */
export const ListFieldReportsResponseSchema: GenMessage<ListFieldReportsResponse> = /*@__PURE__*/
  messageDesc(file_hero_fieldreport_v1_fieldreport, 8);

/**
 * UPDATE
 *
 * @generated from message hero.fieldreport.v1.UpdateFieldReportRequest
 */
export type UpdateFieldReportRequest = Message<"hero.fieldreport.v1.UpdateFieldReportRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * FieldMask for partial updates (optional usage)
   *
   * @generated from field: google.protobuf.FieldMask update_mask = 2;
   */
  updateMask?: FieldMask;

  /**
   * Fields that may be updated
   *
   * @generated from field: hero.fieldreport.v1.ReportType report_type = 3;
   */
  reportType: ReportType;

  /**
   * @generated from field: string location_string = 4;
   */
  locationString: string;

  /**
   * @generated from field: double latitude = 5;
   */
  latitude: number;

  /**
   * @generated from field: double longitude = 6;
   */
  longitude: number;

  /**
   * @generated from field: string description = 7;
   */
  description: string;

  /**
   * @generated from field: repeated string task_ids = 8;
   */
  taskIds: string[];

  /**
   * @generated from field: hero.fieldreport.v1.FieldReportStatus status = 9;
   */
  status: FieldReportStatus;

  /**
   * @generated from field: string situation_id = 10;
   */
  situationId: string;

  /**
   * @generated from field: repeated hero.fieldreport.v1.MediaAttachment media_attachments = 11;
   */
  mediaAttachments: MediaAttachment[];

  /**
   * The user (or system) performing the update
   *
   * @generated from field: string updated_by = 12;
   */
  updatedBy: string;

  /**
   * @generated from field: google.protobuf.BoolValue need_manual_review = 13;
   */
  needManualReview?: boolean;

  /**
   * @generated from field: string report_details_json = 14;
   */
  reportDetailsJson: string;

  /**
   * @generated from field: google.protobuf.BoolValue use_reporter_live_location = 15;
   */
  useReporterLiveLocation?: boolean;
};

/**
 * Describes the message hero.fieldreport.v1.UpdateFieldReportRequest.
 * Use `create(UpdateFieldReportRequestSchema)` to create a new message.
 */
export const UpdateFieldReportRequestSchema: GenMessage<UpdateFieldReportRequest> = /*@__PURE__*/
  messageDesc(file_hero_fieldreport_v1_fieldreport, 9);

/**
 * @generated from message hero.fieldreport.v1.UpdateFieldReportResponse
 */
export type UpdateFieldReportResponse = Message<"hero.fieldreport.v1.UpdateFieldReportResponse"> & {
  /**
   * @generated from field: hero.fieldreport.v1.FieldReport report = 1;
   */
  report?: FieldReport;
};

/**
 * Describes the message hero.fieldreport.v1.UpdateFieldReportResponse.
 * Use `create(UpdateFieldReportResponseSchema)` to create a new message.
 */
export const UpdateFieldReportResponseSchema: GenMessage<UpdateFieldReportResponse> = /*@__PURE__*/
  messageDesc(file_hero_fieldreport_v1_fieldreport, 10);

/**
 * DELETE
 *
 * @generated from message hero.fieldreport.v1.DeleteFieldReportRequest
 */
export type DeleteFieldReportRequest = Message<"hero.fieldreport.v1.DeleteFieldReportRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;
};

/**
 * Describes the message hero.fieldreport.v1.DeleteFieldReportRequest.
 * Use `create(DeleteFieldReportRequestSchema)` to create a new message.
 */
export const DeleteFieldReportRequestSchema: GenMessage<DeleteFieldReportRequest> = /*@__PURE__*/
  messageDesc(file_hero_fieldreport_v1_fieldreport, 11);

/**
 * @generated from message hero.fieldreport.v1.DeleteFieldReportResponse
 */
export type DeleteFieldReportResponse = Message<"hero.fieldreport.v1.DeleteFieldReportResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message hero.fieldreport.v1.DeleteFieldReportResponse.
 * Use `create(DeleteFieldReportResponseSchema)` to create a new message.
 */
export const DeleteFieldReportResponseSchema: GenMessage<DeleteFieldReportResponse> = /*@__PURE__*/
  messageDesc(file_hero_fieldreport_v1_fieldreport, 12);

/**
 * @generated from message hero.fieldreport.v1.FieldReport
 */
export type FieldReport = Message<"hero.fieldreport.v1.FieldReport"> & {
  /**
   * Unique identifier (assigned by the server)
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * The user (or system) who initially created this FieldReport
   *
   * @generated from field: string created_by = 2;
   */
  createdBy: string;

  /**
   * The user (or system) who last updated this FieldReport
   *
   * @generated from field: string updated_by = 3;
   */
  updatedBy: string;

  /**
   * The type/category of the FieldReport
   *
   * @generated from field: hero.fieldreport.v1.ReportType report_type = 4;
   */
  reportType: ReportType;

  /**
   * Location details
   *
   * @generated from field: string location_string = 5;
   */
  locationString: string;

  /**
   * @generated from field: double latitude = 6;
   */
  latitude: number;

  /**
   * @generated from field: double longitude = 7;
   */
  longitude: number;

  /**
   * Flexible text
   *
   * @generated from field: string description = 8;
   */
  description: string;

  /**
   * Chronological updates or notes (could be appended in an update)
   *
   * @generated from field: repeated string updates = 9;
   */
  updates: string[];

  /**
   * Optional tasks associated
   *
   * @generated from field: repeated string task_ids = 10;
   */
  taskIds: string[];

  /**
   * Current status (OPEN, IN_PROGRESS, CLOSED, etc.)
   *
   * @generated from field: hero.fieldreport.v1.FieldReportStatus status = 11;
   */
  status: FieldReportStatus;

  /**
   * References to attached media (images, videos, etc.)
   *
   * @generated from field: repeated hero.fieldreport.v1.MediaAttachment media_attachments = 12;
   */
  mediaAttachments: MediaAttachment[];

  /**
   * Auditing timestamps
   *
   * @generated from field: google.protobuf.Timestamp create_time = 13;
   */
  createTime?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp update_time = 14;
   */
  updateTime?: Timestamp;

  /**
   * Text messages associated with this report
   *
   * @generated from field: repeated hero.fieldreport.v1.TextMessage messages = 15;
   */
  messages: TextMessage[];

  /**
   * Will be populated on escalation
   *
   * @generated from field: string situation_id = 16;
   */
  situationId: string;

  /**
   * If turned on, automated services won't process it
   *
   * @generated from field: google.protobuf.BoolValue need_manual_review = 17;
   */
  needManualReview?: boolean;

  /**
   * Additional information that can be stored as JSON
   *
   * @generated from field: string report_details_json = 18;
   */
  reportDetailsJson: string;

  /**
   * Whether to use the reporter’s live location instead of static coords
   *
   * @generated from field: google.protobuf.BoolValue use_reporter_live_location = 19;
   */
  useReporterLiveLocation?: boolean;
};

/**
 * Describes the message hero.fieldreport.v1.FieldReport.
 * Use `create(FieldReportSchema)` to create a new message.
 */
export const FieldReportSchema: GenMessage<FieldReport> = /*@__PURE__*/
  messageDesc(file_hero_fieldreport_v1_fieldreport, 13);

/**
 * ---------- Media Attachment ----------
 *
 * @generated from message hero.fieldreport.v1.MediaAttachment
 */
export type MediaAttachment = Message<"hero.fieldreport.v1.MediaAttachment"> & {
  /**
   * @generated from field: string attachment_id = 1;
   */
  attachmentId: string;

  /**
   * @generated from field: string url = 2;
   */
  url: string;

  /**
   * @generated from field: string content_type = 3;
   */
  contentType: string;
};

/**
 * Describes the message hero.fieldreport.v1.MediaAttachment.
 * Use `create(MediaAttachmentSchema)` to create a new message.
 */
export const MediaAttachmentSchema: GenMessage<MediaAttachment> = /*@__PURE__*/
  messageDesc(file_hero_fieldreport_v1_fieldreport, 14);

/**
 * @generated from enum hero.fieldreport.v1.FieldReportStatus
 */
export enum FieldReportStatus {
  /**
   * @generated from enum value: FIELD_REPORT_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: FIELD_REPORT_STATUS_CREATED = 1;
   */
  CREATED = 1,

  /**
   * @generated from enum value: FIELD_REPORT_STATUS_PENDING_ACTION = 2;
   */
  PENDING_ACTION = 2,

  /**
   * @generated from enum value: FIELD_REPORT_STATUS_ASSIGNED = 3;
   */
  ASSIGNED = 3,

  /**
   * @generated from enum value: FIELD_REPORT_STATUS_ASSISTING = 4;
   */
  ASSISTING = 4,

  /**
   * @generated from enum value: FIELD_REPORT_STATUS_ESCALATED = 5;
   */
  ESCALATED = 5,

  /**
   * @generated from enum value: FIELD_REPORT_STATUS_RESOLVED = 6;
   */
  RESOLVED = 6,

  /**
   * @generated from enum value: FIELD_REPORT_STATUS_CLOSED = 7;
   */
  CLOSED = 7,
}

/**
 * Describes the enum hero.fieldreport.v1.FieldReportStatus.
 */
export const FieldReportStatusSchema: GenEnum<FieldReportStatus> = /*@__PURE__*/
  enumDesc(file_hero_fieldreport_v1_fieldreport, 0);

/**
 * @generated from enum hero.fieldreport.v1.ReportType
 */
export enum ReportType {
  /**
   * @generated from enum value: REPORT_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: REPORT_TYPE_PANIC = 1;
   */
  PANIC = 1,

  /**
   * @generated from enum value: REPORT_TYPE_EDGE_ALERT = 2;
   */
  EDGE_ALERT = 2,

  /**
   * @generated from enum value: REPORT_TYPE_OTHER = 4;
   */
  OTHER = 4,
}

/**
 * Describes the enum hero.fieldreport.v1.ReportType.
 */
export const ReportTypeSchema: GenEnum<ReportType> = /*@__PURE__*/
  enumDesc(file_hero_fieldreport_v1_fieldreport, 1);

/**
 * @generated from service hero.fieldreport.v1.FieldReportService
 */
export const FieldReportService: GenService<{
  /**
   * CREATE
   *
   * @generated from rpc hero.fieldreport.v1.FieldReportService.CreateFieldReport
   */
  createFieldReport: {
    methodKind: "unary";
    input: typeof CreateFieldReportRequestSchema;
    output: typeof CreateFieldReportResponseSchema;
  },
  /**
   * @generated from rpc hero.fieldreport.v1.FieldReportService.AddMessageToReport
   */
  addMessageToReport: {
    methodKind: "unary";
    input: typeof AddMessageToReportRequestSchema;
    output: typeof AddMessageToReportResponseSchema;
  },
  /**
   * READ (single)
   *
   * @generated from rpc hero.fieldreport.v1.FieldReportService.GetFieldReport
   */
  getFieldReport: {
    methodKind: "unary";
    input: typeof GetFieldReportRequestSchema;
    output: typeof GetFieldReportResponseSchema;
  },
  /**
   * READ (list) with pagination & filtering
   *
   * @generated from rpc hero.fieldreport.v1.FieldReportService.ListFieldReports
   */
  listFieldReports: {
    methodKind: "unary";
    input: typeof ListFieldReportsRequestSchema;
    output: typeof ListFieldReportsResponseSchema;
  },
  /**
   * UPDATE (fields) - can optionally use FieldMask for partial updates
   *
   * @generated from rpc hero.fieldreport.v1.FieldReportService.UpdateFieldReport
   */
  updateFieldReport: {
    methodKind: "unary";
    input: typeof UpdateFieldReportRequestSchema;
    output: typeof UpdateFieldReportResponseSchema;
  },
  /**
   * DELETE
   *
   * @generated from rpc hero.fieldreport.v1.FieldReportService.DeleteFieldReport
   */
  deleteFieldReport: {
    methodKind: "unary";
    input: typeof DeleteFieldReportRequestSchema;
    output: typeof DeleteFieldReportResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_fieldreport_v1_fieldreport, 0);

