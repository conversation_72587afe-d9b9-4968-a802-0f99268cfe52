// @generated by protoc-gen-es v2.6.1 with parameter "target=ts"
// @generated from file hero/sensors/v1/sensors.proto (package hero.sensors.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/sensors/v1/sensors.proto.
 */
export const file_hero_sensors_v1_sensors: GenFile = /*@__PURE__*/
  fileDesc("Ch1oZXJvL3NlbnNvcnMvdjEvc2Vuc29ycy5wcm90bxIPaGVyby5zZW5zb3JzLnYxImIKEUNhbWVyYUNyZWRlbnRpYWxzEhAKCHVzZXJuYW1lGAEgASgJEhAKCHBhc3N3b3JkGAIgASgJEg8KB3VzZV90bHMYAyABKAgSGAoQY2VydGlmaWNhdGVfcGF0aBgEIAEoCSLIAgoGQ2FtZXJhEhAKCGFzc2V0X2lkGAEgASgJEgwKBG5hbWUYAiABKAkSEgoKaXBfYWRkcmVzcxgDIAEoCRIQCghydHNwX3VybBgEIAEoCRI3CgtjcmVkZW50aWFscxgFIAEoCzIiLmhlcm8uc2Vuc29ycy52MS5DYW1lcmFDcmVkZW50aWFscxIwCgtzdHJlYW1fdHlwZRgGIAEoDjIbLmhlcm8uc2Vuc29ycy52MS5TdHJlYW1UeXBlEjcKCG1ldGFkYXRhGAcgAygLMiUuaGVyby5zZW5zb3JzLnYxLkNhbWVyYS5NZXRhZGF0YUVudHJ5EhAKCGxhdGl0dWRlGAggASgBEhEKCWxvbmdpdHVkZRgJIAEoARovCg1NZXRhZGF0YUVudHJ5EgsKA2tleRgBIAEoCRINCgV2YWx1ZRgCIAEoCToCOAEiJwoDQWNrEg8KB3N1Y2Nlc3MYASABKAgSDwoHbWVzc2FnZRgCIAEoCSLQAQoPR1N0cmVhbWVyQ29uZmlnEhAKCHBpcGVsaW5lGAEgASgJEkQKCnBhcmFtZXRlcnMYAiADKAsyMC5oZXJvLnNlbnNvcnMudjEuR1N0cmVhbWVyQ29uZmlnLlBhcmFtZXRlcnNFbnRyeRIbChN1c2VfaHdfYWNjZWxlcmF0aW9uGAMgASgIEhUKDWh3X2FjY2VsX3R5cGUYBCABKAkaMQoPUGFyYW1ldGVyc0VudHJ5EgsKA2tleRgBIAEoCRINCgV2YWx1ZRgCIAEoCToCOAEixQEKE0NyZWF0ZVN0cmVhbVJlcXVlc3QSEAoIYXNzZXRfaWQYASABKAkSOgoQZ3N0cmVhbWVyX2NvbmZpZxgCIAEoCzIgLmhlcm8uc2Vuc29ycy52MS5HU3RyZWFtZXJDb25maWcSFQoNZW5hYmxlX3dlYnJ0YxgDIAEoCBIVCg13ZWJydGNfY29uZmlnGAQgASgJEhIKCnJlc29sdXRpb24YBSABKAkSDwoHYml0cmF0ZRgGIAEoDRINCgVjb2RlYxgHIAEoCSJTChRDcmVhdGVTdHJlYW1SZXNwb25zZRIRCglzdHJlYW1faWQYASABKAkSFwoPc3RyZWFtX2VuZHBvaW50GAIgASgJEg8KB21lc3NhZ2UYAyABKAkiJQoRUGxheVN0cmVhbVJlcXVlc3QSEAoIYXNzZXRfaWQYASABKAkiPgoSUGxheVN0cmVhbVJlc3BvbnNlEhcKD3N0cmVhbV9lbmRwb2ludBgBIAEoCRIPCgdtZXNzYWdlGAIgASgJIiUKEVN0b3BTdHJlYW1SZXF1ZXN0EhAKCGFzc2V0X2lkGAEgASgJIicKE0RlbGV0ZVN0cmVhbVJlcXVlc3QSEAoIYXNzZXRfaWQYASABKAkiKgoWR2V0U3RyZWFtU3RhdHVzUmVxdWVzdBIQCghhc3NldF9pZBgBIAEoCSKhAQoUU3RyZWFtU3RhdHVzUmVzcG9uc2USKwoFc3RhdGUYASABKA4yHC5oZXJvLnNlbnNvcnMudjEuU3RyZWFtU3RhdGUSDwoHbWVzc2FnZRgCIAEoCRIZChFjdXJyZW50X2ZyYW1lcmF0ZRgDIAEoDRIXCg9jdXJyZW50X2JpdHJhdGUYBCABKA0SFwoPcGlwZWxpbmVfc3RhdHVzGAUgASgJIhkKF1Jlc3RhcnRHU3RyZWFtZXJSZXF1ZXN0IjwKGFJlc3RhcnRHU3RyZWFtZXJSZXNwb25zZRIPCgdzdWNjZXNzGAEgASgIEg8KB21lc3NhZ2UYAiABKAkiIwohTnVrZUFuZFJlc3RhcnRBbGxQaXBlbGluZXNSZXF1ZXN0ImMKIk51a2VBbmRSZXN0YXJ0QWxsUGlwZWxpbmVzUmVzcG9uc2USDwoHc3VjY2VzcxgBIAEoCBIPCgdtZXNzYWdlGAIgASgJEhsKE3BpcGVsaW5lc19yZXN0YXJ0ZWQYAyABKA0iHwodR2V0QWxsUGlwZWxpbmVTdGF0dXNlc1JlcXVlc3QiyQEKHkdldEFsbFBpcGVsaW5lU3RhdHVzZXNSZXNwb25zZRJPCghzdGF0dXNlcxgBIAMoCzI9Lmhlcm8uc2Vuc29ycy52MS5HZXRBbGxQaXBlbGluZVN0YXR1c2VzUmVzcG9uc2UuU3RhdHVzZXNFbnRyeRpWCg1TdGF0dXNlc0VudHJ5EgsKA2tleRgBIAEoCRI0CgV2YWx1ZRgCIAEoCzIlLmhlcm8uc2Vuc29ycy52MS5TdHJlYW1TdGF0dXNSZXNwb25zZToCOAEipQMKE0NyZWF0ZUNhbWVyYVJlcXVlc3QSDAoEbmFtZRgBIAEoCRISCgppcF9hZGRyZXNzGAIgASgJEhAKCHJ0c3BfdXJsGAMgASgJEjcKC2NyZWRlbnRpYWxzGAQgASgLMiIuaGVyby5zZW5zb3JzLnYxLkNhbWVyYUNyZWRlbnRpYWxzEhQKDG1hbnVmYWN0dXJlchgFIAEoCRINCgVtb2RlbBgGIAEoCRIYChBmaXJtd2FyZV92ZXJzaW9uGAcgASgJEhQKDGNhcGFiaWxpdGllcxgIIAMoCRIwCgtzdHJlYW1fdHlwZRgJIAEoDjIbLmhlcm8uc2Vuc29ycy52MS5TdHJlYW1UeXBlEkQKCG1ldGFkYXRhGAogAygLMjIuaGVyby5zZW5zb3JzLnYxLkNyZWF0ZUNhbWVyYVJlcXVlc3QuTWV0YWRhdGFFbnRyeRIQCghsYXRpdHVkZRgLIAEoARIRCglsb25naXR1ZGUYDCABKAEaLwoNTWV0YWRhdGFFbnRyeRILCgNrZXkYASABKAkSDQoFdmFsdWUYAiABKAk6AjgBIv4CChNVcGRhdGVDYW1lcmFSZXF1ZXN0EhAKCGFzc2V0X2lkGAEgASgJEgwKBG5hbWUYAiABKAkSEgoKaXBfYWRkcmVzcxgDIAEoCRIQCghydHNwX3VybBgEIAEoCRIUCgxtYW51ZmFjdHVyZXIYBSABKAkSDQoFbW9kZWwYBiABKAkSGAoQZmlybXdhcmVfdmVyc2lvbhgHIAEoCRIUCgxjYXBhYmlsaXRpZXMYCCADKAkSMAoLc3RyZWFtX3R5cGUYCSABKA4yGy5oZXJvLnNlbnNvcnMudjEuU3RyZWFtVHlwZRJECghtZXRhZGF0YRgKIAMoCzIyLmhlcm8uc2Vuc29ycy52MS5VcGRhdGVDYW1lcmFSZXF1ZXN0Lk1ldGFkYXRhRW50cnkSEAoIbGF0aXR1ZGUYCyABKAESEQoJbG9uZ2l0dWRlGAwgASgBGi8KDU1ldGFkYXRhRW50cnkSCwoDa2V5GAEgASgJEg0KBXZhbHVlGAIgASgJOgI4ASJLChJMaXN0Q2FtZXJhc1JlcXVlc3QSEQoJcGFnZV9zaXplGAEgASgFEhIKCnBhZ2VfdG9rZW4YAiABKAkSDgoGZmlsdGVyGAMgASgJIlgKE0xpc3RDYW1lcmFzUmVzcG9uc2USKAoHY2FtZXJhcxgBIAMoCzIXLmhlcm8uc2Vuc29ycy52MS5DYW1lcmESFwoPbmV4dF9wYWdlX3Rva2VuGAIgASgJIicKE0RlbGV0ZUNhbWVyYVJlcXVlc3QSEAoIYXNzZXRfaWQYASABKAkiJAoQR2V0Q2FtZXJhUmVxdWVzdBIQCghhc3NldF9pZBgBIAEoCSIaChhJbml0aWFsaXplU3RyZWFtc1JlcXVlc3QiGwoZSW5pdGlhbGl6ZVN0cmVhbXNSZXNwb25zZSImCg9MaXZlRmVlZFJlcXVlc3QSEwoLc3RyZWFtX25hbWUYASABKAkiIwoQTGl2ZUZlZWRSZXNwb25zZRIPCgdobHNfdXJsGAEgASgJIlEKFFZpZGVvUGxheWJhY2tSZXF1ZXN0EhMKC3N0cmVhbV9uYW1lGAEgASgJEhIKCnN0YXJ0X3RpbWUYAiABKAkSEAoIZW5kX3RpbWUYAyABKAkiKAoVVmlkZW9QbGF5YmFja1Jlc3BvbnNlEg8KB2hsc191cmwYASABKAkiRAoURXZlbnRQbGF5YmFja1JlcXVlc3QSEwoLc3RyZWFtX25hbWUYASABKAkSFwoPZXZlbnRfdGltZXN0YW1wGAIgASgJIigKFUV2ZW50UGxheWJhY2tSZXNwb25zZRIPCgdobHNfdXJsGAEgASgJKowBCgtTdHJlYW1TdGF0ZRIYChRTVFJFQU1fU1RBVEVfVU5LTk9XThAAEhgKFFNUUkVBTV9TVEFURV9QTEFZSU5HEAESFwoTU1RSRUFNX1NUQVRFX1BBVVNFRBACEhgKFFNUUkVBTV9TVEFURV9TVE9QUEVEEAMSFgoSU1RSRUFNX1NUQVRFX0VSUk9SEAQqaQoKU3RyZWFtVHlwZRIXChNTVFJFQU1fVFlQRV9VTktOT1dOEAASFAoQU1RSRUFNX1RZUEVfUlRTUBABEhYKElNUUkVBTV9UWVBFX1dFQkNBTRACEhQKEFNUUkVBTV9UWVBFX0ZJTEUQAzKIBAoNQ2FtZXJhU2VydmljZRJNCgxDcmVhdGVDYW1lcmESJC5oZXJvLnNlbnNvcnMudjEuQ3JlYXRlQ2FtZXJhUmVxdWVzdBoXLmhlcm8uc2Vuc29ycy52MS5DYW1lcmESRwoJR2V0Q2FtZXJhEiEuaGVyby5zZW5zb3JzLnYxLkdldENhbWVyYVJlcXVlc3QaFy5oZXJvLnNlbnNvcnMudjEuQ2FtZXJhEk0KDFVwZGF0ZUNhbWVyYRIkLmhlcm8uc2Vuc29ycy52MS5VcGRhdGVDYW1lcmFSZXF1ZXN0GhcuaGVyby5zZW5zb3JzLnYxLkNhbWVyYRJYCgtMaXN0Q2FtZXJhcxIjLmhlcm8uc2Vuc29ycy52MS5MaXN0Q2FtZXJhc1JlcXVlc3QaJC5oZXJvLnNlbnNvcnMudjEuTGlzdENhbWVyYXNSZXNwb25zZRJKCgxEZWxldGVDYW1lcmESJC5oZXJvLnNlbnNvcnMudjEuRGVsZXRlQ2FtZXJhUmVxdWVzdBoULmhlcm8uc2Vuc29ycy52MS5BY2sSagoRSW5pdGlhbGl6ZVN0cmVhbXMSKS5oZXJvLnNlbnNvcnMudjEuSW5pdGlhbGl6ZVN0cmVhbXNSZXF1ZXN0GiouaGVyby5zZW5zb3JzLnYxLkluaXRpYWxpemVTdHJlYW1zUmVzcG9uc2UyswYKGkNhbWVyYU9yY2hlc3RyYXRpb25TZXJ2aWNlElsKDENyZWF0ZVN0cmVhbRIkLmhlcm8uc2Vuc29ycy52MS5DcmVhdGVTdHJlYW1SZXF1ZXN0GiUuaGVyby5zZW5zb3JzLnYxLkNyZWF0ZVN0cmVhbVJlc3BvbnNlElUKClBsYXlTdHJlYW0SIi5oZXJvLnNlbnNvcnMudjEuUGxheVN0cmVhbVJlcXVlc3QaIy5oZXJvLnNlbnNvcnMudjEuUGxheVN0cmVhbVJlc3BvbnNlEkYKClN0b3BTdHJlYW0SIi5oZXJvLnNlbnNvcnMudjEuU3RvcFN0cmVhbVJlcXVlc3QaFC5oZXJvLnNlbnNvcnMudjEuQWNrEmEKD0dldFN0cmVhbVN0YXR1cxInLmhlcm8uc2Vuc29ycy52MS5HZXRTdHJlYW1TdGF0dXNSZXF1ZXN0GiUuaGVyby5zZW5zb3JzLnYxLlN0cmVhbVN0YXR1c1Jlc3BvbnNlEkoKDERlbGV0ZVN0cmVhbRIkLmhlcm8uc2Vuc29ycy52MS5EZWxldGVTdHJlYW1SZXF1ZXN0GhQuaGVyby5zZW5zb3JzLnYxLkFjaxJnChBSZXN0YXJ0R1N0cmVhbWVyEiguaGVyby5zZW5zb3JzLnYxLlJlc3RhcnRHU3RyZWFtZXJSZXF1ZXN0GikuaGVyby5zZW5zb3JzLnYxLlJlc3RhcnRHU3RyZWFtZXJSZXNwb25zZRKFAQoaTnVrZUFuZFJlc3RhcnRBbGxQaXBlbGluZXMSMi5oZXJvLnNlbnNvcnMudjEuTnVrZUFuZFJlc3RhcnRBbGxQaXBlbGluZXNSZXF1ZXN0GjMuaGVyby5zZW5zb3JzLnYxLk51a2VBbmRSZXN0YXJ0QWxsUGlwZWxpbmVzUmVzcG9uc2USeQoWR2V0QWxsUGlwZWxpbmVTdGF0dXNlcxIuLmhlcm8uc2Vuc29ycy52MS5HZXRBbGxQaXBlbGluZVN0YXR1c2VzUmVxdWVzdBovLmhlcm8uc2Vuc29ycy52MS5HZXRBbGxQaXBlbGluZVN0YXR1c2VzUmVzcG9uc2UyrQIKEUNhbWVyYUZlZWRTZXJ2aWNlElIKC0dldExpdmVGZWVkEiAuaGVyby5zZW5zb3JzLnYxLkxpdmVGZWVkUmVxdWVzdBohLmhlcm8uc2Vuc29ycy52MS5MaXZlRmVlZFJlc3BvbnNlEmEKEEdldFZpZGVvUGxheWJhY2sSJS5oZXJvLnNlbnNvcnMudjEuVmlkZW9QbGF5YmFja1JlcXVlc3QaJi5oZXJvLnNlbnNvcnMudjEuVmlkZW9QbGF5YmFja1Jlc3BvbnNlEmEKEEdldEV2ZW50UGxheWJhY2sSJS5oZXJvLnNlbnNvcnMudjEuRXZlbnRQbGF5YmFja1JlcXVlc3QaJi5oZXJvLnNlbnNvcnMudjEuRXZlbnRQbGF5YmFja1Jlc3BvbnNlQh9aHXByb3RvL2hlcm8vc2Vuc29ycy92MTtzZW5zb3JzYgZwcm90bzM");

/**
 * @generated from message hero.sensors.v1.CameraCredentials
 */
export type CameraCredentials = Message<"hero.sensors.v1.CameraCredentials"> & {
  /**
   * @generated from field: string username = 1;
   */
  username: string;

  /**
   * @generated from field: string password = 2;
   */
  password: string;

  /**
   * @generated from field: bool use_tls = 3;
   */
  useTls: boolean;

  /**
   * Optional path to custom certificate
   *
   * @generated from field: string certificate_path = 4;
   */
  certificatePath: string;
};

/**
 * Describes the message hero.sensors.v1.CameraCredentials.
 * Use `create(CameraCredentialsSchema)` to create a new message.
 */
export const CameraCredentialsSchema: GenMessage<CameraCredentials> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 0);

/**
 * Camera represents the technical details needed for streaming
 * This is used internally by the cameras service
 *
 * @generated from message hero.sensors.v1.Camera
 */
export type Camera = Message<"hero.sensors.v1.Camera"> & {
  /**
   * Reference to the asset in the asset registry
   *
   * @generated from field: string asset_id = 1;
   */
  assetId: string;

  /**
   * Display name of the camera
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * Local network IP or hostname
   *
   * @generated from field: string ip_address = 3;
   */
  ipAddress: string;

  /**
   * RTSP stream URL
   *
   * @generated from field: string rtsp_url = 4;
   */
  rtspUrl: string;

  /**
   * Authentication credentials
   *
   * @generated from field: hero.sensors.v1.CameraCredentials credentials = 5;
   */
  credentials?: CameraCredentials;

  /**
   * Type of stream (RTSP, Webcam, etc.)
   *
   * @generated from field: hero.sensors.v1.StreamType stream_type = 6;
   */
  streamType: StreamType;

  /**
   * Additional camera-specific metadata
   *
   * @generated from field: map<string, string> metadata = 7;
   */
  metadata: { [key: string]: string };

  /**
   * Camera's latitude
   *
   * @generated from field: double latitude = 8;
   */
  latitude: number;

  /**
   * Camera's longitude
   *
   * @generated from field: double longitude = 9;
   */
  longitude: number;
};

/**
 * Describes the message hero.sensors.v1.Camera.
 * Use `create(CameraSchema)` to create a new message.
 */
export const CameraSchema: GenMessage<Camera> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 1);

/**
 * @generated from message hero.sensors.v1.Ack
 */
export type Ack = Message<"hero.sensors.v1.Ack"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message hero.sensors.v1.Ack.
 * Use `create(AckSchema)` to create a new message.
 */
export const AckSchema: GenMessage<Ack> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 2);

/**
 * @generated from message hero.sensors.v1.GStreamerConfig
 */
export type GStreamerConfig = Message<"hero.sensors.v1.GStreamerConfig"> & {
  /**
   * Custom gstreamer pipeline
   *
   * @generated from field: string pipeline = 1;
   */
  pipeline: string;

  /**
   * Pipeline parameters
   *
   * @generated from field: map<string, string> parameters = 2;
   */
  parameters: { [key: string]: string };

  /**
   * Enable hardware acceleration
   *
   * @generated from field: bool use_hw_acceleration = 3;
   */
  useHwAcceleration: boolean;

  /**
   * Type of hardware acceleration (e.g., "vaapi", "nvenc")
   *
   * @generated from field: string hw_accel_type = 4;
   */
  hwAccelType: string;
};

/**
 * Describes the message hero.sensors.v1.GStreamerConfig.
 * Use `create(GStreamerConfigSchema)` to create a new message.
 */
export const GStreamerConfigSchema: GenMessage<GStreamerConfig> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 3);

/**
 * @generated from message hero.sensors.v1.CreateStreamRequest
 */
export type CreateStreamRequest = Message<"hero.sensors.v1.CreateStreamRequest"> & {
  /**
   * Asset ID of the camera
   *
   * @generated from field: string asset_id = 1;
   */
  assetId: string;

  /**
   * @generated from field: hero.sensors.v1.GStreamerConfig gstreamer_config = 2;
   */
  gstreamerConfig?: GStreamerConfig;

  /**
   * Whether to enable WebRTC streaming
   *
   * @generated from field: bool enable_webrtc = 3;
   */
  enableWebrtc: boolean;

  /**
   * WebRTC configuration (e.g., STUN/TURN servers)
   *
   * @generated from field: string webrtc_config = 4;
   */
  webrtcConfig: string;

  /**
   * e.g., "1920x1080"
   *
   * @generated from field: string resolution = 5;
   */
  resolution: string;

  /**
   * Desired bitrate, in kbps
   *
   * @generated from field: uint32 bitrate = 6;
   */
  bitrate: number;

  /**
   * e.g., "H264", "H265"
   *
   * @generated from field: string codec = 7;
   */
  codec: string;
};

/**
 * Describes the message hero.sensors.v1.CreateStreamRequest.
 * Use `create(CreateStreamRequestSchema)` to create a new message.
 */
export const CreateStreamRequestSchema: GenMessage<CreateStreamRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 4);

/**
 * @generated from message hero.sensors.v1.CreateStreamResponse
 */
export type CreateStreamResponse = Message<"hero.sensors.v1.CreateStreamResponse"> & {
  /**
   * Unique identifier for the stream session
   *
   * @generated from field: string stream_id = 1;
   */
  streamId: string;

  /**
   * The WebRTC endpoint URL
   *
   * @generated from field: string stream_endpoint = 2;
   */
  streamEndpoint: string;

  /**
   * Optional confirmation message
   *
   * @generated from field: string message = 3;
   */
  message: string;
};

/**
 * Describes the message hero.sensors.v1.CreateStreamResponse.
 * Use `create(CreateStreamResponseSchema)` to create a new message.
 */
export const CreateStreamResponseSchema: GenMessage<CreateStreamResponse> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 5);

/**
 * @generated from message hero.sensors.v1.PlayStreamRequest
 */
export type PlayStreamRequest = Message<"hero.sensors.v1.PlayStreamRequest"> & {
  /**
   * Asset ID of the camera to start streaming from
   *
   * @generated from field: string asset_id = 1;
   */
  assetId: string;
};

/**
 * Describes the message hero.sensors.v1.PlayStreamRequest.
 * Use `create(PlayStreamRequestSchema)` to create a new message.
 */
export const PlayStreamRequestSchema: GenMessage<PlayStreamRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 6);

/**
 * @generated from message hero.sensors.v1.PlayStreamResponse
 */
export type PlayStreamResponse = Message<"hero.sensors.v1.PlayStreamResponse"> & {
  /**
   * WebRTC endpoint where the live stream is accessible
   *
   * @generated from field: string stream_endpoint = 1;
   */
  streamEndpoint: string;

  /**
   * Optional additional message
   *
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message hero.sensors.v1.PlayStreamResponse.
 * Use `create(PlayStreamResponseSchema)` to create a new message.
 */
export const PlayStreamResponseSchema: GenMessage<PlayStreamResponse> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 7);

/**
 * @generated from message hero.sensors.v1.StopStreamRequest
 */
export type StopStreamRequest = Message<"hero.sensors.v1.StopStreamRequest"> & {
  /**
   * Asset ID of the camera to stop streaming from
   *
   * @generated from field: string asset_id = 1;
   */
  assetId: string;
};

/**
 * Describes the message hero.sensors.v1.StopStreamRequest.
 * Use `create(StopStreamRequestSchema)` to create a new message.
 */
export const StopStreamRequestSchema: GenMessage<StopStreamRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 8);

/**
 * @generated from message hero.sensors.v1.DeleteStreamRequest
 */
export type DeleteStreamRequest = Message<"hero.sensors.v1.DeleteStreamRequest"> & {
  /**
   * Asset ID of the camera to delete stream configuration for
   *
   * @generated from field: string asset_id = 1;
   */
  assetId: string;
};

/**
 * Describes the message hero.sensors.v1.DeleteStreamRequest.
 * Use `create(DeleteStreamRequestSchema)` to create a new message.
 */
export const DeleteStreamRequestSchema: GenMessage<DeleteStreamRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 9);

/**
 * @generated from message hero.sensors.v1.GetStreamStatusRequest
 */
export type GetStreamStatusRequest = Message<"hero.sensors.v1.GetStreamStatusRequest"> & {
  /**
   * Asset ID of the camera to get stream status for
   *
   * @generated from field: string asset_id = 1;
   */
  assetId: string;
};

/**
 * Describes the message hero.sensors.v1.GetStreamStatusRequest.
 * Use `create(GetStreamStatusRequestSchema)` to create a new message.
 */
export const GetStreamStatusRequestSchema: GenMessage<GetStreamStatusRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 10);

/**
 * @generated from message hero.sensors.v1.StreamStatusResponse
 */
export type StreamStatusResponse = Message<"hero.sensors.v1.StreamStatusResponse"> & {
  /**
   * @generated from field: hero.sensors.v1.StreamState state = 1;
   */
  state: StreamState;

  /**
   * Optional: error or state message
   *
   * @generated from field: string message = 2;
   */
  message: string;

  /**
   * Current framerate, if available
   *
   * @generated from field: uint32 current_framerate = 3;
   */
  currentFramerate: number;

  /**
   * Current bitrate, if available
   *
   * @generated from field: uint32 current_bitrate = 4;
   */
  currentBitrate: number;

  /**
   * Status of the gstreamer pipeline
   *
   * @generated from field: string pipeline_status = 5;
   */
  pipelineStatus: string;
};

/**
 * Describes the message hero.sensors.v1.StreamStatusResponse.
 * Use `create(StreamStatusResponseSchema)` to create a new message.
 */
export const StreamStatusResponseSchema: GenMessage<StreamStatusResponse> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 11);

/**
 * New messages for pipeline control
 *
 * No parameters needed for restarting GStreamer
 *
 * @generated from message hero.sensors.v1.RestartGStreamerRequest
 */
export type RestartGStreamerRequest = Message<"hero.sensors.v1.RestartGStreamerRequest"> & {
};

/**
 * Describes the message hero.sensors.v1.RestartGStreamerRequest.
 * Use `create(RestartGStreamerRequestSchema)` to create a new message.
 */
export const RestartGStreamerRequestSchema: GenMessage<RestartGStreamerRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 12);

/**
 * @generated from message hero.sensors.v1.RestartGStreamerResponse
 */
export type RestartGStreamerResponse = Message<"hero.sensors.v1.RestartGStreamerResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message hero.sensors.v1.RestartGStreamerResponse.
 * Use `create(RestartGStreamerResponseSchema)` to create a new message.
 */
export const RestartGStreamerResponseSchema: GenMessage<RestartGStreamerResponse> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 13);

/**
 * No parameters needed for nuking and restarting all pipelines
 *
 * @generated from message hero.sensors.v1.NukeAndRestartAllPipelinesRequest
 */
export type NukeAndRestartAllPipelinesRequest = Message<"hero.sensors.v1.NukeAndRestartAllPipelinesRequest"> & {
};

/**
 * Describes the message hero.sensors.v1.NukeAndRestartAllPipelinesRequest.
 * Use `create(NukeAndRestartAllPipelinesRequestSchema)` to create a new message.
 */
export const NukeAndRestartAllPipelinesRequestSchema: GenMessage<NukeAndRestartAllPipelinesRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 14);

/**
 * @generated from message hero.sensors.v1.NukeAndRestartAllPipelinesResponse
 */
export type NukeAndRestartAllPipelinesResponse = Message<"hero.sensors.v1.NukeAndRestartAllPipelinesResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;

  /**
   * Number of pipelines that were restarted
   *
   * @generated from field: uint32 pipelines_restarted = 3;
   */
  pipelinesRestarted: number;
};

/**
 * Describes the message hero.sensors.v1.NukeAndRestartAllPipelinesResponse.
 * Use `create(NukeAndRestartAllPipelinesResponseSchema)` to create a new message.
 */
export const NukeAndRestartAllPipelinesResponseSchema: GenMessage<NukeAndRestartAllPipelinesResponse> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 15);

/**
 * No parameters needed for getting all pipeline statuses
 *
 * @generated from message hero.sensors.v1.GetAllPipelineStatusesRequest
 */
export type GetAllPipelineStatusesRequest = Message<"hero.sensors.v1.GetAllPipelineStatusesRequest"> & {
};

/**
 * Describes the message hero.sensors.v1.GetAllPipelineStatusesRequest.
 * Use `create(GetAllPipelineStatusesRequestSchema)` to create a new message.
 */
export const GetAllPipelineStatusesRequestSchema: GenMessage<GetAllPipelineStatusesRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 16);

/**
 * @generated from message hero.sensors.v1.GetAllPipelineStatusesResponse
 */
export type GetAllPipelineStatusesResponse = Message<"hero.sensors.v1.GetAllPipelineStatusesResponse"> & {
  /**
   * Map of asset_id to status
   *
   * @generated from field: map<string, hero.sensors.v1.StreamStatusResponse> statuses = 1;
   */
  statuses: { [key: string]: StreamStatusResponse };
};

/**
 * Describes the message hero.sensors.v1.GetAllPipelineStatusesResponse.
 * Use `create(GetAllPipelineStatusesResponseSchema)` to create a new message.
 */
export const GetAllPipelineStatusesResponseSchema: GenMessage<GetAllPipelineStatusesResponse> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 17);

/**
 * @generated from message hero.sensors.v1.CreateCameraRequest
 */
export type CreateCameraRequest = Message<"hero.sensors.v1.CreateCameraRequest"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string ip_address = 2;
   */
  ipAddress: string;

  /**
   * @generated from field: string rtsp_url = 3;
   */
  rtspUrl: string;

  /**
   * @generated from field: hero.sensors.v1.CameraCredentials credentials = 4;
   */
  credentials?: CameraCredentials;

  /**
   * @generated from field: string manufacturer = 5;
   */
  manufacturer: string;

  /**
   * @generated from field: string model = 6;
   */
  model: string;

  /**
   * @generated from field: string firmware_version = 7;
   */
  firmwareVersion: string;

  /**
   * @generated from field: repeated string capabilities = 8;
   */
  capabilities: string[];

  /**
   * @generated from field: hero.sensors.v1.StreamType stream_type = 9;
   */
  streamType: StreamType;

  /**
   * @generated from field: map<string, string> metadata = 10;
   */
  metadata: { [key: string]: string };

  /**
   * @generated from field: double latitude = 11;
   */
  latitude: number;

  /**
   * @generated from field: double longitude = 12;
   */
  longitude: number;
};

/**
 * Describes the message hero.sensors.v1.CreateCameraRequest.
 * Use `create(CreateCameraRequestSchema)` to create a new message.
 */
export const CreateCameraRequestSchema: GenMessage<CreateCameraRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 18);

/**
 * @generated from message hero.sensors.v1.UpdateCameraRequest
 */
export type UpdateCameraRequest = Message<"hero.sensors.v1.UpdateCameraRequest"> & {
  /**
   * @generated from field: string asset_id = 1;
   */
  assetId: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string ip_address = 3;
   */
  ipAddress: string;

  /**
   * @generated from field: string rtsp_url = 4;
   */
  rtspUrl: string;

  /**
   * @generated from field: string manufacturer = 5;
   */
  manufacturer: string;

  /**
   * @generated from field: string model = 6;
   */
  model: string;

  /**
   * @generated from field: string firmware_version = 7;
   */
  firmwareVersion: string;

  /**
   * @generated from field: repeated string capabilities = 8;
   */
  capabilities: string[];

  /**
   * @generated from field: hero.sensors.v1.StreamType stream_type = 9;
   */
  streamType: StreamType;

  /**
   * @generated from field: map<string, string> metadata = 10;
   */
  metadata: { [key: string]: string };

  /**
   * @generated from field: double latitude = 11;
   */
  latitude: number;

  /**
   * @generated from field: double longitude = 12;
   */
  longitude: number;
};

/**
 * Describes the message hero.sensors.v1.UpdateCameraRequest.
 * Use `create(UpdateCameraRequestSchema)` to create a new message.
 */
export const UpdateCameraRequestSchema: GenMessage<UpdateCameraRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 19);

/**
 * @generated from message hero.sensors.v1.ListCamerasRequest
 */
export type ListCamerasRequest = Message<"hero.sensors.v1.ListCamerasRequest"> & {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  /**
   * @generated from field: string filter = 3;
   */
  filter: string;
};

/**
 * Describes the message hero.sensors.v1.ListCamerasRequest.
 * Use `create(ListCamerasRequestSchema)` to create a new message.
 */
export const ListCamerasRequestSchema: GenMessage<ListCamerasRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 20);

/**
 * @generated from message hero.sensors.v1.ListCamerasResponse
 */
export type ListCamerasResponse = Message<"hero.sensors.v1.ListCamerasResponse"> & {
  /**
   * @generated from field: repeated hero.sensors.v1.Camera cameras = 1;
   */
  cameras: Camera[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.sensors.v1.ListCamerasResponse.
 * Use `create(ListCamerasResponseSchema)` to create a new message.
 */
export const ListCamerasResponseSchema: GenMessage<ListCamerasResponse> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 21);

/**
 * @generated from message hero.sensors.v1.DeleteCameraRequest
 */
export type DeleteCameraRequest = Message<"hero.sensors.v1.DeleteCameraRequest"> & {
  /**
   * @generated from field: string asset_id = 1;
   */
  assetId: string;
};

/**
 * Describes the message hero.sensors.v1.DeleteCameraRequest.
 * Use `create(DeleteCameraRequestSchema)` to create a new message.
 */
export const DeleteCameraRequestSchema: GenMessage<DeleteCameraRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 22);

/**
 * @generated from message hero.sensors.v1.GetCameraRequest
 */
export type GetCameraRequest = Message<"hero.sensors.v1.GetCameraRequest"> & {
  /**
   * Asset ID of the camera
   *
   * @generated from field: string asset_id = 1;
   */
  assetId: string;
};

/**
 * Describes the message hero.sensors.v1.GetCameraRequest.
 * Use `create(GetCameraRequestSchema)` to create a new message.
 */
export const GetCameraRequestSchema: GenMessage<GetCameraRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 23);

/**
 * @generated from message hero.sensors.v1.InitializeStreamsRequest
 */
export type InitializeStreamsRequest = Message<"hero.sensors.v1.InitializeStreamsRequest"> & {
};

/**
 * Describes the message hero.sensors.v1.InitializeStreamsRequest.
 * Use `create(InitializeStreamsRequestSchema)` to create a new message.
 */
export const InitializeStreamsRequestSchema: GenMessage<InitializeStreamsRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 24);

/**
 * @generated from message hero.sensors.v1.InitializeStreamsResponse
 */
export type InitializeStreamsResponse = Message<"hero.sensors.v1.InitializeStreamsResponse"> & {
};

/**
 * Describes the message hero.sensors.v1.InitializeStreamsResponse.
 * Use `create(InitializeStreamsResponseSchema)` to create a new message.
 */
export const InitializeStreamsResponseSchema: GenMessage<InitializeStreamsResponse> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 25);

/**
 * @generated from message hero.sensors.v1.LiveFeedRequest
 */
export type LiveFeedRequest = Message<"hero.sensors.v1.LiveFeedRequest"> & {
  /**
   * @generated from field: string stream_name = 1;
   */
  streamName: string;
};

/**
 * Describes the message hero.sensors.v1.LiveFeedRequest.
 * Use `create(LiveFeedRequestSchema)` to create a new message.
 */
export const LiveFeedRequestSchema: GenMessage<LiveFeedRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 26);

/**
 * @generated from message hero.sensors.v1.LiveFeedResponse
 */
export type LiveFeedResponse = Message<"hero.sensors.v1.LiveFeedResponse"> & {
  /**
   * @generated from field: string hls_url = 1;
   */
  hlsUrl: string;
};

/**
 * Describes the message hero.sensors.v1.LiveFeedResponse.
 * Use `create(LiveFeedResponseSchema)` to create a new message.
 */
export const LiveFeedResponseSchema: GenMessage<LiveFeedResponse> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 27);

/**
 * @generated from message hero.sensors.v1.VideoPlaybackRequest
 */
export type VideoPlaybackRequest = Message<"hero.sensors.v1.VideoPlaybackRequest"> & {
  /**
   * @generated from field: string stream_name = 1;
   */
  streamName: string;

  /**
   * RFC3339 format
   *
   * @generated from field: string start_time = 2;
   */
  startTime: string;

  /**
   * RFC3339 format, optional
   *
   * @generated from field: string end_time = 3;
   */
  endTime: string;
};

/**
 * Describes the message hero.sensors.v1.VideoPlaybackRequest.
 * Use `create(VideoPlaybackRequestSchema)` to create a new message.
 */
export const VideoPlaybackRequestSchema: GenMessage<VideoPlaybackRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 28);

/**
 * @generated from message hero.sensors.v1.VideoPlaybackResponse
 */
export type VideoPlaybackResponse = Message<"hero.sensors.v1.VideoPlaybackResponse"> & {
  /**
   * @generated from field: string hls_url = 1;
   */
  hlsUrl: string;
};

/**
 * Describes the message hero.sensors.v1.VideoPlaybackResponse.
 * Use `create(VideoPlaybackResponseSchema)` to create a new message.
 */
export const VideoPlaybackResponseSchema: GenMessage<VideoPlaybackResponse> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 29);

/**
 * @generated from message hero.sensors.v1.EventPlaybackRequest
 */
export type EventPlaybackRequest = Message<"hero.sensors.v1.EventPlaybackRequest"> & {
  /**
   * @generated from field: string stream_name = 1;
   */
  streamName: string;

  /**
   * RFC3339 format
   *
   * @generated from field: string event_timestamp = 2;
   */
  eventTimestamp: string;
};

/**
 * Describes the message hero.sensors.v1.EventPlaybackRequest.
 * Use `create(EventPlaybackRequestSchema)` to create a new message.
 */
export const EventPlaybackRequestSchema: GenMessage<EventPlaybackRequest> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 30);

/**
 * @generated from message hero.sensors.v1.EventPlaybackResponse
 */
export type EventPlaybackResponse = Message<"hero.sensors.v1.EventPlaybackResponse"> & {
  /**
   * @generated from field: string hls_url = 1;
   */
  hlsUrl: string;
};

/**
 * Describes the message hero.sensors.v1.EventPlaybackResponse.
 * Use `create(EventPlaybackResponseSchema)` to create a new message.
 */
export const EventPlaybackResponseSchema: GenMessage<EventPlaybackResponse> = /*@__PURE__*/
  messageDesc(file_hero_sensors_v1_sensors, 31);

/**
 * @generated from enum hero.sensors.v1.StreamState
 */
export enum StreamState {
  /**
   * @generated from enum value: STREAM_STATE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: STREAM_STATE_PLAYING = 1;
   */
  PLAYING = 1,

  /**
   * @generated from enum value: STREAM_STATE_PAUSED = 2;
   */
  PAUSED = 2,

  /**
   * @generated from enum value: STREAM_STATE_STOPPED = 3;
   */
  STOPPED = 3,

  /**
   * @generated from enum value: STREAM_STATE_ERROR = 4;
   */
  ERROR = 4,
}

/**
 * Describes the enum hero.sensors.v1.StreamState.
 */
export const StreamStateSchema: GenEnum<StreamState> = /*@__PURE__*/
  enumDesc(file_hero_sensors_v1_sensors, 0);

/**
 * @generated from enum hero.sensors.v1.StreamType
 */
export enum StreamType {
  /**
   * @generated from enum value: STREAM_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: STREAM_TYPE_RTSP = 1;
   */
  RTSP = 1,

  /**
   * @generated from enum value: STREAM_TYPE_WEBCAM = 2;
   */
  WEBCAM = 2,

  /**
   * @generated from enum value: STREAM_TYPE_FILE = 3;
   */
  FILE = 3,
}

/**
 * Describes the enum hero.sensors.v1.StreamType.
 */
export const StreamTypeSchema: GenEnum<StreamType> = /*@__PURE__*/
  enumDesc(file_hero_sensors_v1_sensors, 1);

/**
 * @generated from service hero.sensors.v1.CameraService
 */
export const CameraService: GenService<{
  /**
   * CreateCamera creates a new camera
   *
   * @generated from rpc hero.sensors.v1.CameraService.CreateCamera
   */
  createCamera: {
    methodKind: "unary";
    input: typeof CreateCameraRequestSchema;
    output: typeof CameraSchema;
  },
  /**
   * GetCamera retrieves a camera by its ID
   *
   * @generated from rpc hero.sensors.v1.CameraService.GetCamera
   */
  getCamera: {
    methodKind: "unary";
    input: typeof GetCameraRequestSchema;
    output: typeof CameraSchema;
  },
  /**
   * UpdateCamera updates an existing camera
   *
   * @generated from rpc hero.sensors.v1.CameraService.UpdateCamera
   */
  updateCamera: {
    methodKind: "unary";
    input: typeof UpdateCameraRequestSchema;
    output: typeof CameraSchema;
  },
  /**
   * ListCameras returns a paginated list of cameras
   *
   * @generated from rpc hero.sensors.v1.CameraService.ListCameras
   */
  listCameras: {
    methodKind: "unary";
    input: typeof ListCamerasRequestSchema;
    output: typeof ListCamerasResponseSchema;
  },
  /**
   * DeleteCamera deletes a camera by its ID
   *
   * @generated from rpc hero.sensors.v1.CameraService.DeleteCamera
   */
  deleteCamera: {
    methodKind: "unary";
    input: typeof DeleteCameraRequestSchema;
    output: typeof AckSchema;
  },
  /**
   * InitializeStreams is called on service startup to restore any active streams
   *
   * @generated from rpc hero.sensors.v1.CameraService.InitializeStreams
   */
  initializeStreams: {
    methodKind: "unary";
    input: typeof InitializeStreamsRequestSchema;
    output: typeof InitializeStreamsResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_sensors_v1_sensors, 0);

/**
 * @generated from service hero.sensors.v1.CameraOrchestrationService
 */
export const CameraOrchestrationService: GenService<{
  /**
   * @generated from rpc hero.sensors.v1.CameraOrchestrationService.CreateStream
   */
  createStream: {
    methodKind: "unary";
    input: typeof CreateStreamRequestSchema;
    output: typeof CreateStreamResponseSchema;
  },
  /**
   * @generated from rpc hero.sensors.v1.CameraOrchestrationService.PlayStream
   */
  playStream: {
    methodKind: "unary";
    input: typeof PlayStreamRequestSchema;
    output: typeof PlayStreamResponseSchema;
  },
  /**
   * @generated from rpc hero.sensors.v1.CameraOrchestrationService.StopStream
   */
  stopStream: {
    methodKind: "unary";
    input: typeof StopStreamRequestSchema;
    output: typeof AckSchema;
  },
  /**
   * @generated from rpc hero.sensors.v1.CameraOrchestrationService.GetStreamStatus
   */
  getStreamStatus: {
    methodKind: "unary";
    input: typeof GetStreamStatusRequestSchema;
    output: typeof StreamStatusResponseSchema;
  },
  /**
   * @generated from rpc hero.sensors.v1.CameraOrchestrationService.DeleteStream
   */
  deleteStream: {
    methodKind: "unary";
    input: typeof DeleteStreamRequestSchema;
    output: typeof AckSchema;
  },
  /**
   * @generated from rpc hero.sensors.v1.CameraOrchestrationService.RestartGStreamer
   */
  restartGStreamer: {
    methodKind: "unary";
    input: typeof RestartGStreamerRequestSchema;
    output: typeof RestartGStreamerResponseSchema;
  },
  /**
   * @generated from rpc hero.sensors.v1.CameraOrchestrationService.NukeAndRestartAllPipelines
   */
  nukeAndRestartAllPipelines: {
    methodKind: "unary";
    input: typeof NukeAndRestartAllPipelinesRequestSchema;
    output: typeof NukeAndRestartAllPipelinesResponseSchema;
  },
  /**
   * @generated from rpc hero.sensors.v1.CameraOrchestrationService.GetAllPipelineStatuses
   */
  getAllPipelineStatuses: {
    methodKind: "unary";
    input: typeof GetAllPipelineStatusesRequestSchema;
    output: typeof GetAllPipelineStatusesResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_sensors_v1_sensors, 1);

/**
 * @generated from service hero.sensors.v1.CameraFeedService
 */
export const CameraFeedService: GenService<{
  /**
   * @generated from rpc hero.sensors.v1.CameraFeedService.GetLiveFeed
   */
  getLiveFeed: {
    methodKind: "unary";
    input: typeof LiveFeedRequestSchema;
    output: typeof LiveFeedResponseSchema;
  },
  /**
   * @generated from rpc hero.sensors.v1.CameraFeedService.GetVideoPlayback
   */
  getVideoPlayback: {
    methodKind: "unary";
    input: typeof VideoPlaybackRequestSchema;
    output: typeof VideoPlaybackResponseSchema;
  },
  /**
   * @generated from rpc hero.sensors.v1.CameraFeedService.GetEventPlayback
   */
  getEventPlayback: {
    methodKind: "unary";
    input: typeof EventPlaybackRequestSchema;
    output: typeof EventPlaybackResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_sensors_v1_sensors, 2);

