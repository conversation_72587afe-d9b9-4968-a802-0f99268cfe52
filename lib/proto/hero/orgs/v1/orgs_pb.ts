// @generated by protoc-gen-es v2.6.1 with parameter "target=ts"
// @generated from file hero/orgs/v1/orgs.proto (package hero.orgs.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_hero_permissions_v1_permissions } from "../../permissions/v1/permissions_pb";
import type { AssetType } from "../../assets/v2/assets_pb";
import { file_hero_assets_v2_assets } from "../../assets/v2/assets_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/orgs/v1/orgs.proto.
 */
export const file_hero_orgs_v1_orgs: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_timestamp, file_hero_permissions_v1_permissions, file_hero_assets_v2_assets]);

/**
 * @generated from message hero.orgs.v1.TurnOnGuestModeRequest
 */
export type TurnOnGuestModeRequest = Message<"hero.orgs.v1.TurnOnGuestModeRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;
};

/**
 * Describes the message hero.orgs.v1.TurnOnGuestModeRequest.
 * Use `create(TurnOnGuestModeRequestSchema)` to create a new message.
 */
export const TurnOnGuestModeRequestSchema: GenMessage<TurnOnGuestModeRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 0);

/**
 * @generated from message hero.orgs.v1.TurnOnGuestModeResponse
 */
export type TurnOnGuestModeResponse = Message<"hero.orgs.v1.TurnOnGuestModeResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;
};

/**
 * Describes the message hero.orgs.v1.TurnOnGuestModeResponse.
 * Use `create(TurnOnGuestModeResponseSchema)` to create a new message.
 */
export const TurnOnGuestModeResponseSchema: GenMessage<TurnOnGuestModeResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 1);

/**
 * @generated from message hero.orgs.v1.TurnOffGuestModeRequest
 */
export type TurnOffGuestModeRequest = Message<"hero.orgs.v1.TurnOffGuestModeRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;
};

/**
 * Describes the message hero.orgs.v1.TurnOffGuestModeRequest.
 * Use `create(TurnOffGuestModeRequestSchema)` to create a new message.
 */
export const TurnOffGuestModeRequestSchema: GenMessage<TurnOffGuestModeRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 2);

/**
 * @generated from message hero.orgs.v1.TurnOffGuestModeResponse
 */
export type TurnOffGuestModeResponse = Message<"hero.orgs.v1.TurnOffGuestModeResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;
};

/**
 * Describes the message hero.orgs.v1.TurnOffGuestModeResponse.
 * Use `create(TurnOffGuestModeResponseSchema)` to create a new message.
 */
export const TurnOffGuestModeResponseSchema: GenMessage<TurnOffGuestModeResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 3);

/**
 * @generated from message hero.orgs.v1.GetOrgAPIUserPrivateByIdRequest
 */
export type GetOrgAPIUserPrivateByIdRequest = Message<"hero.orgs.v1.GetOrgAPIUserPrivateByIdRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;
};

/**
 * Describes the message hero.orgs.v1.GetOrgAPIUserPrivateByIdRequest.
 * Use `create(GetOrgAPIUserPrivateByIdRequestSchema)` to create a new message.
 */
export const GetOrgAPIUserPrivateByIdRequestSchema: GenMessage<GetOrgAPIUserPrivateByIdRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 4);

/**
 * @generated from message hero.orgs.v1.GetOrgAPIUserPrivateByIdResponse
 */
export type GetOrgAPIUserPrivateByIdResponse = Message<"hero.orgs.v1.GetOrgAPIUserPrivateByIdResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.OrgApiUserPrivate org_api_user = 1;
   */
  orgApiUser?: OrgApiUserPrivate;
};

/**
 * Describes the message hero.orgs.v1.GetOrgAPIUserPrivateByIdResponse.
 * Use `create(GetOrgAPIUserPrivateByIdResponseSchema)` to create a new message.
 */
export const GetOrgAPIUserPrivateByIdResponseSchema: GenMessage<GetOrgAPIUserPrivateByIdResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 5);

/**
 * @generated from message hero.orgs.v1.CreateOrgRequest
 */
export type CreateOrgRequest = Message<"hero.orgs.v1.CreateOrgRequest"> & {
  /**
   * @generated from field: hero.orgs.v1.Org org = 1;
   */
  org?: Org;
};

/**
 * Describes the message hero.orgs.v1.CreateOrgRequest.
 * Use `create(CreateOrgRequestSchema)` to create a new message.
 */
export const CreateOrgRequestSchema: GenMessage<CreateOrgRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 6);

/**
 * @generated from message hero.orgs.v1.CreateOrgResponse
 */
export type CreateOrgResponse = Message<"hero.orgs.v1.CreateOrgResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.Org org = 1;
   */
  org?: Org;
};

/**
 * Describes the message hero.orgs.v1.CreateOrgResponse.
 * Use `create(CreateOrgResponseSchema)` to create a new message.
 */
export const CreateOrgResponseSchema: GenMessage<CreateOrgResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 7);

/**
 * @generated from message hero.orgs.v1.UpdateOrgRequest
 */
export type UpdateOrgRequest = Message<"hero.orgs.v1.UpdateOrgRequest"> & {
  /**
   * @generated from field: hero.orgs.v1.Org org = 1;
   */
  org?: Org;
};

/**
 * Describes the message hero.orgs.v1.UpdateOrgRequest.
 * Use `create(UpdateOrgRequestSchema)` to create a new message.
 */
export const UpdateOrgRequestSchema: GenMessage<UpdateOrgRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 8);

/**
 * @generated from message hero.orgs.v1.UpdateOrgResponse
 */
export type UpdateOrgResponse = Message<"hero.orgs.v1.UpdateOrgResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.Org org = 1;
   */
  org?: Org;
};

/**
 * Describes the message hero.orgs.v1.UpdateOrgResponse.
 * Use `create(UpdateOrgResponseSchema)` to create a new message.
 */
export const UpdateOrgResponseSchema: GenMessage<UpdateOrgResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 9);

/**
 * @generated from message hero.orgs.v1.DeleteOrgRequest
 */
export type DeleteOrgRequest = Message<"hero.orgs.v1.DeleteOrgRequest"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;
};

/**
 * Describes the message hero.orgs.v1.DeleteOrgRequest.
 * Use `create(DeleteOrgRequestSchema)` to create a new message.
 */
export const DeleteOrgRequestSchema: GenMessage<DeleteOrgRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 10);

/**
 * @generated from message hero.orgs.v1.DeleteOrgResponse
 */
export type DeleteOrgResponse = Message<"hero.orgs.v1.DeleteOrgResponse"> & {
};

/**
 * Describes the message hero.orgs.v1.DeleteOrgResponse.
 * Use `create(DeleteOrgResponseSchema)` to create a new message.
 */
export const DeleteOrgResponseSchema: GenMessage<DeleteOrgResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 11);

/**
 * @generated from message hero.orgs.v1.GetOrgRequest
 */
export type GetOrgRequest = Message<"hero.orgs.v1.GetOrgRequest"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;
};

/**
 * Describes the message hero.orgs.v1.GetOrgRequest.
 * Use `create(GetOrgRequestSchema)` to create a new message.
 */
export const GetOrgRequestSchema: GenMessage<GetOrgRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 12);

/**
 * @generated from message hero.orgs.v1.GetOrgResponse
 */
export type GetOrgResponse = Message<"hero.orgs.v1.GetOrgResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.Org org = 1;
   */
  org?: Org;
};

/**
 * Describes the message hero.orgs.v1.GetOrgResponse.
 * Use `create(GetOrgResponseSchema)` to create a new message.
 */
export const GetOrgResponseSchema: GenMessage<GetOrgResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 13);

/**
 * @generated from message hero.orgs.v1.ListOrgsRequest
 */
export type ListOrgsRequest = Message<"hero.orgs.v1.ListOrgsRequest"> & {
};

/**
 * Describes the message hero.orgs.v1.ListOrgsRequest.
 * Use `create(ListOrgsRequestSchema)` to create a new message.
 */
export const ListOrgsRequestSchema: GenMessage<ListOrgsRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 14);

/**
 * @generated from message hero.orgs.v1.ListOrgsResponse
 */
export type ListOrgsResponse = Message<"hero.orgs.v1.ListOrgsResponse"> & {
  /**
   * @generated from field: repeated hero.orgs.v1.Org orgs = 1;
   */
  orgs: Org[];
};

/**
 * Describes the message hero.orgs.v1.ListOrgsResponse.
 * Use `create(ListOrgsResponseSchema)` to create a new message.
 */
export const ListOrgsResponseSchema: GenMessage<ListOrgsResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 15);

/**
 * @generated from message hero.orgs.v1.ValidateOrgCredsRequest
 */
export type ValidateOrgCredsRequest = Message<"hero.orgs.v1.ValidateOrgCredsRequest"> & {
  /**
   * @generated from field: string username = 1;
   */
  username: string;

  /**
   * @generated from field: string password = 2;
   */
  password: string;
};

/**
 * Describes the message hero.orgs.v1.ValidateOrgCredsRequest.
 * Use `create(ValidateOrgCredsRequestSchema)` to create a new message.
 */
export const ValidateOrgCredsRequestSchema: GenMessage<ValidateOrgCredsRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 16);

/**
 * @generated from message hero.orgs.v1.ValidateOrgCredsResponse
 */
export type ValidateOrgCredsResponse = Message<"hero.orgs.v1.ValidateOrgCredsResponse"> & {
  /**
   * @generated from field: bool valid = 1;
   */
  valid: boolean;

  /**
   * @generated from field: hero.orgs.v1.OrgApiUser org_api_user = 2;
   */
  orgApiUser?: OrgApiUser;
};

/**
 * Describes the message hero.orgs.v1.ValidateOrgCredsResponse.
 * Use `create(ValidateOrgCredsResponseSchema)` to create a new message.
 */
export const ValidateOrgCredsResponseSchema: GenMessage<ValidateOrgCredsResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 17);

/**
 * @generated from message hero.orgs.v1.OrgApiUser
 */
export type OrgApiUser = Message<"hero.orgs.v1.OrgApiUser"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * @generated from field: string username = 3;
   */
  username: string;

  /**
   * @generated from field: string encrypted_password = 4;
   */
  encryptedPassword: string;

  /**
   * @generated from field: string hashed_password = 5;
   */
  hashedPassword: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 6;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 7;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message hero.orgs.v1.OrgApiUser.
 * Use `create(OrgApiUserSchema)` to create a new message.
 */
export const OrgApiUserSchema: GenMessage<OrgApiUser> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 18);

/**
 * @generated from message hero.orgs.v1.OrgApiUserPrivate
 */
export type OrgApiUserPrivate = Message<"hero.orgs.v1.OrgApiUserPrivate"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * @generated from field: string username = 3;
   */
  username: string;

  /**
   * @generated from field: string encrypted_password = 4;
   */
  encryptedPassword: string;

  /**
   * @generated from field: string hashed_password = 5;
   */
  hashedPassword: string;

  /**
   * @generated from field: string raw_password = 6;
   */
  rawPassword: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 7;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 8;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message hero.orgs.v1.OrgApiUserPrivate.
 * Use `create(OrgApiUserPrivateSchema)` to create a new message.
 */
export const OrgApiUserPrivateSchema: GenMessage<OrgApiUserPrivate> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 19);

/**
 * @generated from message hero.orgs.v1.OrgQueue
 */
export type OrgQueue = Message<"hero.orgs.v1.OrgQueue"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;

  /**
   * @generated from field: string friendly_name = 2;
   */
  friendlyName: string;

  /**
   * @generated from field: string twilio_queue_sid = 3;
   */
  twilioQueueSid: string;

  /**
   * @generated from field: string description = 4;
   */
  description: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 5;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 6;
   */
  updatedAt?: Timestamp;

  /**
   * @generated from field: int32 org_id = 7;
   */
  orgId: number;
};

/**
 * Describes the message hero.orgs.v1.OrgQueue.
 * Use `create(OrgQueueSchema)` to create a new message.
 */
export const OrgQueueSchema: GenMessage<OrgQueue> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 20);

/**
 * @generated from message hero.orgs.v1.InsertOrgQueueRequest
 */
export type InsertOrgQueueRequest = Message<"hero.orgs.v1.InsertOrgQueueRequest"> & {
  /**
   * @generated from field: string friendly_name = 1;
   */
  friendlyName: string;

  /**
   * @generated from field: string twilio_queue_sid = 2;
   */
  twilioQueueSid: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: int32 org_id = 4;
   */
  orgId: number;
};

/**
 * Describes the message hero.orgs.v1.InsertOrgQueueRequest.
 * Use `create(InsertOrgQueueRequestSchema)` to create a new message.
 */
export const InsertOrgQueueRequestSchema: GenMessage<InsertOrgQueueRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 21);

/**
 * @generated from message hero.orgs.v1.InsertOrgQueueResponse
 */
export type InsertOrgQueueResponse = Message<"hero.orgs.v1.InsertOrgQueueResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.OrgQueue org_queue = 1;
   */
  orgQueue?: OrgQueue;
};

/**
 * Describes the message hero.orgs.v1.InsertOrgQueueResponse.
 * Use `create(InsertOrgQueueResponseSchema)` to create a new message.
 */
export const InsertOrgQueueResponseSchema: GenMessage<InsertOrgQueueResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 22);

/**
 * @generated from message hero.orgs.v1.CreateOrgTwilioQueueRequest
 */
export type CreateOrgTwilioQueueRequest = Message<"hero.orgs.v1.CreateOrgTwilioQueueRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * @generated from field: string friendly_name = 2;
   */
  friendlyName: string;
};

/**
 * Describes the message hero.orgs.v1.CreateOrgTwilioQueueRequest.
 * Use `create(CreateOrgTwilioQueueRequestSchema)` to create a new message.
 */
export const CreateOrgTwilioQueueRequestSchema: GenMessage<CreateOrgTwilioQueueRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 23);

/**
 * @generated from message hero.orgs.v1.CreateOrgTwilioQueueResponse
 */
export type CreateOrgTwilioQueueResponse = Message<"hero.orgs.v1.CreateOrgTwilioQueueResponse"> & {
  /**
   * @generated from field: string queue_sid = 1;
   */
  queueSid: string;
};

/**
 * Describes the message hero.orgs.v1.CreateOrgTwilioQueueResponse.
 * Use `create(CreateOrgTwilioQueueResponseSchema)` to create a new message.
 */
export const CreateOrgTwilioQueueResponseSchema: GenMessage<CreateOrgTwilioQueueResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 24);

/**
 * @generated from message hero.orgs.v1.Org
 */
export type Org = Message<"hero.orgs.v1.Org"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: repeated string domains = 3;
   */
  domains: string[];

  /**
   * @generated from field: string twiml_app_sid = 4;
   */
  twimlAppSid: string;

  /**
   * @generated from field: string twilio_number = 5;
   */
  twilioNumber: string;

  /**
   * @generated from field: string twilio_number_sid = 6;
   */
  twilioNumberSid: string;

  /**
   * @generated from field: hero.orgs.v1.ServiceType service_type = 7;
   */
  serviceType: ServiceType;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 8;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 9;
   */
  updatedAt?: Timestamp;

  /**
   * ID of the template used to setup the org's features
   *
   * @generated from field: string template_id = 10;
   */
  templateId: string;

  /**
   * Call Forwarding Configuration
   *
   * Whether call forwarding is enabled for this organization
   *
   * @generated from field: bool is_call_forwarding_enabled = 11;
   */
  isCallForwardingEnabled: boolean;

  /**
   * The primary phone number for the organization (used for Twilio setup and call forwarding)
   *
   * @generated from field: string primary_phone_number = 12;
   */
  primaryPhoneNumber: string;

  /**
   * Type of call forwarding: "PSTN" (standard phone) or "SIP" (VoIP)
   *
   * @generated from field: string call_forwarding_type = 13;
   */
  callForwardingType: string;

  /**
   * SIP URI for SIP forwarding (format: sip:<EMAIL>)
   *
   * @generated from field: string sip_uri = 14;
   */
  sipUri: string;
};

/**
 * Describes the message hero.orgs.v1.Org.
 * Use `create(OrgSchema)` to create a new message.
 */
export const OrgSchema: GenMessage<Org> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 25);

/**
 * @generated from message hero.orgs.v1.CreateOrgAPIUserRequest
 */
export type CreateOrgAPIUserRequest = Message<"hero.orgs.v1.CreateOrgAPIUserRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;
};

/**
 * Describes the message hero.orgs.v1.CreateOrgAPIUserRequest.
 * Use `create(CreateOrgAPIUserRequestSchema)` to create a new message.
 */
export const CreateOrgAPIUserRequestSchema: GenMessage<CreateOrgAPIUserRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 26);

/**
 * @generated from message hero.orgs.v1.CreateOrgAPIUserResponse
 */
export type CreateOrgAPIUserResponse = Message<"hero.orgs.v1.CreateOrgAPIUserResponse"> & {
  /**
   * @generated from field: string username = 1;
   */
  username: string;

  /**
   * @generated from field: string encrypted_password = 2;
   */
  encryptedPassword: string;

  /**
   * @generated from field: string hashed_password = 3;
   */
  hashedPassword: string;
};

/**
 * Describes the message hero.orgs.v1.CreateOrgAPIUserResponse.
 * Use `create(CreateOrgAPIUserResponseSchema)` to create a new message.
 */
export const CreateOrgAPIUserResponseSchema: GenMessage<CreateOrgAPIUserResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 27);

/**
 * @generated from message hero.orgs.v1.GetZelloChannelsRequest
 */
export type GetZelloChannelsRequest = Message<"hero.orgs.v1.GetZelloChannelsRequest"> & {
};

/**
 * Describes the message hero.orgs.v1.GetZelloChannelsRequest.
 * Use `create(GetZelloChannelsRequestSchema)` to create a new message.
 */
export const GetZelloChannelsRequestSchema: GenMessage<GetZelloChannelsRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 28);

/**
 * @generated from message hero.orgs.v1.GetZelloChannelsResponse
 */
export type GetZelloChannelsResponse = Message<"hero.orgs.v1.GetZelloChannelsResponse"> & {
  /**
   * @generated from field: repeated hero.orgs.v1.ZelloChannel zello_channels = 1;
   */
  zelloChannels: ZelloChannel[];
};

/**
 * Describes the message hero.orgs.v1.GetZelloChannelsResponse.
 * Use `create(GetZelloChannelsResponseSchema)` to create a new message.
 */
export const GetZelloChannelsResponseSchema: GenMessage<GetZelloChannelsResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 29);

/**
 * @generated from message hero.orgs.v1.ZelloChannel
 */
export type ZelloChannel = Message<"hero.orgs.v1.ZelloChannel"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * @generated from field: string zello_channel_id = 3;
   */
  zelloChannelId: string;

  /**
   * @generated from field: string display_name = 4;
   */
  displayName: string;
};

/**
 * Describes the message hero.orgs.v1.ZelloChannel.
 * Use `create(ZelloChannelSchema)` to create a new message.
 */
export const ZelloChannelSchema: GenMessage<ZelloChannel> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 30);

/**
 * @generated from message hero.orgs.v1.CreateCognitoUserRequest
 */
export type CreateCognitoUserRequest = Message<"hero.orgs.v1.CreateCognitoUserRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * @generated from field: string username = 2;
   */
  username: string;

  /**
   * @generated from field: string email = 3;
   */
  email: string;

  /**
   * @generated from field: string password = 4;
   */
  password: string;
};

/**
 * Describes the message hero.orgs.v1.CreateCognitoUserRequest.
 * Use `create(CreateCognitoUserRequestSchema)` to create a new message.
 */
export const CreateCognitoUserRequestSchema: GenMessage<CreateCognitoUserRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 31);

/**
 * @generated from message hero.orgs.v1.CreateCognitoUserResponse
 */
export type CreateCognitoUserResponse = Message<"hero.orgs.v1.CreateCognitoUserResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * The Cognito user ID (sub)
   *
   * @generated from field: string cognito_sub_id = 2;
   */
  cognitoSubId: string;
};

/**
 * Describes the message hero.orgs.v1.CreateCognitoUserResponse.
 * Use `create(CreateCognitoUserResponseSchema)` to create a new message.
 */
export const CreateCognitoUserResponseSchema: GenMessage<CreateCognitoUserResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 32);

/**
 * Organization Contact Book Messages
 *
 * @generated from message hero.orgs.v1.ContactRecord
 */
export type ContactRecord = Message<"hero.orgs.v1.ContactRecord"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * @generated from field: string phone = 4;
   */
  phone: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 5;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 6;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message hero.orgs.v1.ContactRecord.
 * Use `create(ContactRecordSchema)` to create a new message.
 */
export const ContactRecordSchema: GenMessage<ContactRecord> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 33);

/**
 * @generated from message hero.orgs.v1.AddToContactBookRequest
 */
export type AddToContactBookRequest = Message<"hero.orgs.v1.AddToContactBookRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string phone = 3;
   */
  phone: string;
};

/**
 * Describes the message hero.orgs.v1.AddToContactBookRequest.
 * Use `create(AddToContactBookRequestSchema)` to create a new message.
 */
export const AddToContactBookRequestSchema: GenMessage<AddToContactBookRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 34);

/**
 * @generated from message hero.orgs.v1.AddToContactBookResponse
 */
export type AddToContactBookResponse = Message<"hero.orgs.v1.AddToContactBookResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.ContactRecord contact = 1;
   */
  contact?: ContactRecord;
};

/**
 * Describes the message hero.orgs.v1.AddToContactBookResponse.
 * Use `create(AddToContactBookResponseSchema)` to create a new message.
 */
export const AddToContactBookResponseSchema: GenMessage<AddToContactBookResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 35);

/**
 * @generated from message hero.orgs.v1.UpdateContactInContactBookRequest
 */
export type UpdateContactInContactBookRequest = Message<"hero.orgs.v1.UpdateContactInContactBookRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string phone = 3;
   */
  phone: string;
};

/**
 * Describes the message hero.orgs.v1.UpdateContactInContactBookRequest.
 * Use `create(UpdateContactInContactBookRequestSchema)` to create a new message.
 */
export const UpdateContactInContactBookRequestSchema: GenMessage<UpdateContactInContactBookRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 36);

/**
 * @generated from message hero.orgs.v1.UpdateContactInContactBookResponse
 */
export type UpdateContactInContactBookResponse = Message<"hero.orgs.v1.UpdateContactInContactBookResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.ContactRecord contact = 1;
   */
  contact?: ContactRecord;
};

/**
 * Describes the message hero.orgs.v1.UpdateContactInContactBookResponse.
 * Use `create(UpdateContactInContactBookResponseSchema)` to create a new message.
 */
export const UpdateContactInContactBookResponseSchema: GenMessage<UpdateContactInContactBookResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 37);

/**
 * @generated from message hero.orgs.v1.DeleteFromContactBookRequest
 */
export type DeleteFromContactBookRequest = Message<"hero.orgs.v1.DeleteFromContactBookRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.orgs.v1.DeleteFromContactBookRequest.
 * Use `create(DeleteFromContactBookRequestSchema)` to create a new message.
 */
export const DeleteFromContactBookRequestSchema: GenMessage<DeleteFromContactBookRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 38);

/**
 * @generated from message hero.orgs.v1.DeleteFromContactBookResponse
 */
export type DeleteFromContactBookResponse = Message<"hero.orgs.v1.DeleteFromContactBookResponse"> & {
};

/**
 * Describes the message hero.orgs.v1.DeleteFromContactBookResponse.
 * Use `create(DeleteFromContactBookResponseSchema)` to create a new message.
 */
export const DeleteFromContactBookResponseSchema: GenMessage<DeleteFromContactBookResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 39);

/**
 * @generated from message hero.orgs.v1.GetContactFromContactBookRequest
 */
export type GetContactFromContactBookRequest = Message<"hero.orgs.v1.GetContactFromContactBookRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.orgs.v1.GetContactFromContactBookRequest.
 * Use `create(GetContactFromContactBookRequestSchema)` to create a new message.
 */
export const GetContactFromContactBookRequestSchema: GenMessage<GetContactFromContactBookRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 40);

/**
 * @generated from message hero.orgs.v1.GetContactFromContactBookResponse
 */
export type GetContactFromContactBookResponse = Message<"hero.orgs.v1.GetContactFromContactBookResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.ContactRecord contact = 1;
   */
  contact?: ContactRecord;
};

/**
 * Describes the message hero.orgs.v1.GetContactFromContactBookResponse.
 * Use `create(GetContactFromContactBookResponseSchema)` to create a new message.
 */
export const GetContactFromContactBookResponseSchema: GenMessage<GetContactFromContactBookResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 41);

/**
 * @generated from message hero.orgs.v1.ListContactsInContactBookRequest
 */
export type ListContactsInContactBookRequest = Message<"hero.orgs.v1.ListContactsInContactBookRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * Token for pagination (empty for first page)
   *
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  /**
   * Number of contacts to return (max 100)
   *
   * @generated from field: int32 page_size = 3;
   */
  pageSize: number;
};

/**
 * Describes the message hero.orgs.v1.ListContactsInContactBookRequest.
 * Use `create(ListContactsInContactBookRequestSchema)` to create a new message.
 */
export const ListContactsInContactBookRequestSchema: GenMessage<ListContactsInContactBookRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 42);

/**
 * @generated from message hero.orgs.v1.ListContactsInContactBookResponse
 */
export type ListContactsInContactBookResponse = Message<"hero.orgs.v1.ListContactsInContactBookResponse"> & {
  /**
   * @generated from field: repeated hero.orgs.v1.ContactRecord contacts = 1;
   */
  contacts: ContactRecord[];

  /**
   * Token for next page (empty if no more pages)
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;

  /**
   * Total number of contacts in the contact book
   *
   * @generated from field: int32 total_count = 3;
   */
  totalCount: number;
};

/**
 * Describes the message hero.orgs.v1.ListContactsInContactBookResponse.
 * Use `create(ListContactsInContactBookResponseSchema)` to create a new message.
 */
export const ListContactsInContactBookResponseSchema: GenMessage<ListContactsInContactBookResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 43);

/**
 * @generated from message hero.orgs.v1.GetContactByPhoneNumberRequest
 */
export type GetContactByPhoneNumberRequest = Message<"hero.orgs.v1.GetContactByPhoneNumberRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * @generated from field: string phone = 2;
   */
  phone: string;
};

/**
 * Describes the message hero.orgs.v1.GetContactByPhoneNumberRequest.
 * Use `create(GetContactByPhoneNumberRequestSchema)` to create a new message.
 */
export const GetContactByPhoneNumberRequestSchema: GenMessage<GetContactByPhoneNumberRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 44);

/**
 * @generated from message hero.orgs.v1.GetContactByPhoneNumberResponse
 */
export type GetContactByPhoneNumberResponse = Message<"hero.orgs.v1.GetContactByPhoneNumberResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.ContactRecord contact = 1;
   */
  contact?: ContactRecord;
};

/**
 * Describes the message hero.orgs.v1.GetContactByPhoneNumberResponse.
 * Use `create(GetContactByPhoneNumberResponseSchema)` to create a new message.
 */
export const GetContactByPhoneNumberResponseSchema: GenMessage<GetContactByPhoneNumberResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 45);

/**
 * Pre-registration user mapping messages
 *
 * @generated from message hero.orgs.v1.PreRegistrationUserMapping
 */
export type PreRegistrationUserMapping = Message<"hero.orgs.v1.PreRegistrationUserMapping"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string email = 2;
   */
  email: string;

  /**
   * @generated from field: int32 org_id = 3;
   */
  orgId: number;

  /**
   * Human-readable label for asset type (e.g., "Supervisor", "Dispatcher")
   *
   * @generated from field: string asset_type_label = 4;
   */
  assetTypeLabel: string;

  /**
   * @generated from field: hero.assets.v2.AssetType asset_type = 5;
   */
  assetType: AssetType;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 6;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp used_at = 7;
   */
  usedAt?: Timestamp;

  /**
   * @generated from field: string created_by = 8;
   */
  createdBy: string;
};

/**
 * Describes the message hero.orgs.v1.PreRegistrationUserMapping.
 * Use `create(PreRegistrationUserMappingSchema)` to create a new message.
 */
export const PreRegistrationUserMappingSchema: GenMessage<PreRegistrationUserMapping> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 46);

/**
 * @generated from message hero.orgs.v1.CreatePreRegistrationMappingRequest
 */
export type CreatePreRegistrationMappingRequest = Message<"hero.orgs.v1.CreatePreRegistrationMappingRequest"> & {
  /**
   * @generated from field: string email = 1;
   */
  email: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * Human-readable label for asset type (e.g., "Supervisor", "Dispatcher")
   *
   * @generated from field: string asset_type_label = 3;
   */
  assetTypeLabel: string;

  /**
   * Optional, defaults to RESPONDER
   *
   * @generated from field: hero.assets.v2.AssetType asset_type = 4;
   */
  assetType: AssetType;

  /**
   * @generated from field: string created_by = 5;
   */
  createdBy: string;
};

/**
 * Describes the message hero.orgs.v1.CreatePreRegistrationMappingRequest.
 * Use `create(CreatePreRegistrationMappingRequestSchema)` to create a new message.
 */
export const CreatePreRegistrationMappingRequestSchema: GenMessage<CreatePreRegistrationMappingRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 47);

/**
 * @generated from message hero.orgs.v1.CreatePreRegistrationMappingResponse
 */
export type CreatePreRegistrationMappingResponse = Message<"hero.orgs.v1.CreatePreRegistrationMappingResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.PreRegistrationUserMapping mapping = 1;
   */
  mapping?: PreRegistrationUserMapping;
};

/**
 * Describes the message hero.orgs.v1.CreatePreRegistrationMappingResponse.
 * Use `create(CreatePreRegistrationMappingResponseSchema)` to create a new message.
 */
export const CreatePreRegistrationMappingResponseSchema: GenMessage<CreatePreRegistrationMappingResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 48);

/**
 * @generated from message hero.orgs.v1.CreatePreRegistrationMappingsRequest
 */
export type CreatePreRegistrationMappingsRequest = Message<"hero.orgs.v1.CreatePreRegistrationMappingsRequest"> & {
  /**
   * @generated from field: repeated hero.orgs.v1.CreatePreRegistrationMappingRequest mappings = 1;
   */
  mappings: CreatePreRegistrationMappingRequest[];
};

/**
 * Describes the message hero.orgs.v1.CreatePreRegistrationMappingsRequest.
 * Use `create(CreatePreRegistrationMappingsRequestSchema)` to create a new message.
 */
export const CreatePreRegistrationMappingsRequestSchema: GenMessage<CreatePreRegistrationMappingsRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 49);

/**
 * @generated from message hero.orgs.v1.CreatePreRegistrationMappingsResponse
 */
export type CreatePreRegistrationMappingsResponse = Message<"hero.orgs.v1.CreatePreRegistrationMappingsResponse"> & {
  /**
   * @generated from field: repeated hero.orgs.v1.PreRegistrationUserMapping mappings = 1;
   */
  mappings: PreRegistrationUserMapping[];

  /**
   * Error messages for failed mappings
   *
   * @generated from field: repeated string errors = 2;
   */
  errors: string[];
};

/**
 * Describes the message hero.orgs.v1.CreatePreRegistrationMappingsResponse.
 * Use `create(CreatePreRegistrationMappingsResponseSchema)` to create a new message.
 */
export const CreatePreRegistrationMappingsResponseSchema: GenMessage<CreatePreRegistrationMappingsResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 50);

/**
 * @generated from message hero.orgs.v1.GetPreRegistrationMappingRequest
 */
export type GetPreRegistrationMappingRequest = Message<"hero.orgs.v1.GetPreRegistrationMappingRequest"> & {
  /**
   * @generated from field: string email = 1;
   */
  email: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;
};

/**
 * Describes the message hero.orgs.v1.GetPreRegistrationMappingRequest.
 * Use `create(GetPreRegistrationMappingRequestSchema)` to create a new message.
 */
export const GetPreRegistrationMappingRequestSchema: GenMessage<GetPreRegistrationMappingRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 51);

/**
 * @generated from message hero.orgs.v1.GetPreRegistrationMappingResponse
 */
export type GetPreRegistrationMappingResponse = Message<"hero.orgs.v1.GetPreRegistrationMappingResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.PreRegistrationUserMapping mapping = 1;
   */
  mapping?: PreRegistrationUserMapping;
};

/**
 * Describes the message hero.orgs.v1.GetPreRegistrationMappingResponse.
 * Use `create(GetPreRegistrationMappingResponseSchema)` to create a new message.
 */
export const GetPreRegistrationMappingResponseSchema: GenMessage<GetPreRegistrationMappingResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 52);

/**
 * @generated from message hero.orgs.v1.ListPreRegistrationMappingsRequest
 */
export type ListPreRegistrationMappingsRequest = Message<"hero.orgs.v1.ListPreRegistrationMappingsRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  /**
   * @generated from field: int32 page_size = 3;
   */
  pageSize: number;

  /**
   * Whether to include already used mappings
   *
   * @generated from field: bool include_used = 4;
   */
  includeUsed: boolean;
};

/**
 * Describes the message hero.orgs.v1.ListPreRegistrationMappingsRequest.
 * Use `create(ListPreRegistrationMappingsRequestSchema)` to create a new message.
 */
export const ListPreRegistrationMappingsRequestSchema: GenMessage<ListPreRegistrationMappingsRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 53);

/**
 * @generated from message hero.orgs.v1.ListPreRegistrationMappingsResponse
 */
export type ListPreRegistrationMappingsResponse = Message<"hero.orgs.v1.ListPreRegistrationMappingsResponse"> & {
  /**
   * @generated from field: repeated hero.orgs.v1.PreRegistrationUserMapping mappings = 1;
   */
  mappings: PreRegistrationUserMapping[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;

  /**
   * @generated from field: int32 total_count = 3;
   */
  totalCount: number;
};

/**
 * Describes the message hero.orgs.v1.ListPreRegistrationMappingsResponse.
 * Use `create(ListPreRegistrationMappingsResponseSchema)` to create a new message.
 */
export const ListPreRegistrationMappingsResponseSchema: GenMessage<ListPreRegistrationMappingsResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 54);

/**
 * @generated from message hero.orgs.v1.UpdatePreRegistrationMappingRequest
 */
export type UpdatePreRegistrationMappingRequest = Message<"hero.orgs.v1.UpdatePreRegistrationMappingRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Human-readable label for asset type
   *
   * @generated from field: string asset_type_label = 2;
   */
  assetTypeLabel: string;

  /**
   * @generated from field: hero.assets.v2.AssetType asset_type = 3;
   */
  assetType: AssetType;
};

/**
 * Describes the message hero.orgs.v1.UpdatePreRegistrationMappingRequest.
 * Use `create(UpdatePreRegistrationMappingRequestSchema)` to create a new message.
 */
export const UpdatePreRegistrationMappingRequestSchema: GenMessage<UpdatePreRegistrationMappingRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 55);

/**
 * @generated from message hero.orgs.v1.UpdatePreRegistrationMappingResponse
 */
export type UpdatePreRegistrationMappingResponse = Message<"hero.orgs.v1.UpdatePreRegistrationMappingResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.PreRegistrationUserMapping mapping = 1;
   */
  mapping?: PreRegistrationUserMapping;
};

/**
 * Describes the message hero.orgs.v1.UpdatePreRegistrationMappingResponse.
 * Use `create(UpdatePreRegistrationMappingResponseSchema)` to create a new message.
 */
export const UpdatePreRegistrationMappingResponseSchema: GenMessage<UpdatePreRegistrationMappingResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 56);

/**
 * @generated from message hero.orgs.v1.DeletePreRegistrationMappingRequest
 */
export type DeletePreRegistrationMappingRequest = Message<"hero.orgs.v1.DeletePreRegistrationMappingRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.orgs.v1.DeletePreRegistrationMappingRequest.
 * Use `create(DeletePreRegistrationMappingRequestSchema)` to create a new message.
 */
export const DeletePreRegistrationMappingRequestSchema: GenMessage<DeletePreRegistrationMappingRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 57);

/**
 * @generated from message hero.orgs.v1.DeletePreRegistrationMappingResponse
 */
export type DeletePreRegistrationMappingResponse = Message<"hero.orgs.v1.DeletePreRegistrationMappingResponse"> & {
};

/**
 * Describes the message hero.orgs.v1.DeletePreRegistrationMappingResponse.
 * Use `create(DeletePreRegistrationMappingResponseSchema)` to create a new message.
 */
export const DeletePreRegistrationMappingResponseSchema: GenMessage<DeletePreRegistrationMappingResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 58);

/**
 * @generated from message hero.orgs.v1.MarkMappingAsUsedRequest
 */
export type MarkMappingAsUsedRequest = Message<"hero.orgs.v1.MarkMappingAsUsedRequest"> & {
  /**
   * @generated from field: string mapping_id = 1;
   */
  mappingId: string;
};

/**
 * Describes the message hero.orgs.v1.MarkMappingAsUsedRequest.
 * Use `create(MarkMappingAsUsedRequestSchema)` to create a new message.
 */
export const MarkMappingAsUsedRequestSchema: GenMessage<MarkMappingAsUsedRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 59);

/**
 * @generated from message hero.orgs.v1.MarkMappingAsUsedResponse
 */
export type MarkMappingAsUsedResponse = Message<"hero.orgs.v1.MarkMappingAsUsedResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;
};

/**
 * Describes the message hero.orgs.v1.MarkMappingAsUsedResponse.
 * Use `create(MarkMappingAsUsedResponseSchema)` to create a new message.
 */
export const MarkMappingAsUsedResponseSchema: GenMessage<MarkMappingAsUsedResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 60);

/**
 * @generated from enum hero.orgs.v1.ServiceType
 */
export enum ServiceType {
  /**
   * @generated from enum value: SERVICE_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: SERVICE_TYPE_DEMO = 1;
   */
  DEMO = 1,

  /**
   * @generated from enum value: SERVICE_TYPE_PRODUCTION = 2;
   */
  PRODUCTION = 2,
}

/**
 * Describes the enum hero.orgs.v1.ServiceType.
 */
export const ServiceTypeSchema: GenEnum<ServiceType> = /*@__PURE__*/
  enumDesc(file_hero_orgs_v1_orgs, 0);

/**
 * @generated from service hero.orgs.v1.OrgsService
 */
export const OrgsService: GenService<{
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.CreateOrg
   */
  createOrg: {
    methodKind: "unary";
    input: typeof CreateOrgRequestSchema;
    output: typeof CreateOrgResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.UpdateOrg
   */
  updateOrg: {
    methodKind: "unary";
    input: typeof UpdateOrgRequestSchema;
    output: typeof UpdateOrgResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.DeleteOrg
   */
  deleteOrg: {
    methodKind: "unary";
    input: typeof DeleteOrgRequestSchema;
    output: typeof DeleteOrgResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.GetOrg
   */
  getOrg: {
    methodKind: "unary";
    input: typeof GetOrgRequestSchema;
    output: typeof GetOrgResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.ListOrgs
   */
  listOrgs: {
    methodKind: "unary";
    input: typeof ListOrgsRequestSchema;
    output: typeof ListOrgsResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.ValidateOrgCreds
   */
  validateOrgCreds: {
    methodKind: "unary";
    input: typeof ValidateOrgCredsRequestSchema;
    output: typeof ValidateOrgCredsResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.CreateOrgAPIUser
   */
  createOrgAPIUser: {
    methodKind: "unary";
    input: typeof CreateOrgAPIUserRequestSchema;
    output: typeof CreateOrgAPIUserResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.GetOrgAPIUserPrivateById
   */
  getOrgAPIUserPrivateById: {
    methodKind: "unary";
    input: typeof GetOrgAPIUserPrivateByIdRequestSchema;
    output: typeof GetOrgAPIUserPrivateByIdResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.GetZelloChannels
   */
  getZelloChannels: {
    methodKind: "unary";
    input: typeof GetZelloChannelsRequestSchema;
    output: typeof GetZelloChannelsResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.InsertOrgQueue
   */
  insertOrgQueue: {
    methodKind: "unary";
    input: typeof InsertOrgQueueRequestSchema;
    output: typeof InsertOrgQueueResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.CreateOrgTwilioQueue
   */
  createOrgTwilioQueue: {
    methodKind: "unary";
    input: typeof CreateOrgTwilioQueueRequestSchema;
    output: typeof CreateOrgTwilioQueueResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.CreateCognitoUser
   */
  createCognitoUser: {
    methodKind: "unary";
    input: typeof CreateCognitoUserRequestSchema;
    output: typeof CreateCognitoUserResponseSchema;
  },
  /**
   * Organization Contact Book Management
   *
   * @generated from rpc hero.orgs.v1.OrgsService.AddToContactBook
   */
  addToContactBook: {
    methodKind: "unary";
    input: typeof AddToContactBookRequestSchema;
    output: typeof AddToContactBookResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.UpdateContactInContactBook
   */
  updateContactInContactBook: {
    methodKind: "unary";
    input: typeof UpdateContactInContactBookRequestSchema;
    output: typeof UpdateContactInContactBookResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.DeleteFromContactBook
   */
  deleteFromContactBook: {
    methodKind: "unary";
    input: typeof DeleteFromContactBookRequestSchema;
    output: typeof DeleteFromContactBookResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.GetContactFromContactBook
   */
  getContactFromContactBook: {
    methodKind: "unary";
    input: typeof GetContactFromContactBookRequestSchema;
    output: typeof GetContactFromContactBookResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.ListContactsInContactBook
   */
  listContactsInContactBook: {
    methodKind: "unary";
    input: typeof ListContactsInContactBookRequestSchema;
    output: typeof ListContactsInContactBookResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.GetContactByPhoneNumber
   */
  getContactByPhoneNumber: {
    methodKind: "unary";
    input: typeof GetContactByPhoneNumberRequestSchema;
    output: typeof GetContactByPhoneNumberResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.TurnOnGuestMode
   */
  turnOnGuestMode: {
    methodKind: "unary";
    input: typeof TurnOnGuestModeRequestSchema;
    output: typeof TurnOnGuestModeResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.TurnOffGuestMode
   */
  turnOffGuestMode: {
    methodKind: "unary";
    input: typeof TurnOffGuestModeRequestSchema;
    output: typeof TurnOffGuestModeResponseSchema;
  },
  /**
   * Pre-registration user mapping management
   *
   * @generated from rpc hero.orgs.v1.OrgsService.CreatePreRegistrationMapping
   */
  createPreRegistrationMapping: {
    methodKind: "unary";
    input: typeof CreatePreRegistrationMappingRequestSchema;
    output: typeof CreatePreRegistrationMappingResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.CreatePreRegistrationMappings
   */
  createPreRegistrationMappings: {
    methodKind: "unary";
    input: typeof CreatePreRegistrationMappingsRequestSchema;
    output: typeof CreatePreRegistrationMappingsResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.GetPreRegistrationMapping
   */
  getPreRegistrationMapping: {
    methodKind: "unary";
    input: typeof GetPreRegistrationMappingRequestSchema;
    output: typeof GetPreRegistrationMappingResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.ListPreRegistrationMappings
   */
  listPreRegistrationMappings: {
    methodKind: "unary";
    input: typeof ListPreRegistrationMappingsRequestSchema;
    output: typeof ListPreRegistrationMappingsResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.UpdatePreRegistrationMapping
   */
  updatePreRegistrationMapping: {
    methodKind: "unary";
    input: typeof UpdatePreRegistrationMappingRequestSchema;
    output: typeof UpdatePreRegistrationMappingResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.DeletePreRegistrationMapping
   */
  deletePreRegistrationMapping: {
    methodKind: "unary";
    input: typeof DeletePreRegistrationMappingRequestSchema;
    output: typeof DeletePreRegistrationMappingResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.MarkMappingAsUsed
   */
  markMappingAsUsed: {
    methodKind: "unary";
    input: typeof MarkMappingAsUsedRequestSchema;
    output: typeof MarkMappingAsUsedResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_orgs_v1_orgs, 0);

