// @generated by protoc-gen-es v2.6.1 with parameter "target=ts"
// @generated from file hero/command/v1/command.proto (package hero.command.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/command/v1/command.proto.
 */
export const file_hero_command_v1_command: GenFile = /*@__PURE__*/
  fileDesc("Ch1oZXJvL2NvbW1hbmQvdjEvY29tbWFuZC5wcm90bxIPaGVyby5jb21tYW5kLnYxIhEKD0dldFN0YXRlUmVxdWVzdCI5ChBHZXRTdGF0ZVJlc3BvbnNlEiUKBXN0YXRlGAEgASgOMhYuaGVyby5jb21tYW5kLnYxLlN0YXRlIjgKD1NldFN0YXRlUmVxdWVzdBIlCgVzdGF0ZRgBIAEoDjIWLmhlcm8uY29tbWFuZC52MS5TdGF0ZSISChBTZXRTdGF0ZVJlc3BvbnNlKigKBVN0YXRlEgsKB1VOS05PV04QABIHCgNSVU4QARIJCgVQQVVTRRACMrYBCg5Db21tYW5kU2VydmljZRJRCghHZXRTdGF0ZRIgLmhlcm8uY29tbWFuZC52MS5HZXRTdGF0ZVJlcXVlc3QaIS5oZXJvLmNvbW1hbmQudjEuR2V0U3RhdGVSZXNwb25zZSIAElEKCFNldFN0YXRlEiAuaGVyby5jb21tYW5kLnYxLlNldFN0YXRlUmVxdWVzdBohLmhlcm8uY29tbWFuZC52MS5TZXRTdGF0ZVJlc3BvbnNlIgBCH1odcHJvdG8vaGVyby9jb21tYW5kL3YxO2NvbW1hbmRiBnByb3RvMw");

/**
 * @generated from message hero.command.v1.GetStateRequest
 */
export type GetStateRequest = Message<"hero.command.v1.GetStateRequest"> & {
};

/**
 * Describes the message hero.command.v1.GetStateRequest.
 * Use `create(GetStateRequestSchema)` to create a new message.
 */
export const GetStateRequestSchema: GenMessage<GetStateRequest> = /*@__PURE__*/
  messageDesc(file_hero_command_v1_command, 0);

/**
 * @generated from message hero.command.v1.GetStateResponse
 */
export type GetStateResponse = Message<"hero.command.v1.GetStateResponse"> & {
  /**
   * @generated from field: hero.command.v1.State state = 1;
   */
  state: State;
};

/**
 * Describes the message hero.command.v1.GetStateResponse.
 * Use `create(GetStateResponseSchema)` to create a new message.
 */
export const GetStateResponseSchema: GenMessage<GetStateResponse> = /*@__PURE__*/
  messageDesc(file_hero_command_v1_command, 1);

/**
 * @generated from message hero.command.v1.SetStateRequest
 */
export type SetStateRequest = Message<"hero.command.v1.SetStateRequest"> & {
  /**
   * @generated from field: hero.command.v1.State state = 1;
   */
  state: State;
};

/**
 * Describes the message hero.command.v1.SetStateRequest.
 * Use `create(SetStateRequestSchema)` to create a new message.
 */
export const SetStateRequestSchema: GenMessage<SetStateRequest> = /*@__PURE__*/
  messageDesc(file_hero_command_v1_command, 2);

/**
 * @generated from message hero.command.v1.SetStateResponse
 */
export type SetStateResponse = Message<"hero.command.v1.SetStateResponse"> & {
};

/**
 * Describes the message hero.command.v1.SetStateResponse.
 * Use `create(SetStateResponseSchema)` to create a new message.
 */
export const SetStateResponseSchema: GenMessage<SetStateResponse> = /*@__PURE__*/
  messageDesc(file_hero_command_v1_command, 3);

/**
 * @generated from enum hero.command.v1.State
 */
export enum State {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: RUN = 1;
   */
  RUN = 1,

  /**
   * @generated from enum value: PAUSE = 2;
   */
  PAUSE = 2,
}

/**
 * Describes the enum hero.command.v1.State.
 */
export const StateSchema: GenEnum<State> = /*@__PURE__*/
  enumDesc(file_hero_command_v1_command, 0);

/**
 * @generated from service hero.command.v1.CommandService
 */
export const CommandService: GenService<{
  /**
   * @generated from rpc hero.command.v1.CommandService.GetState
   */
  getState: {
    methodKind: "unary";
    input: typeof GetStateRequestSchema;
    output: typeof GetStateResponseSchema;
  },
  /**
   * @generated from rpc hero.command.v1.CommandService.SetState
   */
  setState: {
    methodKind: "unary";
    input: typeof SetStateRequestSchema;
    output: typeof SetStateResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_command_v1_command, 0);

