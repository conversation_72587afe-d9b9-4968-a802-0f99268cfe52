// @generated by protoc-gen-es v2.6.1 with parameter "target=ts"
// @generated from file hero/filerepository/v1/filerepository.proto (package hero.filerepository.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_google_protobuf_struct } from "@bufbuild/protobuf/wkt";
import type { JsonObject, Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/filerepository/v1/filerepository.proto.
 */
export const file_hero_filerepository_v1_filerepository: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_struct]);

/**
 * FileMetadata holds all non‑binary data about a file stored in cloud object storage.
 * This message is cloud-provider agnostic and maps to any object storage backend.
 *
 * NOTE: clients never send this directly, it's returned by the service.
 *
 * @generated from message hero.filerepository.v1.FileMetadata
 */
export type FileMetadata = Message<"hero.filerepository.v1.FileMetadata"> & {
  /**
   * Unique UUID (v4) identifying this file record.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Tenant scoping: which organization owns the file.
   *
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * Application‑level owner (could be user_id, service_id, etc).
   *
   * @generated from field: string owner_id = 3;
   */
  ownerId: string;

  /**
   * Original filename as uploaded by client (for download hint).
   *
   * @generated from field: string file_name = 4;
   */
  fileName: string;

  /**
   * Standard MIME type, e.g. "image/png" or "application/pdf".
   *
   * @generated from field: string file_type = 5;
   */
  fileType: string;

  /**
   * Byte count of the object (for progress tracking / billing).
   *
   * @generated from field: int32 file_size = 6;
   */
  fileSize: number;

  /**
   * Timestamps in ISO‑8601 format; set by server.
   *
   * @generated from field: string created_at = 7;
   */
  createdAt: string;

  /**
   * @generated from field: string updated_at = 8;
   */
  updatedAt: string;

  /**
   * Lifecycle tracker through FileStatus enum.
   *
   * @generated from field: hero.filerepository.v1.FileStatus status = 9;
   */
  status: FileStatus;

  /**
   * Which cloud storage provider is hosting this file.
   *
   * @generated from field: hero.filerepository.v1.StorageProvider provider = 10;
   */
  provider: StorageProvider;

  /**
   * The object key/path in the cloud storage bucket/container.
   *
   * @generated from field: string storage_key = 11;
   */
  storageKey: string;

  /**
   * Cloud storage bucket/container name where the file is stored.
   *
   * @generated from field: string bucket_name = 12;
   */
  bucketName: string;

  /**
   * Object version identifier (if versioning enabled on bucket).
   *
   * @generated from field: string object_version = 13;
   */
  objectVersion: string;

  /**
   * Integrity hash from cloud storage (ETag, MD5, CRC32C, etc).
   *
   * @generated from field: string integrity_hash = 14;
   */
  integrityHash: string;

  /**
   * Key/value tags for the cloud storage object (lifecycle, search, billing).
   *
   * @generated from field: map<string, string> storage_tags = 15;
   */
  storageTags: { [key: string]: string };

  /**
   * Which storage tier/class this object uses.
   *
   * @generated from field: hero.filerepository.v1.StorageClass storage_class = 16;
   */
  storageClass: StorageClass;

  /**
   * Encryption key identifier (KMS key ID, customer key reference, etc).
   *
   * @generated from field: string encryption_key_id = 17;
   */
  encryptionKeyId: string;

  /**
   * Application-level checksum (e.g. SHA-256) for extra integrity verification.
   *
   * @generated from field: string checksum = 18;
   */
  checksum: string;

  /**
   * Provider-specific metadata that doesn't fit in standard fields.
   * Examples: S3 metadata headers, GCS custom metadata, Azure blob properties.
   *
   * @generated from field: google.protobuf.Struct provider_metadata = 19;
   */
  providerMetadata?: JsonObject;

  /**
   * Free‑form JSON metadata for any additional application attributes.
   *
   * @generated from field: google.protobuf.Struct extra_metadata = 20;
   */
  extraMetadata?: JsonObject;

  /**
   * -------------------------------------------------------------------------
   * ANALYTICS & ACCESS TRACKING
   * -------------------------------------------------------------------------
   *
   * Usage analytics
   *
   * @generated from field: int32 download_count = 21;
   */
  downloadCount: number;

  /**
   * For lifecycle policies
   *
   * @generated from field: string last_accessed = 22;
   */
  lastAccessed: string;

  /**
   * Public/private flag
   *
   * @generated from field: bool is_public = 23;
   */
  isPublic: boolean;

  /**
   * For images/videos
   *
   * @generated from field: string thumbnail_url = 24;
   */
  thumbnailUrl: string;
};

/**
 * Describes the message hero.filerepository.v1.FileMetadata.
 * Use `create(FileMetadataSchema)` to create a new message.
 */
export const FileMetadataSchema: GenMessage<FileMetadata> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 0);

/**
 * ListFilesRequest allows clients to page, filter, and scope their queries.
 * Only records matching all non‑empty filters are returned.
 *
 * @generated from message hero.filerepository.v1.ListFilesRequest
 */
export type ListFilesRequest = Message<"hero.filerepository.v1.ListFilesRequest"> & {
  /**
   * filter: by file owner
   *
   * @generated from field: string owner_id = 1;
   */
  ownerId: string;

  /**
   * filter: by lifecycle state
   *
   * @generated from field: hero.filerepository.v1.FileStatus status = 2;
   */
  status: FileStatus;

  /**
   * max results to return
   *
   * @generated from field: int32 page_size = 3;
   */
  pageSize: number;

  /**
   * opaque cursor for next page
   *
   * @generated from field: string page_token = 4;
   */
  pageToken: string;
};

/**
 * Describes the message hero.filerepository.v1.ListFilesRequest.
 * Use `create(ListFilesRequestSchema)` to create a new message.
 */
export const ListFilesRequestSchema: GenMessage<ListFilesRequest> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 1);

/**
 * Response includes a page of FileMetadata plus a token for further paging.
 *
 * @generated from message hero.filerepository.v1.ListFilesResponse
 */
export type ListFilesResponse = Message<"hero.filerepository.v1.ListFilesResponse"> & {
  /**
   * @generated from field: repeated hero.filerepository.v1.FileMetadata files = 1;
   */
  files: FileMetadata[];

  /**
   * empty if no more pages
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.filerepository.v1.ListFilesResponse.
 * Use `create(ListFilesResponseSchema)` to create a new message.
 */
export const ListFilesResponseSchema: GenMessage<ListFilesResponse> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 2);

/**
 * Fetch only FileMetadata by ID; no presigned URL issued.
 *
 * @generated from message hero.filerepository.v1.GetFileMetadataRequest
 */
export type GetFileMetadataRequest = Message<"hero.filerepository.v1.GetFileMetadataRequest"> & {
  /**
   * the FileMetadata.id to look up
   *
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.filerepository.v1.GetFileMetadataRequest.
 * Use `create(GetFileMetadataRequestSchema)` to create a new message.
 */
export const GetFileMetadataRequestSchema: GenMessage<GetFileMetadataRequest> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 3);

/**
 * @generated from message hero.filerepository.v1.GetFileMetadataResponse
 */
export type GetFileMetadataResponse = Message<"hero.filerepository.v1.GetFileMetadataResponse"> & {
  /**
   * @generated from field: hero.filerepository.v1.FileMetadata metadata = 1;
   */
  metadata?: FileMetadata;
};

/**
 * Describes the message hero.filerepository.v1.GetFileMetadataResponse.
 * Use `create(GetFileMetadataResponseSchema)` to create a new message.
 */
export const GetFileMetadataResponseSchema: GenMessage<GetFileMetadataResponse> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 4);

/**
 * Date range filter for timestamps
 *
 * @generated from message hero.filerepository.v1.DateRange
 */
export type DateRange = Message<"hero.filerepository.v1.DateRange"> & {
  /**
   * ISO-8601 start date (inclusive)
   *
   * @generated from field: string from = 1;
   */
  from: string;

  /**
   * ISO-8601 end date (inclusive)
   *
   * @generated from field: string to = 2;
   */
  to: string;
};

/**
 * Describes the message hero.filerepository.v1.DateRange.
 * Use `create(DateRangeSchema)` to create a new message.
 */
export const DateRangeSchema: GenMessage<DateRange> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 5);

/**
 * Size range filter for file sizes
 *
 * @generated from message hero.filerepository.v1.SizeRange
 */
export type SizeRange = Message<"hero.filerepository.v1.SizeRange"> & {
  /**
   * minimum size (inclusive)
   *
   * @generated from field: int32 min_bytes = 1;
   */
  minBytes: number;

  /**
   * maximum size (inclusive)
   *
   * @generated from field: int32 max_bytes = 2;
   */
  maxBytes: number;
};

/**
 * Describes the message hero.filerepository.v1.SizeRange.
 * Use `create(SizeRangeSchema)` to create a new message.
 */
export const SizeRangeSchema: GenMessage<SizeRange> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 6);

/**
 * Numeric range filter for download counts
 *
 * @generated from message hero.filerepository.v1.CountRange
 */
export type CountRange = Message<"hero.filerepository.v1.CountRange"> & {
  /**
   * minimum count (inclusive)
   *
   * @generated from field: int32 min_count = 1;
   */
  minCount: number;

  /**
   * maximum count (inclusive)
   *
   * @generated from field: int32 max_count = 2;
   */
  maxCount: number;
};

/**
 * Describes the message hero.filerepository.v1.CountRange.
 * Use `create(CountRangeSchema)` to create a new message.
 */
export const CountRangeSchema: GenMessage<CountRange> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 7);

/**
 * Comprehensive search request with advanced filtering and options
 *
 * @generated from message hero.filerepository.v1.SearchFilesRequest
 */
export type SearchFilesRequest = Message<"hero.filerepository.v1.SearchFilesRequest"> & {
  /**
   * -------------------------------------------------------------------------
   * TEXT SEARCH
   * -------------------------------------------------------------------------
   *
   * full-text search query
   *
   * @generated from field: string query = 1;
   */
  query: string;

  /**
   * fields to search: filename, tags, content, etc.
   *
   * @generated from field: repeated string search_fields = 2;
   */
  searchFields: string[];

  /**
   * -------------------------------------------------------------------------
   * FILTERS
   * -------------------------------------------------------------------------
   *
   * filter by file owner
   *
   * @generated from field: string owner_id = 3;
   */
  ownerId: string;

  /**
   * filter by lifecycle states
   *
   * @generated from field: repeated hero.filerepository.v1.FileStatus statuses = 4;
   */
  statuses: FileStatus[];

  /**
   * filter by MIME types (e.g., "image/*", "application/pdf")
   *
   * @generated from field: repeated string file_types = 5;
   */
  fileTypes: string[];

  /**
   * filter by file extensions (e.g., ".pdf", ".jpg")
   *
   * @generated from field: repeated string extensions = 6;
   */
  extensions: string[];

  /**
   * filter by storage tiers
   *
   * @generated from field: repeated hero.filerepository.v1.StorageClass storage_classes = 7;
   */
  storageClasses: StorageClass[];

  /**
   * Date range filters
   *
   * filter by creation date
   *
   * @generated from field: hero.filerepository.v1.DateRange created_range = 8;
   */
  createdRange?: DateRange;

  /**
   * filter by last modified date
   *
   * @generated from field: hero.filerepository.v1.DateRange updated_range = 9;
   */
  updatedRange?: DateRange;

  /**
   * filter by last access date
   *
   * @generated from field: hero.filerepository.v1.DateRange accessed_range = 10;
   */
  accessedRange?: DateRange;

  /**
   * Size and popularity filters
   *
   * filter by file size
   *
   * @generated from field: hero.filerepository.v1.SizeRange size_range = 11;
   */
  sizeRange?: SizeRange;

  /**
   * filter by download count
   *
   * @generated from field: hero.filerepository.v1.CountRange download_range = 12;
   */
  downloadRange?: CountRange;

  /**
   * Access control filters
   *
   * only public files
   *
   * @generated from field: bool public_only = 13;
   */
  publicOnly: boolean;

  /**
   * only private files
   *
   * @generated from field: bool private_only = 14;
   */
  privateOnly: boolean;

  /**
   * Tag filters (all must match)
   *
   * exact tag key-value matches
   *
   * @generated from field: map<string, string> tag_filters = 15;
   */
  tagFilters: { [key: string]: string };

  /**
   * files that have these tag keys (any value)
   *
   * @generated from field: repeated string tag_keys = 16;
   */
  tagKeys: string[];

  /**
   * Content filters
   *
   * files with/without thumbnails
   *
   * @generated from field: bool has_thumbnail = 17;
   */
  hasThumbnail: boolean;

  /**
   * -------------------------------------------------------------------------
   * SORTING & PAGINATION
   * -------------------------------------------------------------------------
   *
   * field to sort by
   *
   * @generated from field: hero.filerepository.v1.SortField sort_field = 18;
   */
  sortField: SortField;

  /**
   * ascending or descending
   *
   * @generated from field: hero.filerepository.v1.SortOrder sort_order = 19;
   */
  sortOrder: SortOrder;

  /**
   * max results per page
   *
   * @generated from field: int32 page_size = 20;
   */
  pageSize: number;

  /**
   * pagination cursor
   *
   * @generated from field: string page_token = 21;
   */
  pageToken: string;
};

/**
 * Describes the message hero.filerepository.v1.SearchFilesRequest.
 * Use `create(SearchFilesRequestSchema)` to create a new message.
 */
export const SearchFilesRequestSchema: GenMessage<SearchFilesRequest> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 8);

/**
 * Search result highlighting with context fragments
 *
 * @generated from message hero.filerepository.v1.HighlightResult
 */
export type HighlightResult = Message<"hero.filerepository.v1.HighlightResult"> & {
  /**
   * comma-separated list of fields that matched
   *
   * @generated from field: string field = 1;
   */
  field: string;

  /**
   * text fragments showing matches with context
   *
   * @generated from field: repeated string fragments = 2;
   */
  fragments: string[];
};

/**
 * Describes the message hero.filerepository.v1.HighlightResult.
 * Use `create(HighlightResultSchema)` to create a new message.
 */
export const HighlightResultSchema: GenMessage<HighlightResult> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 9);

/**
 * Enhanced search result with highlights and metadata
 *
 * @generated from message hero.filerepository.v1.SearchResult
 */
export type SearchResult = Message<"hero.filerepository.v1.SearchResult"> & {
  /**
   * full file metadata
   *
   * @generated from field: hero.filerepository.v1.FileMetadata metadata = 1;
   */
  metadata?: FileMetadata;

  /**
   * search relevance score
   *
   * @generated from field: float score = 2;
   */
  score: number;

  /**
   * highlighted search terms by field (deprecated, use highlight_results)
   *
   * @generated from field: map<string, string> highlights = 3;
   */
  highlights: { [key: string]: string };

  /**
   * which tags matched the search
   *
   * @generated from field: repeated string matched_tags = 4;
   */
  matchedTags: string[];
};

/**
 * Describes the message hero.filerepository.v1.SearchResult.
 * Use `create(SearchResultSchema)` to create a new message.
 */
export const SearchResultSchema: GenMessage<SearchResult> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 10);

/**
 * Comprehensive search response
 *
 * @generated from message hero.filerepository.v1.SearchFilesResponse
 */
export type SearchFilesResponse = Message<"hero.filerepository.v1.SearchFilesResponse"> & {
  /**
   * search results
   *
   * @generated from field: repeated hero.filerepository.v1.SearchResult results = 1;
   */
  results: SearchResult[];

  /**
   * pagination cursor
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;

  /**
   * total matching files
   *
   * @generated from field: int32 total_count = 3;
   */
  totalCount: number;

  /**
   * file ID to highlight mapping
   *
   * @generated from field: map<string, hero.filerepository.v1.HighlightResult> highlight_results = 4;
   */
  highlightResults: { [key: string]: HighlightResult };
};

/**
 * Describes the message hero.filerepository.v1.SearchFilesResponse.
 * Use `create(SearchFilesResponseSchema)` to create a new message.
 */
export const SearchFilesResponseSchema: GenMessage<SearchFilesResponse> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 11);

/**
 * Structured access log entry for audit and analytics
 *
 * @generated from message hero.filerepository.v1.AccessLogEntry
 */
export type AccessLogEntry = Message<"hero.filerepository.v1.AccessLogEntry"> & {
  /**
   * Unique log entry ID
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * FileMetadata.id that was accessed
   *
   * @generated from field: string file_id = 2;
   */
  fileId: string;

  /**
   * Who performed the action
   *
   * @generated from field: string user_id = 3;
   */
  userId: string;

  /**
   * Action type: "download", "view", "share", "delete", "update"
   *
   * @generated from field: string action = 4;
   */
  action: string;

  /**
   * ISO-8601 timestamp of access
   *
   * @generated from field: string timestamp = 5;
   */
  timestamp: string;

  /**
   * Client IP address for security
   *
   * @generated from field: string ip_address = 6;
   */
  ipAddress: string;

  /**
   * Client user agent for analytics
   *
   * @generated from field: string user_agent = 7;
   */
  userAgent: string;

  /**
   * Where the request originated from
   *
   * @generated from field: string referrer = 8;
   */
  referrer: string;

  /**
   * File size at time of access (for bandwidth tracking)
   *
   * @generated from field: int32 file_size = 9;
   */
  fileSize: number;

  /**
   * Session identifier for correlation
   *
   * @generated from field: string session_id = 10;
   */
  sessionId: string;

  /**
   * Additional context (e.g., download_method, share_recipient)
   *
   * @generated from field: map<string, string> metadata = 11;
   */
  metadata: { [key: string]: string };
};

/**
 * Describes the message hero.filerepository.v1.AccessLogEntry.
 * Use `create(AccessLogEntrySchema)` to create a new message.
 */
export const AccessLogEntrySchema: GenMessage<AccessLogEntry> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 12);

/**
 * Request access logs for a specific file with pagination
 *
 * @generated from message hero.filerepository.v1.GetAccessLogRequest
 */
export type GetAccessLogRequest = Message<"hero.filerepository.v1.GetAccessLogRequest"> & {
  /**
   * FileMetadata.id to get logs for
   *
   * @generated from field: string file_id = 1;
   */
  fileId: string;

  /**
   * Optional: filter by specific user
   *
   * @generated from field: string user_id = 2;
   */
  userId: string;

  /**
   * Optional: filter by action type
   *
   * @generated from field: string action = 3;
   */
  action: string;

  /**
   * Optional: filter by time range
   *
   * @generated from field: hero.filerepository.v1.DateRange time_range = 4;
   */
  timeRange?: DateRange;

  /**
   * Max entries per page
   *
   * @generated from field: int32 page_size = 5;
   */
  pageSize: number;

  /**
   * Pagination cursor
   *
   * @generated from field: string page_token = 6;
   */
  pageToken: string;
};

/**
 * Describes the message hero.filerepository.v1.GetAccessLogRequest.
 * Use `create(GetAccessLogRequestSchema)` to create a new message.
 */
export const GetAccessLogRequestSchema: GenMessage<GetAccessLogRequest> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 13);

/**
 * Response with paginated access log entries
 *
 * @generated from message hero.filerepository.v1.GetAccessLogResponse
 */
export type GetAccessLogResponse = Message<"hero.filerepository.v1.GetAccessLogResponse"> & {
  /**
   * Access log entries
   *
   * @generated from field: repeated hero.filerepository.v1.AccessLogEntry entries = 1;
   */
  entries: AccessLogEntry[];

  /**
   * Pagination cursor
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;

  /**
   * Total matching entries
   *
   * @generated from field: int32 total_count = 3;
   */
  totalCount: number;
};

/**
 * Describes the message hero.filerepository.v1.GetAccessLogResponse.
 * Use `create(GetAccessLogResponseSchema)` to create a new message.
 */
export const GetAccessLogResponseSchema: GenMessage<GetAccessLogResponse> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 14);

/**
 * Record a new access log entry (used internally by service)
 *
 * @generated from message hero.filerepository.v1.RecordAccessRequest
 */
export type RecordAccessRequest = Message<"hero.filerepository.v1.RecordAccessRequest"> & {
  /**
   * FileMetadata.id being accessed
   *
   * @generated from field: string file_id = 1;
   */
  fileId: string;

  /**
   * Who is accessing
   *
   * @generated from field: string user_id = 2;
   */
  userId: string;

  /**
   * What action is being performed
   *
   * @generated from field: string action = 3;
   */
  action: string;

  /**
   * Client IP
   *
   * @generated from field: string ip_address = 4;
   */
  ipAddress: string;

  /**
   * Client user agent
   *
   * @generated from field: string user_agent = 5;
   */
  userAgent: string;

  /**
   * Optional: referrer URL
   *
   * @generated from field: string referrer = 6;
   */
  referrer: string;

  /**
   * Optional: session ID
   *
   * @generated from field: string session_id = 7;
   */
  sessionId: string;

  /**
   * Optional: additional context
   *
   * @generated from field: map<string, string> metadata = 8;
   */
  metadata: { [key: string]: string };
};

/**
 * Describes the message hero.filerepository.v1.RecordAccessRequest.
 * Use `create(RecordAccessRequestSchema)` to create a new message.
 */
export const RecordAccessRequestSchema: GenMessage<RecordAccessRequest> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 15);

/**
 * Response for access log recording
 *
 * @generated from message hero.filerepository.v1.RecordAccessResponse
 */
export type RecordAccessResponse = Message<"hero.filerepository.v1.RecordAccessResponse"> & {
  /**
   * ID of the created log entry
   *
   * @generated from field: string entry_id = 1;
   */
  entryId: string;

  /**
   * Success message
   *
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message hero.filerepository.v1.RecordAccessResponse.
 * Use `create(RecordAccessResponseSchema)` to create a new message.
 */
export const RecordAccessResponseSchema: GenMessage<RecordAccessResponse> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 16);

/**
 * Soft‑delete: marks status = DELETED, object still lives in cloud storage.
 *
 * @generated from message hero.filerepository.v1.DeleteFileRequest
 */
export type DeleteFileRequest = Message<"hero.filerepository.v1.DeleteFileRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.filerepository.v1.DeleteFileRequest.
 * Use `create(DeleteFileRequestSchema)` to create a new message.
 */
export const DeleteFileRequestSchema: GenMessage<DeleteFileRequest> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 17);

/**
 * @generated from message hero.filerepository.v1.DeleteFileResponse
 */
export type DeleteFileResponse = Message<"hero.filerepository.v1.DeleteFileResponse"> & {
  /**
   * e.g. "file marked deleted"
   *
   * @generated from field: string message = 1;
   */
  message: string;
};

/**
 * Describes the message hero.filerepository.v1.DeleteFileResponse.
 * Use `create(DeleteFileResponseSchema)` to create a new message.
 */
export const DeleteFileResponseSchema: GenMessage<DeleteFileResponse> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 18);

/**
 * Undelete: revert a soft‑delete back to ACTIVE.
 *
 * @generated from message hero.filerepository.v1.UndeleteFileRequest
 */
export type UndeleteFileRequest = Message<"hero.filerepository.v1.UndeleteFileRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.filerepository.v1.UndeleteFileRequest.
 * Use `create(UndeleteFileRequestSchema)` to create a new message.
 */
export const UndeleteFileRequestSchema: GenMessage<UndeleteFileRequest> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 19);

/**
 * @generated from message hero.filerepository.v1.UndeleteFileResponse
 */
export type UndeleteFileResponse = Message<"hero.filerepository.v1.UndeleteFileResponse"> & {
  /**
   * @generated from field: hero.filerepository.v1.FileMetadata metadata = 1;
   */
  metadata?: FileMetadata;

  /**
   * e.g. "file restored"
   *
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message hero.filerepository.v1.UndeleteFileResponse.
 * Use `create(UndeleteFileResponseSchema)` to create a new message.
 */
export const UndeleteFileResponseSchema: GenMessage<UndeleteFileResponse> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 20);

/**
 * Purge: permanently remove from metadata store and delete cloud storage object.
 * Irreversible—use with care.
 *
 * @generated from message hero.filerepository.v1.PurgeFileRequest
 */
export type PurgeFileRequest = Message<"hero.filerepository.v1.PurgeFileRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.filerepository.v1.PurgeFileRequest.
 * Use `create(PurgeFileRequestSchema)` to create a new message.
 */
export const PurgeFileRequestSchema: GenMessage<PurgeFileRequest> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 21);

/**
 * @generated from message hero.filerepository.v1.PurgeFileResponse
 */
export type PurgeFileResponse = Message<"hero.filerepository.v1.PurgeFileResponse"> & {
  /**
   * e.g. "file permanently deleted"
   *
   * @generated from field: string message = 1;
   */
  message: string;
};

/**
 * Describes the message hero.filerepository.v1.PurgeFileResponse.
 * Use `create(PurgeFileResponseSchema)` to create a new message.
 */
export const PurgeFileResponseSchema: GenMessage<PurgeFileResponse> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 22);

/**
 * Request a presigned URL to upload bytes directly into cloud storage.
 * Server will create a PENDING FileMetadata record.
 *
 * @generated from message hero.filerepository.v1.PresignedUploadRequest
 */
export type PresignedUploadRequest = Message<"hero.filerepository.v1.PresignedUploadRequest"> & {
  /**
   * for metadata + key generation
   *
   * @generated from field: string file_name = 1;
   */
  fileName: string;

  /**
   * MIME type
   *
   * @generated from field: string file_type = 2;
   */
  fileType: string;

  /**
   * app‑level ownership
   *
   * @generated from field: string owner_id = 3;
   */
  ownerId: string;

  /**
   * tenant scoping: which organization owns the file
   *
   * @generated from field: int32 org_id = 4;
   */
  orgId: number;

  /**
   * any custom JSON fields
   *
   * @generated from field: google.protobuf.Struct extra_metadata = 5;
   */
  extraMetadata?: JsonObject;

  /**
   * seconds until URL expires
   *
   * @generated from field: int32 expires_in = 6;
   */
  expiresIn: number;
};

/**
 * Describes the message hero.filerepository.v1.PresignedUploadRequest.
 * Use `create(PresignedUploadRequestSchema)` to create a new message.
 */
export const PresignedUploadRequestSchema: GenMessage<PresignedUploadRequest> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 23);

/**
 * Returns the presigned URL + the PENDING metadata entry.
 *
 * @generated from message hero.filerepository.v1.PresignedUploadResponse
 */
export type PresignedUploadResponse = Message<"hero.filerepository.v1.PresignedUploadResponse"> & {
  /**
   * @generated from field: string presigned_url = 1;
   */
  presignedUrl: string;

  /**
   * @generated from field: hero.filerepository.v1.FileMetadata metadata = 2;
   */
  metadata?: FileMetadata;

  /**
   * human‑readable status
   *
   * @generated from field: string message = 3;
   */
  message: string;
};

/**
 * Describes the message hero.filerepository.v1.PresignedUploadResponse.
 * Use `create(PresignedUploadResponseSchema)` to create a new message.
 */
export const PresignedUploadResponseSchema: GenMessage<PresignedUploadResponse> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 24);

/**
 * Request a presigned URL to download an existing object.
 *
 * @generated from message hero.filerepository.v1.PresignedDownloadRequest
 */
export type PresignedDownloadRequest = Message<"hero.filerepository.v1.PresignedDownloadRequest"> & {
  /**
   * FileMetadata.id
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * seconds until URL expires
   *
   * @generated from field: int32 expires_in = 2;
   */
  expiresIn: number;
};

/**
 * Describes the message hero.filerepository.v1.PresignedDownloadRequest.
 * Use `create(PresignedDownloadRequestSchema)` to create a new message.
 */
export const PresignedDownloadRequestSchema: GenMessage<PresignedDownloadRequest> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 25);

/**
 * Returns the download URL + current metadata.
 *
 * @generated from message hero.filerepository.v1.PresignedDownloadResponse
 */
export type PresignedDownloadResponse = Message<"hero.filerepository.v1.PresignedDownloadResponse"> & {
  /**
   * @generated from field: string presigned_url = 1;
   */
  presignedUrl: string;

  /**
   * @generated from field: hero.filerepository.v1.FileMetadata metadata = 2;
   */
  metadata?: FileMetadata;

  /**
   * @generated from field: string message = 3;
   */
  message: string;
};

/**
 * Describes the message hero.filerepository.v1.PresignedDownloadResponse.
 * Use `create(PresignedDownloadResponseSchema)` to create a new message.
 */
export const PresignedDownloadResponseSchema: GenMessage<PresignedDownloadResponse> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 26);

/**
 * Update only non‑binary metadata fields (file_name, extra_metadata, etc).
 *
 * @generated from message hero.filerepository.v1.UpdateFileMetadataRequest
 */
export type UpdateFileMetadataRequest = Message<"hero.filerepository.v1.UpdateFileMetadataRequest"> & {
  /**
   * FileMetadata.id
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * new filename (optional)
   *
   * @generated from field: string file_name = 2;
   */
  fileName: string;

  /**
   * new owner (optional)
   *
   * @generated from field: string owner_id = 3;
   */
  ownerId: string;

  /**
   * merge/overwrite existing
   *
   * @generated from field: google.protobuf.Struct extra_metadata = 4;
   */
  extraMetadata?: JsonObject;
};

/**
 * Describes the message hero.filerepository.v1.UpdateFileMetadataRequest.
 * Use `create(UpdateFileMetadataRequestSchema)` to create a new message.
 */
export const UpdateFileMetadataRequestSchema: GenMessage<UpdateFileMetadataRequest> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 27);

/**
 * Returns the new FileMetadata record after update.
 *
 * @generated from message hero.filerepository.v1.UpdateFileMetadataResponse
 */
export type UpdateFileMetadataResponse = Message<"hero.filerepository.v1.UpdateFileMetadataResponse"> & {
  /**
   * @generated from field: hero.filerepository.v1.FileMetadata metadata = 1;
   */
  metadata?: FileMetadata;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message hero.filerepository.v1.UpdateFileMetadataResponse.
 * Use `create(UpdateFileMetadataResponseSchema)` to create a new message.
 */
export const UpdateFileMetadataResponseSchema: GenMessage<UpdateFileMetadataResponse> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 28);

/**
 * After PUT to the presigned upload URL, client calls this to confirm.
 * Service will verify size/etag, then mark status = ACTIVE.
 *
 * @generated from message hero.filerepository.v1.ConfirmUploadRequest
 */
export type ConfirmUploadRequest = Message<"hero.filerepository.v1.ConfirmUploadRequest"> & {
  /**
   * the metadata.id returned earlier
   *
   * @generated from field: string pending_id = 1;
   */
  pendingId: string;

  /**
   * verify actual byte count
   *
   * @generated from field: int32 file_size = 2;
   */
  fileSize: number;
};

/**
 * Describes the message hero.filerepository.v1.ConfirmUploadRequest.
 * Use `create(ConfirmUploadRequestSchema)` to create a new message.
 */
export const ConfirmUploadRequestSchema: GenMessage<ConfirmUploadRequest> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 29);

/**
 * @generated from message hero.filerepository.v1.ConfirmUploadResponse
 */
export type ConfirmUploadResponse = Message<"hero.filerepository.v1.ConfirmUploadResponse"> & {
  /**
   * @generated from field: hero.filerepository.v1.FileMetadata metadata = 1;
   */
  metadata?: FileMetadata;

  /**
   * e.g. "upload confirmed"
   *
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message hero.filerepository.v1.ConfirmUploadResponse.
 * Use `create(ConfirmUploadResponseSchema)` to create a new message.
 */
export const ConfirmUploadResponseSchema: GenMessage<ConfirmUploadResponse> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 30);

/**
 * Cleanup stray PENDING records older than a cutoff.
 *
 * @generated from message hero.filerepository.v1.CleanupPendingUploadsRequest
 */
export type CleanupPendingUploadsRequest = Message<"hero.filerepository.v1.CleanupPendingUploadsRequest"> & {
  /**
   * ISO‑8601 timestamp cutoff
   *
   * @generated from field: string older_than = 1;
   */
  olderThan: string;
};

/**
 * Describes the message hero.filerepository.v1.CleanupPendingUploadsRequest.
 * Use `create(CleanupPendingUploadsRequestSchema)` to create a new message.
 */
export const CleanupPendingUploadsRequestSchema: GenMessage<CleanupPendingUploadsRequest> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 31);

/**
 * @generated from message hero.filerepository.v1.CleanupPendingUploadsResponse
 */
export type CleanupPendingUploadsResponse = Message<"hero.filerepository.v1.CleanupPendingUploadsResponse"> & {
  /**
   * how many PENDING entries purged
   *
   * @generated from field: int32 removed_count = 1;
   */
  removedCount: number;

  /**
   * human‑readable status
   *
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message hero.filerepository.v1.CleanupPendingUploadsResponse.
 * Use `create(CleanupPendingUploadsResponseSchema)` to create a new message.
 */
export const CleanupPendingUploadsResponseSchema: GenMessage<CleanupPendingUploadsResponse> = /*@__PURE__*/
  messageDesc(file_hero_filerepository_v1_filerepository, 32);

/**
 * FileStatus tracks the lifecycle state of a file record.
 * Files progress through: PENDING → ACTIVE → (optionally ARCHIVED) → DELETED
 *
 * @generated from enum hero.filerepository.v1.FileStatus
 */
export enum FileStatus {
  /**
   * default / unset
   *
   * @generated from enum value: FILE_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * presigned URL issued but upload not confirmed
   *
   * @generated from enum value: FILE_STATUS_PENDING = 1;
   */
  PENDING = 1,

  /**
   * file successfully uploaded and available
   *
   * @generated from enum value: FILE_STATUS_ACTIVE = 2;
   */
  ACTIVE = 2,

  /**
   * file retired but retained for audit/compliance
   *
   * @generated from enum value: FILE_STATUS_ARCHIVED = 3;
   */
  ARCHIVED = 3,

  /**
   * soft‑deleted file (potentially recoverable)
   *
   * @generated from enum value: FILE_STATUS_DELETED = 4;
   */
  DELETED = 4,
}

/**
 * Describes the enum hero.filerepository.v1.FileStatus.
 */
export const FileStatusSchema: GenEnum<FileStatus> = /*@__PURE__*/
  enumDesc(file_hero_filerepository_v1_filerepository, 0);

/**
 * StorageClass defines the storage tier for cost and access optimization.
 * Maps to provider-specific storage classes (S3, GCS, Azure Blob tiers).
 *
 * @generated from enum hero.filerepository.v1.StorageClass
 */
export enum StorageClass {
  /**
   * default / unknown
   *
   * @generated from enum value: STORAGE_CLASS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * frequently accessed, high availability
   *
   * @generated from enum value: STORAGE_CLASS_HOT = 1;
   */
  HOT = 1,

  /**
   * infrequently accessed, lower cost
   *
   * @generated from enum value: STORAGE_CLASS_WARM = 2;
   */
  WARM = 2,

  /**
   * archive storage, minutes to restore
   *
   * @generated from enum value: STORAGE_CLASS_COLD = 3;
   */
  COLD = 3,

  /**
   * deep archive, hours to restore
   *
   * @generated from enum value: STORAGE_CLASS_FROZEN = 4;
   */
  FROZEN = 4,
}

/**
 * Describes the enum hero.filerepository.v1.StorageClass.
 */
export const StorageClassSchema: GenEnum<StorageClass> = /*@__PURE__*/
  enumDesc(file_hero_filerepository_v1_filerepository, 1);

/**
 * StorageProvider identifies which cloud storage backend is being used.
 *
 * @generated from enum hero.filerepository.v1.StorageProvider
 */
export enum StorageProvider {
  /**
   * default / unknown
   *
   * @generated from enum value: STORAGE_PROVIDER_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Amazon S3
   *
   * @generated from enum value: STORAGE_PROVIDER_AWS_S3 = 1;
   */
  AWS_S3 = 1,

  /**
   * Google Cloud Storage
   *
   * @generated from enum value: STORAGE_PROVIDER_GCP_GCS = 2;
   */
  GCP_GCS = 2,

  /**
   * Azure Blob Storage
   *
   * @generated from enum value: STORAGE_PROVIDER_AZURE_BLOB = 3;
   */
  AZURE_BLOB = 3,

  /**
   * Self-hosted MinIO (S3-compatible)
   *
   * @generated from enum value: STORAGE_PROVIDER_MINIO = 4;
   */
  MINIO = 4,
}

/**
 * Describes the enum hero.filerepository.v1.StorageProvider.
 */
export const StorageProviderSchema: GenEnum<StorageProvider> = /*@__PURE__*/
  enumDesc(file_hero_filerepository_v1_filerepository, 2);

/**
 * Sort field options for search results
 *
 * @generated from enum hero.filerepository.v1.SortField
 */
export enum SortField {
  /**
   * @generated from enum value: SORT_FIELD_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * sort by creation date
   *
   * @generated from enum value: SORT_FIELD_CREATED_AT = 1;
   */
  CREATED_AT = 1,

  /**
   * sort by last modified
   *
   * @generated from enum value: SORT_FIELD_UPDATED_AT = 2;
   */
  UPDATED_AT = 2,

  /**
   * alphabetical by filename
   *
   * @generated from enum value: SORT_FIELD_FILE_NAME = 3;
   */
  FILE_NAME = 3,

  /**
   * sort by file size
   *
   * @generated from enum value: SORT_FIELD_FILE_SIZE = 4;
   */
  FILE_SIZE = 4,

  /**
   * sort by popularity
   *
   * @generated from enum value: SORT_FIELD_DOWNLOAD_COUNT = 5;
   */
  DOWNLOAD_COUNT = 5,

  /**
   * sort by recent access
   *
   * @generated from enum value: SORT_FIELD_LAST_ACCESSED = 6;
   */
  LAST_ACCESSED = 6,

  /**
   * search relevance score
   *
   * @generated from enum value: SORT_FIELD_RELEVANCE = 7;
   */
  RELEVANCE = 7,
}

/**
 * Describes the enum hero.filerepository.v1.SortField.
 */
export const SortFieldSchema: GenEnum<SortField> = /*@__PURE__*/
  enumDesc(file_hero_filerepository_v1_filerepository, 3);

/**
 * Sort order direction
 *
 * @generated from enum hero.filerepository.v1.SortOrder
 */
export enum SortOrder {
  /**
   * @generated from enum value: SORT_ORDER_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * ascending
   *
   * @generated from enum value: SORT_ORDER_ASC = 1;
   */
  ASC = 1,

  /**
   * descending
   *
   * @generated from enum value: SORT_ORDER_DESC = 2;
   */
  DESC = 2,
}

/**
 * Describes the enum hero.filerepository.v1.SortOrder.
 */
export const SortOrderSchema: GenEnum<SortOrder> = /*@__PURE__*/
  enumDesc(file_hero_filerepository_v1_filerepository, 4);

/**
 * @generated from service hero.filerepository.v1.FileRepositoryService
 */
export const FileRepositoryService: GenService<{
  /**
   * --- URL endpoints
   *
   * @generated from rpc hero.filerepository.v1.FileRepositoryService.GetPresignedUploadUrl
   */
  getPresignedUploadUrl: {
    methodKind: "unary";
    input: typeof PresignedUploadRequestSchema;
    output: typeof PresignedUploadResponseSchema;
  },
  /**
   * @generated from rpc hero.filerepository.v1.FileRepositoryService.GetPresignedDownloadUrl
   */
  getPresignedDownloadUrl: {
    methodKind: "unary";
    input: typeof PresignedDownloadRequestSchema;
    output: typeof PresignedDownloadResponseSchema;
  },
  /**
   * --- Metadata-only and search
   *
   * @generated from rpc hero.filerepository.v1.FileRepositoryService.GetFileMetadata
   */
  getFileMetadata: {
    methodKind: "unary";
    input: typeof GetFileMetadataRequestSchema;
    output: typeof GetFileMetadataResponseSchema;
  },
  /**
   * @generated from rpc hero.filerepository.v1.FileRepositoryService.SearchFiles
   */
  searchFiles: {
    methodKind: "unary";
    input: typeof SearchFilesRequestSchema;
    output: typeof SearchFilesResponseSchema;
  },
  /**
   * --- Paging & listing
   *
   * @generated from rpc hero.filerepository.v1.FileRepositoryService.ListFiles
   */
  listFiles: {
    methodKind: "unary";
    input: typeof ListFilesRequestSchema;
    output: typeof ListFilesResponseSchema;
  },
  /**
   * --- Metadata updates
   *
   * @generated from rpc hero.filerepository.v1.FileRepositoryService.UpdateFileMetadata
   */
  updateFileMetadata: {
    methodKind: "unary";
    input: typeof UpdateFileMetadataRequestSchema;
    output: typeof UpdateFileMetadataResponseSchema;
  },
  /**
   * --- Upload confirmation & cleanup
   *
   * @generated from rpc hero.filerepository.v1.FileRepositoryService.ConfirmUpload
   */
  confirmUpload: {
    methodKind: "unary";
    input: typeof ConfirmUploadRequestSchema;
    output: typeof ConfirmUploadResponseSchema;
  },
  /**
   * @generated from rpc hero.filerepository.v1.FileRepositoryService.CleanupPendingUploads
   */
  cleanupPendingUploads: {
    methodKind: "unary";
    input: typeof CleanupPendingUploadsRequestSchema;
    output: typeof CleanupPendingUploadsResponseSchema;
  },
  /**
   * --- Soft delete / undelete / hard purge
   *
   * @generated from rpc hero.filerepository.v1.FileRepositoryService.DeleteFile
   */
  deleteFile: {
    methodKind: "unary";
    input: typeof DeleteFileRequestSchema;
    output: typeof DeleteFileResponseSchema;
  },
  /**
   * @generated from rpc hero.filerepository.v1.FileRepositoryService.UndeleteFile
   */
  undeleteFile: {
    methodKind: "unary";
    input: typeof UndeleteFileRequestSchema;
    output: typeof UndeleteFileResponseSchema;
  },
  /**
   * @generated from rpc hero.filerepository.v1.FileRepositoryService.PurgeFile
   */
  purgeFile: {
    methodKind: "unary";
    input: typeof PurgeFileRequestSchema;
    output: typeof PurgeFileResponseSchema;
  },
  /**
   * --- Access logging & audit trail
   *
   * @generated from rpc hero.filerepository.v1.FileRepositoryService.GetAccessLog
   */
  getAccessLog: {
    methodKind: "unary";
    input: typeof GetAccessLogRequestSchema;
    output: typeof GetAccessLogResponseSchema;
  },
  /**
   * @generated from rpc hero.filerepository.v1.FileRepositoryService.RecordAccess
   */
  recordAccess: {
    methodKind: "unary";
    input: typeof RecordAccessRequestSchema;
    output: typeof RecordAccessResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_filerepository_v1_filerepository, 0);

