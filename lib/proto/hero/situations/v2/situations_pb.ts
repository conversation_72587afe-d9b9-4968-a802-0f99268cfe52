// @generated by protoc-gen-es v2.6.1 with parameter "target=ts"
// @generated from file hero/situations/v2/situations.proto (package hero.situations.v2, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { EmptySchema } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import { file_hero_permissions_v1_permissions } from "../../permissions/v1/permissions_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/situations/v2/situations.proto.
 */
export const file_hero_situations_v2_situations: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_empty, file_hero_permissions_v1_permissions]);

/**
 * Message for media attachment.
 *
 * @generated from message hero.situations.v2.MediaAttachment
 */
export type MediaAttachment = Message<"hero.situations.v2.MediaAttachment"> & {
  /**
   * @generated from field: string attachment_id = 1;
   */
  attachmentId: string;

  /**
   * @generated from field: string url = 2;
   */
  url: string;

  /**
   * @generated from field: string content_type = 3;
   */
  contentType: string;
};

/**
 * Describes the message hero.situations.v2.MediaAttachment.
 * Use `create(MediaAttachmentSchema)` to create a new message.
 */
export const MediaAttachmentSchema: GenMessage<MediaAttachment> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 0);

/**
 * Message to capture an update with its associated timestamp, source, and a brief message.
 *
 * @generated from message hero.situations.v2.UpdateEntry
 */
export type UpdateEntry = Message<"hero.situations.v2.UpdateEntry"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;

  /**
   * ISO8601 timestamp
   *
   * @generated from field: string timestamp = 2;
   */
  timestamp: string;

  /**
   * These are all optional 
   *
   * @generated from field: hero.situations.v2.UpdateSource update_source = 3;
   */
  updateSource: UpdateSource;

  /**
   * @generated from field: string display_name = 4;
   */
  displayName: string;

  /**
   * @generated from field: string event_type = 5;
   */
  eventType: string;

  /**
   * @generated from field: string updater_id = 6;
   */
  updaterId: string;
};

/**
 * Describes the message hero.situations.v2.UpdateEntry.
 * Use `create(UpdateEntrySchema)` to create a new message.
 */
export const UpdateEntrySchema: GenMessage<UpdateEntry> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 1);

/**
 * New message dedicated to capturing status update events.
 *
 * @generated from message hero.situations.v2.StatusUpdateEntry
 */
export type StatusUpdateEntry = Message<"hero.situations.v2.StatusUpdateEntry"> & {
  /**
   * ISO8601 timestamp
   *
   * @generated from field: string timestamp = 1;
   */
  timestamp: string;

  /**
   * The new status of the situation.
   *
   * @generated from field: hero.situations.v2.SituationStatus new_status = 2;
   */
  newStatus: SituationStatus;

  /**
   * (Optional) The previous status before this update.
   *
   * @generated from field: hero.situations.v2.SituationStatus previous_status = 3;
   */
  previousStatus: SituationStatus;

  /**
   * (Optional) A note explaining the status change.
   *
   * @generated from field: string note = 4;
   */
  note: string;

  /**
   * (Optional) ID of the updater
   *
   * @generated from field: string updater_id = 5;
   */
  updaterId: string;

  /**
   * (Optional) Source of the update
   *
   * @generated from field: hero.situations.v2.UpdateSource update_source = 6;
   */
  updateSource: UpdateSource;
};

/**
 * Describes the message hero.situations.v2.StatusUpdateEntry.
 * Use `create(StatusUpdateEntrySchema)` to create a new message.
 */
export const StatusUpdateEntrySchema: GenMessage<StatusUpdateEntry> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 2);

/**
 * Main message definition for Situation.
 *
 * @generated from message hero.situations.v2.Situation
 */
export type Situation = Message<"hero.situations.v2.Situation"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 priority = 2;
   */
  priority: number;

  /**
   * @generated from field: string title = 3;
   */
  title: string;

  /**
   * @generated from field: hero.situations.v2.SituationType type = 4;
   */
  type: SituationType;

  /**
   * For situation it will be fixed value "SITUATION"
   *
   * @generated from field: string resource_type = 5;
   */
  resourceType: string;

  /**
   * Additional details.
   *
   * @generated from field: string description = 6;
   */
  description: string;

  /**
   * @generated from field: hero.situations.v2.SituationStatus status = 7;
   */
  status: SituationStatus;

  /**
   * Will try to collect if available, not guaranteed.
   *
   * @generated from field: string reporter_id = 8;
   */
  reporterId: string;

  /**
   * A list of updates.
   *
   * @generated from field: repeated hero.situations.v2.UpdateEntry updates = 9;
   */
  updates: UpdateEntry[];

  /**
   * Optional categorization.
   *
   * @generated from field: repeated string tags = 10;
   */
  tags: string[];

  /**
   * Arrays to capture related situation IDs and media attachments.
   *
   * @generated from field: repeated string related_situations_ids = 11;
   */
  relatedSituationsIds: string[];

  /**
   * @generated from field: repeated hero.situations.v2.MediaAttachment media_attachments = 12;
   */
  mediaAttachments: MediaAttachment[];

  /**
   * Timestamps for various events.
   *
   * ISO8601 timestamp
   *
   * @generated from field: string create_time = 13;
   */
  createTime: string;

  /**
   * ISO8601 timestamp
   *
   * @generated from field: string update_time = 14;
   */
  updateTime: string;

  /**
   * ISO8601 timestamp
   *
   * @generated from field: string due_time = 15;
   */
  dueTime: string;

  /**
   * ISO8601 timestamp
   *
   * @generated from field: string resolved_time = 16;
   */
  resolvedTime: string;

  /**
   * ISO8601 timestamp
   *
   * @generated from field: string incident_time = 17;
   */
  incidentTime: string;

  /**
   * The source that triggered this situation.
   *
   * @generated from field: hero.situations.v2.TriggerSource trigger_source = 18;
   */
  triggerSource: TriggerSource;

  /**
   * these are considered essential info in any situation 
   *
   * @generated from field: string reporter_name = 19;
   */
  reporterName: string;

  /**
   * @generated from field: string contact_no = 20;
   */
  contactNo: string;

  /**
   * @generated from field: string contact_email = 21;
   */
  contactEmail: string;

  /**
   * this is mostly for first time a address gets collected for a situation
   * so that downstream order can kick off 
   * but it can change over the time 
   *
   * @generated from field: string address = 22;
   */
  address: string;

  /**
   * @generated from field: double latitude = 23;
   */
  latitude: number;

  /**
   * @generated from field: double longitude = 24;
   */
  longitude: number;

  /**
   * ISO8601 timestamp
   *
   * @generated from field: string address_update_time = 25;
   */
  addressUpdateTime: string;

  /**
   * @generated from field: bool automation_enabled = 26;
   */
  automationEnabled: boolean;

  /**
   * Field for additional JSON-based information.
   *
   * @generated from field: string additional_info_json = 27;
   */
  additionalInfoJson: string;

  /**
   * @generated from field: repeated hero.situations.v2.StatusUpdateEntry status_updates = 28;
   */
  statusUpdates: StatusUpdateEntry[];
};

/**
 * Describes the message hero.situations.v2.Situation.
 * Use `create(SituationSchema)` to create a new message.
 */
export const SituationSchema: GenMessage<Situation> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 3);

/**
 * Request and Response messages for listing by asset.
 *
 * @generated from message hero.situations.v2.ListSituationsForAssetRequest
 */
export type ListSituationsForAssetRequest = Message<"hero.situations.v2.ListSituationsForAssetRequest"> & {
  /**
   * Asset ID for which to retrieve situations.
   *
   * @generated from field: string asset_id = 1;
   */
  assetId: string;

  /**
   * Optional filter: status of the situation.
   *
   * @generated from field: hero.situations.v2.SituationStatus status = 2;
   */
  status: SituationStatus;

  /**
   * Optional filter: type of the situation.
   *
   * @generated from field: hero.situations.v2.SituationType type = 3;
   */
  type: SituationType;

  /**
   * Optional filter: trigger source of the situation.
   *
   * @generated from field: hero.situations.v2.TriggerSource trigger_source = 4;
   */
  triggerSource: TriggerSource;

  /**
   * Optional filter: priority level of the situation.
   *
   * @generated from field: int32 priority = 5;
   */
  priority: number;

  /**
   * Optional filter: reporter identifier.
   *
   * @generated from field: string reporter_id = 6;
   */
  reporterId: string;

  /**
   * Optional filter: situations created after this timestamp.
   *
   * @generated from field: string created_after = 7;
   */
  createdAfter: string;

  /**
   * Optional filter: situations created before this timestamp.
   *
   * @generated from field: string created_before = 8;
   */
  createdBefore: string;

  /**
   * Optional geographic bounding box: minimum latitude.
   *
   * @generated from field: double min_latitude = 9;
   */
  minLatitude: number;

  /**
   * Optional geographic bounding box: maximum latitude.
   *
   * @generated from field: double max_latitude = 10;
   */
  maxLatitude: number;

  /**
   * Optional geographic bounding box: minimum longitude.
   *
   * @generated from field: double min_longitude = 11;
   */
  minLongitude: number;

  /**
   * Optional geographic bounding box: maximum longitude.
   *
   * @generated from field: double max_longitude = 12;
   */
  maxLongitude: number;

  /**
   * Optional filter: tag values to include.
   *
   * @generated from field: repeated string tags = 13;
   */
  tags: string[];

  /**
   * Pagination: maximum number of items to return.
   *
   * @generated from field: int32 page_size = 14;
   */
  pageSize: number;

  /**
   * Pagination token for fetching the next page.
   *
   * @generated from field: string page_token = 15;
   */
  pageToken: string;
};

/**
 * Describes the message hero.situations.v2.ListSituationsForAssetRequest.
 * Use `create(ListSituationsForAssetRequestSchema)` to create a new message.
 */
export const ListSituationsForAssetRequestSchema: GenMessage<ListSituationsForAssetRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 4);

/**
 * @generated from message hero.situations.v2.ListSituationsForAssetResponse
 */
export type ListSituationsForAssetResponse = Message<"hero.situations.v2.ListSituationsForAssetResponse"> & {
  /**
   * @generated from field: repeated hero.situations.v2.Situation situations = 1;
   */
  situations: Situation[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;

  /**
   * @generated from field: int32 total_results = 3;
   */
  totalResults: number;
};

/**
 * Describes the message hero.situations.v2.ListSituationsForAssetResponse.
 * Use `create(ListSituationsForAssetResponseSchema)` to create a new message.
 */
export const ListSituationsForAssetResponseSchema: GenMessage<ListSituationsForAssetResponse> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 5);

/**
 * Request and Response messages for API operations.
 *
 * @generated from message hero.situations.v2.CreateSituationRequest
 */
export type CreateSituationRequest = Message<"hero.situations.v2.CreateSituationRequest"> & {
  /**
   * @generated from field: hero.situations.v2.Situation situation = 1;
   */
  situation?: Situation;
};

/**
 * Describes the message hero.situations.v2.CreateSituationRequest.
 * Use `create(CreateSituationRequestSchema)` to create a new message.
 */
export const CreateSituationRequestSchema: GenMessage<CreateSituationRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 6);

/**
 * @generated from message hero.situations.v2.CreateSituationResponse
 */
export type CreateSituationResponse = Message<"hero.situations.v2.CreateSituationResponse"> & {
  /**
   * @generated from field: hero.situations.v2.Situation situation = 1;
   */
  situation?: Situation;
};

/**
 * Describes the message hero.situations.v2.CreateSituationResponse.
 * Use `create(CreateSituationResponseSchema)` to create a new message.
 */
export const CreateSituationResponseSchema: GenMessage<CreateSituationResponse> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 7);

/**
 * @generated from message hero.situations.v2.GetSituationRequest
 */
export type GetSituationRequest = Message<"hero.situations.v2.GetSituationRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.situations.v2.GetSituationRequest.
 * Use `create(GetSituationRequestSchema)` to create a new message.
 */
export const GetSituationRequestSchema: GenMessage<GetSituationRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 8);

/**
 * @generated from message hero.situations.v2.UpdateSituationRequest
 */
export type UpdateSituationRequest = Message<"hero.situations.v2.UpdateSituationRequest"> & {
  /**
   * The updated Situation object; its id field determines which situation to update.
   *
   * @generated from field: hero.situations.v2.Situation situation = 1;
   */
  situation?: Situation;
};

/**
 * Describes the message hero.situations.v2.UpdateSituationRequest.
 * Use `create(UpdateSituationRequestSchema)` to create a new message.
 */
export const UpdateSituationRequestSchema: GenMessage<UpdateSituationRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 9);

/**
 * @generated from message hero.situations.v2.ListSituationsRequest
 */
export type ListSituationsRequest = Message<"hero.situations.v2.ListSituationsRequest"> & {
  /**
   * Maximum number of situations to return in the response.
   *
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * A token identifying a specific page of results to retrieve.
   *
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  /**
   * Optional filter: Returns only situations matching the specified status.
   *
   * @generated from field: hero.situations.v2.SituationStatus status = 3;
   */
  status: SituationStatus;

  /**
   * Optional filter: Returns only situations with the specified trigger source.
   *
   * @generated from field: hero.situations.v2.TriggerSource trigger_source = 4;
   */
  triggerSource: TriggerSource;

  /**
   * Optional filter: Returns only situations of the specified type.
   *
   * @generated from field: hero.situations.v2.SituationType type = 5;
   */
  type: SituationType;

  /**
   * Optional: Specifies the field by which the returned situations should be ordered.
   *
   * @generated from field: string order_by = 6;
   */
  orderBy: string;
};

/**
 * Describes the message hero.situations.v2.ListSituationsRequest.
 * Use `create(ListSituationsRequestSchema)` to create a new message.
 */
export const ListSituationsRequestSchema: GenMessage<ListSituationsRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 10);

/**
 * @generated from message hero.situations.v2.ListSituationsResponse
 */
export type ListSituationsResponse = Message<"hero.situations.v2.ListSituationsResponse"> & {
  /**
   * @generated from field: repeated hero.situations.v2.Situation situations = 1;
   */
  situations: Situation[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;

  /**
   * @generated from field: int32 total_results = 3;
   */
  totalResults: number;
};

/**
 * Describes the message hero.situations.v2.ListSituationsResponse.
 * Use `create(ListSituationsResponseSchema)` to create a new message.
 */
export const ListSituationsResponseSchema: GenMessage<ListSituationsResponse> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 11);

/**
 * @generated from message hero.situations.v2.DeleteSituationRequest
 */
export type DeleteSituationRequest = Message<"hero.situations.v2.DeleteSituationRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.situations.v2.DeleteSituationRequest.
 * Use `create(DeleteSituationRequestSchema)` to create a new message.
 */
export const DeleteSituationRequestSchema: GenMessage<DeleteSituationRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 12);

/**
 * @generated from message hero.situations.v2.AddSituationUpdateRequest
 */
export type AddSituationUpdateRequest = Message<"hero.situations.v2.AddSituationUpdateRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: hero.situations.v2.UpdateEntry update = 2;
   */
  update?: UpdateEntry;
};

/**
 * Describes the message hero.situations.v2.AddSituationUpdateRequest.
 * Use `create(AddSituationUpdateRequestSchema)` to create a new message.
 */
export const AddSituationUpdateRequestSchema: GenMessage<AddSituationUpdateRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 13);

/**
 * @generated from message hero.situations.v2.AddSituationTagRequest
 */
export type AddSituationTagRequest = Message<"hero.situations.v2.AddSituationTagRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string tag = 2;
   */
  tag: string;
};

/**
 * Describes the message hero.situations.v2.AddSituationTagRequest.
 * Use `create(AddSituationTagRequestSchema)` to create a new message.
 */
export const AddSituationTagRequestSchema: GenMessage<AddSituationTagRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 14);

/**
 * @generated from message hero.situations.v2.AddRelatedSituationRequest
 */
export type AddRelatedSituationRequest = Message<"hero.situations.v2.AddRelatedSituationRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Identifier of a related situation.
   *
   * @generated from field: string related_situation_id = 2;
   */
  relatedSituationId: string;
};

/**
 * Describes the message hero.situations.v2.AddRelatedSituationRequest.
 * Use `create(AddRelatedSituationRequestSchema)` to create a new message.
 */
export const AddRelatedSituationRequestSchema: GenMessage<AddRelatedSituationRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 15);

/**
 * @generated from message hero.situations.v2.AddMediaAttachmentForSituationRequest
 */
export type AddMediaAttachmentForSituationRequest = Message<"hero.situations.v2.AddMediaAttachmentForSituationRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Media attachment for the situation.
   *
   * @generated from field: hero.situations.v2.MediaAttachment media_attachment = 2;
   */
  mediaAttachment?: MediaAttachment;
};

/**
 * Describes the message hero.situations.v2.AddMediaAttachmentForSituationRequest.
 * Use `create(AddMediaAttachmentForSituationRequestSchema)` to create a new message.
 */
export const AddMediaAttachmentForSituationRequestSchema: GenMessage<AddMediaAttachmentForSituationRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 16);

/**
 * Remove request messages for repeated fields.
 *
 * @generated from message hero.situations.v2.RemoveSituationUpdateRequest
 */
export type RemoveSituationUpdateRequest = Message<"hero.situations.v2.RemoveSituationUpdateRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * The update to remove. The server will remove a matching update entry.
   *
   * @generated from field: hero.situations.v2.UpdateEntry update = 2;
   */
  update?: UpdateEntry;
};

/**
 * Describes the message hero.situations.v2.RemoveSituationUpdateRequest.
 * Use `create(RemoveSituationUpdateRequestSchema)` to create a new message.
 */
export const RemoveSituationUpdateRequestSchema: GenMessage<RemoveSituationUpdateRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 17);

/**
 * @generated from message hero.situations.v2.RemoveSituationTagRequest
 */
export type RemoveSituationTagRequest = Message<"hero.situations.v2.RemoveSituationTagRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string tag = 2;
   */
  tag: string;
};

/**
 * Describes the message hero.situations.v2.RemoveSituationTagRequest.
 * Use `create(RemoveSituationTagRequestSchema)` to create a new message.
 */
export const RemoveSituationTagRequestSchema: GenMessage<RemoveSituationTagRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 18);

/**
 * @generated from message hero.situations.v2.RemoveRelatedSituationRequest
 */
export type RemoveRelatedSituationRequest = Message<"hero.situations.v2.RemoveRelatedSituationRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string related_situation_id = 2;
   */
  relatedSituationId: string;
};

/**
 * Describes the message hero.situations.v2.RemoveRelatedSituationRequest.
 * Use `create(RemoveRelatedSituationRequestSchema)` to create a new message.
 */
export const RemoveRelatedSituationRequestSchema: GenMessage<RemoveRelatedSituationRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 19);

/**
 * @generated from message hero.situations.v2.RemoveMediaAttachmentForSituationRequest
 */
export type RemoveMediaAttachmentForSituationRequest = Message<"hero.situations.v2.RemoveMediaAttachmentForSituationRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: hero.situations.v2.MediaAttachment media_attachment = 2;
   */
  mediaAttachment?: MediaAttachment;
};

/**
 * Describes the message hero.situations.v2.RemoveMediaAttachmentForSituationRequest.
 * Use `create(RemoveMediaAttachmentForSituationRequestSchema)` to create a new message.
 */
export const RemoveMediaAttachmentForSituationRequestSchema: GenMessage<RemoveMediaAttachmentForSituationRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 20);

/**
 * New messages for adding additional info to a situation.
 *
 * @generated from message hero.situations.v2.AddAdditionalInfoRequest
 */
export type AddAdditionalInfoRequest = Message<"hero.situations.v2.AddAdditionalInfoRequest"> & {
  /**
   * The ID of the situation to update.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * A JSON string containing the additional info to be merged.
   *
   * @generated from field: string additional_info_json = 2;
   */
  additionalInfoJson: string;
};

/**
 * Describes the message hero.situations.v2.AddAdditionalInfoRequest.
 * Use `create(AddAdditionalInfoRequestSchema)` to create a new message.
 */
export const AddAdditionalInfoRequestSchema: GenMessage<AddAdditionalInfoRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 21);

/**
 * @generated from message hero.situations.v2.AddAdditionalInfoResponse
 */
export type AddAdditionalInfoResponse = Message<"hero.situations.v2.AddAdditionalInfoResponse"> & {
  /**
   * The ID of the updated situation.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * The updated additional_info_json object.
   *
   * @generated from field: string additional_info_json = 2;
   */
  additionalInfoJson: string;
};

/**
 * Describes the message hero.situations.v2.AddAdditionalInfoResponse.
 * Use `create(AddAdditionalInfoResponseSchema)` to create a new message.
 */
export const AddAdditionalInfoResponseSchema: GenMessage<AddAdditionalInfoResponse> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 22);

/**
 * ---------------------------------------------------------------------------
 * NEW SEARCH MESSAGES
 * ---------------------------------------------------------------------------
 *
 * @generated from message hero.situations.v2.DateRange
 */
export type DateRange = Message<"hero.situations.v2.DateRange"> & {
  /**
   * @generated from field: string from = 1;
   */
  from: string;

  /**
   * @generated from field: string to = 2;
   */
  to: string;
};

/**
 * Describes the message hero.situations.v2.DateRange.
 * Use `create(DateRangeSchema)` to create a new message.
 */
export const DateRangeSchema: GenMessage<DateRange> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 23);

/**
 * @generated from message hero.situations.v2.FieldQuery
 */
export type FieldQuery = Message<"hero.situations.v2.FieldQuery"> & {
  /**
   * The field to search in (title, description, etc.)
   *
   * @generated from field: string field = 1;
   */
  field: string;

  /**
   * The query text for this specific field
   *
   * @generated from field: string query = 2;
   */
  query: string;
};

/**
 * Describes the message hero.situations.v2.FieldQuery.
 * Use `create(FieldQuerySchema)` to create a new message.
 */
export const FieldQuerySchema: GenMessage<FieldQuery> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 24);

/**
 * @generated from message hero.situations.v2.HighlightResult
 */
export type HighlightResult = Message<"hero.situations.v2.HighlightResult"> & {
  /**
   * Field name that had a match
   *
   * @generated from field: string field = 1;
   */
  field: string;

  /**
   * Highlighted fragments with matched terms
   *
   * @generated from field: repeated string fragments = 2;
   */
  fragments: string[];
};

/**
 * Describes the message hero.situations.v2.HighlightResult.
 * Use `create(HighlightResultSchema)` to create a new message.
 */
export const HighlightResultSchema: GenMessage<HighlightResult> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 25);

/**
 * @generated from message hero.situations.v2.SearchSituationsRequest
 */
export type SearchSituationsRequest = Message<"hero.situations.v2.SearchSituationsRequest"> & {
  /**
   * Free-text / fuzzy query (all fields)
   *
   * @generated from field: string query = 1;
   */
  query: string;

  /**
   * Limit search scope (leave empty = all)
   *
   * @generated from field: repeated string search_fields = 2;
   */
  searchFields: string[];

  /**
   * Field-specific query terms
   *
   * @generated from field: repeated hero.situations.v2.FieldQuery field_queries = 21;
   */
  fieldQueries: FieldQuery[];

  /**
   * ─────────────── Fixed-value filters ───────────────
   *
   * @generated from field: repeated hero.situations.v2.SituationStatus status = 3;
   */
  status: SituationStatus[];

  /**
   * @generated from field: repeated hero.situations.v2.SituationType type = 4;
   */
  type: SituationType[];

  /**
   * @generated from field: repeated hero.situations.v2.TriggerSource trigger_source = 5;
   */
  triggerSource: TriggerSource[];

  /**
   * @generated from field: repeated int32 priority = 6;
   */
  priority: number[];

  /**
   * @generated from field: repeated string tags = 7;
   */
  tags: string[];

  /**
   * ─────────────── Date-range filters (inclusive RFC3339) ───────────────
   *
   * @generated from field: hero.situations.v2.DateRange create_time = 8;
   */
  createTime?: DateRange;

  /**
   * @generated from field: hero.situations.v2.DateRange update_time = 9;
   */
  updateTime?: DateRange;

  /**
   * @generated from field: hero.situations.v2.DateRange incident_time = 10;
   */
  incidentTime?: DateRange;

  /**
   * @generated from field: hero.situations.v2.DateRange resolved_time = 11;
   */
  resolvedTime?: DateRange;

  /**
   * @generated from field: hero.situations.v2.DateRange due_time = 12;
   */
  dueTime?: DateRange;

  /**
   * ─────────────── geo bounding box ───────────────
   *
   * @generated from field: double min_latitude = 13;
   */
  minLatitude: number;

  /**
   * @generated from field: double max_latitude = 14;
   */
  maxLatitude: number;

  /**
   * @generated from field: double min_longitude = 15;
   */
  minLongitude: number;

  /**
   * @generated from field: double max_longitude = 16;
   */
  maxLongitude: number;

  /**
   * ─────────────── Pagination & sorting ───────────────
   *
   * @generated from field: int32 page_size = 17;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 18;
   */
  pageToken: string;

  /**
   * ─────────────── enum based ordering ───────────────
   *
   * defaults to RELEVANCE
   *
   * @generated from field: hero.situations.v2.SearchOrderBy order_by = 19;
   */
  orderBy: SearchOrderBy;

  /**
   * true = ASC, false = DESC
   *
   * @generated from field: bool ascending = 20;
   */
  ascending: boolean;
};

/**
 * Describes the message hero.situations.v2.SearchSituationsRequest.
 * Use `create(SearchSituationsRequestSchema)` to create a new message.
 */
export const SearchSituationsRequestSchema: GenMessage<SearchSituationsRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 26);

/**
 * @generated from message hero.situations.v2.SearchSituationsResponse
 */
export type SearchSituationsResponse = Message<"hero.situations.v2.SearchSituationsResponse"> & {
  /**
   * @generated from field: repeated hero.situations.v2.Situation situations = 1;
   */
  situations: Situation[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;

  /**
   * New field for highlight information
   *
   * Key is situation ID
   *
   * @generated from field: map<string, hero.situations.v2.HighlightResult> highlights = 3;
   */
  highlights: { [key: string]: HighlightResult };

  /**
   * @generated from field: int32 total_results = 4;
   */
  totalResults: number;
};

/**
 * Describes the message hero.situations.v2.SearchSituationsResponse.
 * Use `create(SearchSituationsResponseSchema)` to create a new message.
 */
export const SearchSituationsResponseSchema: GenMessage<SearchSituationsResponse> = /*@__PURE__*/
  messageDesc(file_hero_situations_v2_situations, 27);

/**
 * Enum for the situation status.
 *
 * @generated from enum hero.situations.v2.SituationStatus
 */
export enum SituationStatus {
  /**
   * Default value. Indicates that the situation status is not specified.
   *
   * @generated from enum value: SITUATION_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Indicates that the situation has been created.
   *
   * @generated from enum value: SITUATION_STATUS_CREATED = 1;
   */
  CREATED = 1,

  /**
   * Indicates that the situation is currently being triaged.
   *
   * @generated from enum value: SITUATION_STATUS_TRIAGING = 2;
   */
  TRIAGING = 2,

  /**
   * Indicates that the situation is in the process of being dispatched.
   *
   * @generated from enum value: SITUATION_STATUS_DISPATCHING = 3;
   */
  DISPATCHING = 3,

  /**
   * Indicates that the situation is actively being addressed.
   *
   * @generated from enum value: SITUATION_STATUS_ADDRESSING = 4;
   */
  ADDRESSING = 4,

  /**
   * Indicates that the situation has been resolved.
   *
   * @generated from enum value: SITUATION_STATUS_RESOLVED = 5;
   */
  RESOLVED = 5,

  /**
   * Indicates that the situation has been completed.
   *
   * @generated from enum value: SITUATION_STATUS_COMPLETED = 6;
   */
  COMPLETED = 6,

  /**
   * Indicates that the situation has been escalated.
   *
   * @generated from enum value: SITUATION_STATUS_ESCALATED = 7;
   */
  ESCALATED = 7,
}

/**
 * Describes the enum hero.situations.v2.SituationStatus.
 */
export const SituationStatusSchema: GenEnum<SituationStatus> = /*@__PURE__*/
  enumDesc(file_hero_situations_v2_situations, 0);

/**
 * Enum for the trigger source of the situation.
 *
 * @generated from enum hero.situations.v2.TriggerSource
 */
export enum TriggerSource {
  /**
   * @generated from enum value: TRIGGER_SOURCE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: TRIGGER_SOURCE_PHONE_CALL = 1;
   */
  PHONE_CALL = 1,

  /**
   * @generated from enum value: TRIGGER_SOURCE_SMS = 2;
   */
  SMS = 2,

  /**
   * @generated from enum value: TRIGGER_SOURCE_CAMERA_EVENT = 3;
   */
  CAMERA_EVENT = 3,

  /**
   * @generated from enum value: TRIGGER_SOURCE_SOS = 4;
   */
  SOS = 4,

  /**
   * @generated from enum value: TRIGGER_SOURCE_RADIO = 5;
   */
  RADIO = 5,

  /**
   * @generated from enum value: TRIGGER_SOURCE_EMAIL = 6;
   */
  EMAIL = 6,

  /**
   * @generated from enum value: TRIGGER_SOURCE_MOBILE_APP = 7;
   */
  MOBILE_APP = 7,

  /**
   * @generated from enum value: TRIGGER_SOURCE_WEB_PORTAL = 8;
   */
  WEB_PORTAL = 8,

  /**
   * @generated from enum value: TRIGGER_SOURCE_SOCIAL_MEDIA = 9;
   */
  SOCIAL_MEDIA = 9,

  /**
   * @generated from enum value: TRIGGER_SOURCE_IOT_DEVICE = 10;
   */
  IOT_DEVICE = 10,

  /**
   * @generated from enum value: TRIGGER_SOURCE_MANUAL_ENTRY = 11;
   */
  MANUAL_ENTRY = 11,

  /**
   * @generated from enum value: TRIGGER_SOURCE_AUTOMATED_ALERT = 12;
   */
  AUTOMATED_ALERT = 12,
}

/**
 * Describes the enum hero.situations.v2.TriggerSource.
 */
export const TriggerSourceSchema: GenEnum<TriggerSource> = /*@__PURE__*/
  enumDesc(file_hero_situations_v2_situations, 1);

/**
 * Enum for the update source in an update entry.
 *
 * @generated from enum hero.situations.v2.UpdateSource
 */
export enum UpdateSource {
  /**
   * @generated from enum value: UPDATE_SOURCE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * Update generated automatically by the system.
   *
   * @generated from enum value: UPDATE_SOURCE_AUTOMATED_SYSTEM = 1;
   */
  AUTOMATED_SYSTEM = 1,

  /**
   * Update manually provided by a human operator.
   *
   * @generated from enum value: UPDATE_SOURCE_HUMAN_OPERATOR = 2;
   */
  HUMAN_OPERATOR = 2,

  /**
   * Update generated by a sensor or other automated device.
   *
   * @generated from enum value: UPDATE_SOURCE_SENSOR_TRIGGERED = 3;
   */
  SENSOR_TRIGGERED = 3,

  /**
   * Update provided by an external system or API.
   *
   * @generated from enum value: UPDATE_SOURCE_THIRD_PARTY_API = 4;
   */
  THIRD_PARTY_API = 4,

  /**
   * Update created by sideeffect of other API call.
   *
   * @generated from enum value: UPDATE_SOURCE_API_SIDE_EFFECT = 5;
   */
  API_SIDE_EFFECT = 5,
}

/**
 * Describes the enum hero.situations.v2.UpdateSource.
 */
export const UpdateSourceSchema: GenEnum<UpdateSource> = /*@__PURE__*/
  enumDesc(file_hero_situations_v2_situations, 2);

/**
 * Enum for various types of situations.
 *
 * @generated from enum hero.situations.v2.SituationType
 */
export enum SituationType {
  /**
   * @generated from enum value: SITUATION_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: SITUATION_TYPE_VANDALISM = 1;
   */
  VANDALISM = 1,

  /**
   * @generated from enum value: SITUATION_TYPE_INTRUSION = 2;
   */
  INTRUSION = 2,

  /**
   * @generated from enum value: SITUATION_TYPE_MEDICAL_EMERGENCY = 3;
   */
  MEDICAL_EMERGENCY = 3,

  /**
   * @generated from enum value: SITUATION_TYPE_FIGHT = 4;
   */
  FIGHT = 4,

  /**
   * @generated from enum value: SITUATION_TYPE_THEFT = 5;
   */
  THEFT = 5,

  /**
   * @generated from enum value: SITUATION_TYPE_SUSPICIOUS_ACTIVITY = 6;
   */
  SUSPICIOUS_ACTIVITY = 6,

  /**
   * @generated from enum value: SITUATION_TYPE_FIRE = 7;
   */
  FIRE = 7,

  /**
   * @generated from enum value: SITUATION_TYPE_NOISE_COMPLAINT = 8;
   */
  NOISE_COMPLAINT = 8,

  /**
   * @generated from enum value: SITUATION_TYPE_OTHER = 9;
   */
  OTHER = 9,

  /**
   * @generated from enum value: SITUATION_TYPE_WEAPON_DETECTED = 10;
   */
  WEAPON_DETECTED = 10,

  /**
   * @generated from enum value: SITUATION_TYPE_DRUG_ACTIVITY = 11;
   */
  DRUG_ACTIVITY = 11,

  /**
   * @generated from enum value: SITUATION_TYPE_ALCOHOL_VIOLATION = 12;
   */
  ALCOHOL_VIOLATION = 12,

  /**
   * @generated from enum value: SITUATION_TYPE_HAZARDOUS_MATERIAL = 13;
   */
  HAZARDOUS_MATERIAL = 13,

  /**
   * @generated from enum value: SITUATION_TYPE_LOST_PERSON = 14;
   */
  LOST_PERSON = 14,

  /**
   * @generated from enum value: SITUATION_TYPE_TRESPASSING = 15;
   */
  TRESPASSING = 15,

  /**
   * @generated from enum value: SITUATION_TYPE_VEHICLE_ACCIDENT = 16;
   */
  VEHICLE_ACCIDENT = 16,

  /**
   * @generated from enum value: SITUATION_TYPE_POWER_OUTAGE = 17;
   */
  POWER_OUTAGE = 17,

  /**
   * @generated from enum value: SITUATION_TYPE_WATER_LEAK = 18;
   */
  WATER_LEAK = 18,

  /**
   * @generated from enum value: SITUATION_TYPE_GAS_LEAK = 19;
   */
  GAS_LEAK = 19,

  /**
   * @generated from enum value: SITUATION_TYPE_ANIMAL_INCIDENT = 20;
   */
  ANIMAL_INCIDENT = 20,

  /**
   * @generated from enum value: SITUATION_TYPE_PARKING_VIOLATION = 21;
   */
  PARKING_VIOLATION = 21,

  /**
   * @generated from enum value: SITUATION_TYPE_LOCKOUT = 22;
   */
  LOCKOUT = 22,

  /**
   * @generated from enum value: SITUATION_TYPE_BURGLARY = 23;
   */
  BURGLARY = 23,

  /**
   * @generated from enum value: SITUATION_TYPE_TRAFFIC_ACCIDENT = 24;
   */
  TRAFFIC_ACCIDENT = 24,

  /**
   * @generated from enum value: SITUATION_TYPE_PUBLIC_DISTURBANCE = 25;
   */
  PUBLIC_DISTURBANCE = 25,

  /**
   * @generated from enum value: SITUATION_TYPE_SUSPICIOUS_PACKAGE = 26;
   */
  SUSPICIOUS_PACKAGE = 26,
}

/**
 * Describes the enum hero.situations.v2.SituationType.
 */
export const SituationTypeSchema: GenEnum<SituationType> = /*@__PURE__*/
  enumDesc(file_hero_situations_v2_situations, 3);

/**
 * Enum for the order by field in the search situations request.
 *
 * @generated from enum hero.situations.v2.SearchOrderBy
 */
export enum SearchOrderBy {
  /**
   * @generated from enum value: SEARCH_ORDER_BY_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_RELEVANCE = 1;
   */
  RELEVANCE = 1,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_CREATE_TIME = 2;
   */
  CREATE_TIME = 2,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_UPDATE_TIME = 3;
   */
  UPDATE_TIME = 3,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_PRIORITY = 4;
   */
  PRIORITY = 4,
}

/**
 * Describes the enum hero.situations.v2.SearchOrderBy.
 */
export const SearchOrderBySchema: GenEnum<SearchOrderBy> = /*@__PURE__*/
  enumDesc(file_hero_situations_v2_situations, 4);

/**
 * Service definition with API methods for managing situations.
 *
 * @generated from service hero.situations.v2.SituationService
 */
export const SituationService: GenService<{
  /**
   * Create a new situation.
   *
   * @generated from rpc hero.situations.v2.SituationService.CreateSituation
   */
  createSituation: {
    methodKind: "unary";
    input: typeof CreateSituationRequestSchema;
    output: typeof CreateSituationResponseSchema;
  },
  /**
   * Retrieve a situation by its ID.
   *
   * @generated from rpc hero.situations.v2.SituationService.GetSituation
   */
  getSituation: {
    methodKind: "unary";
    input: typeof GetSituationRequestSchema;
    output: typeof SituationSchema;
  },
  /**
   * Update an existing situation.
   *
   * @generated from rpc hero.situations.v2.SituationService.UpdateSituation
   */
  updateSituation: {
    methodKind: "unary";
    input: typeof UpdateSituationRequestSchema;
    output: typeof SituationSchema;
  },
  /**
   * List situations with optional filtering, ordering, and pagination.
   *
   * @generated from rpc hero.situations.v2.SituationService.ListSituations
   */
  listSituations: {
    methodKind: "unary";
    input: typeof ListSituationsRequestSchema;
    output: typeof ListSituationsResponseSchema;
  },
  /**
   * List situations associated with a specific asset.
   *
   * @generated from rpc hero.situations.v2.SituationService.ListSituationsForAsset
   */
  listSituationsForAsset: {
    methodKind: "unary";
    input: typeof ListSituationsForAssetRequestSchema;
    output: typeof ListSituationsForAssetResponseSchema;
  },
  /**
   * robust search RPC (added)
   *
   * @generated from rpc hero.situations.v2.SituationService.SearchSituations
   */
  searchSituations: {
    methodKind: "unary";
    input: typeof SearchSituationsRequestSchema;
    output: typeof SearchSituationsResponseSchema;
  },
  /**
   * Delete a situation by its ID.
   *
   * @generated from rpc hero.situations.v2.SituationService.DeleteSituation
   */
  deleteSituation: {
    methodKind: "unary";
    input: typeof DeleteSituationRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * Add an update entry to an existing situation.
   *
   * @generated from rpc hero.situations.v2.SituationService.AddSituationUpdate
   */
  addSituationUpdate: {
    methodKind: "unary";
    input: typeof AddSituationUpdateRequestSchema;
    output: typeof SituationSchema;
  },
  /**
   * Remove an update entry from an existing situation.
   *
   * @generated from rpc hero.situations.v2.SituationService.RemoveSituationUpdate
   */
  removeSituationUpdate: {
    methodKind: "unary";
    input: typeof RemoveSituationUpdateRequestSchema;
    output: typeof SituationSchema;
  },
  /**
   * Add a tag to an existing situation.
   *
   * @generated from rpc hero.situations.v2.SituationService.AddSituationTag
   */
  addSituationTag: {
    methodKind: "unary";
    input: typeof AddSituationTagRequestSchema;
    output: typeof SituationSchema;
  },
  /**
   * Remove a tag from an existing situation.
   *
   * @generated from rpc hero.situations.v2.SituationService.RemoveSituationTag
   */
  removeSituationTag: {
    methodKind: "unary";
    input: typeof RemoveSituationTagRequestSchema;
    output: typeof SituationSchema;
  },
  /**
   * Add a related situation reference.
   *
   * @generated from rpc hero.situations.v2.SituationService.AddRelatedSituation
   */
  addRelatedSituation: {
    methodKind: "unary";
    input: typeof AddRelatedSituationRequestSchema;
    output: typeof SituationSchema;
  },
  /**
   * Remove a related situation reference.
   *
   * @generated from rpc hero.situations.v2.SituationService.RemoveRelatedSituation
   */
  removeRelatedSituation: {
    methodKind: "unary";
    input: typeof RemoveRelatedSituationRequestSchema;
    output: typeof SituationSchema;
  },
  /**
   * Add a media attachment to an existing situation.
   *
   * @generated from rpc hero.situations.v2.SituationService.AddMediaAttachmentForSituation
   */
  addMediaAttachmentForSituation: {
    methodKind: "unary";
    input: typeof AddMediaAttachmentForSituationRequestSchema;
    output: typeof SituationSchema;
  },
  /**
   * Remove a media attachment from an existing situation.
   *
   * @generated from rpc hero.situations.v2.SituationService.RemoveMediaAttachmentForSituation
   */
  removeMediaAttachmentForSituation: {
    methodKind: "unary";
    input: typeof RemoveMediaAttachmentForSituationRequestSchema;
    output: typeof SituationSchema;
  },
  /**
   * Add additional info to a situation by merging provided JSON into the existing additional_info_json.
   *
   * @generated from rpc hero.situations.v2.SituationService.AddAdditionalInfo
   */
  addAdditionalInfo: {
    methodKind: "unary";
    input: typeof AddAdditionalInfoRequestSchema;
    output: typeof AddAdditionalInfoResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_situations_v2_situations, 0);

