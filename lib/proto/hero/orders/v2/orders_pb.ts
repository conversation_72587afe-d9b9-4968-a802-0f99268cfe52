// @generated by protoc-gen-es v2.6.1 with parameter "target=ts"
// @generated from file hero/orders/v2/orders.proto (package hero.orders.v2, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { EmptySchema } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import type { AssetType } from "../../assets/v2/assets_pb";
import { file_hero_assets_v2_assets } from "../../assets/v2/assets_pb";
import type { UpdateSource } from "../../situations/v2/situations_pb";
import { file_hero_situations_v2_situations } from "../../situations/v2/situations_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/orders/v2/orders.proto.
 */
export const file_hero_orders_v2_orders: GenFile = /*@__PURE__*/
  fileDesc("ChtoZXJvL29yZGVycy92Mi9vcmRlcnMucHJvdG8SDmhlcm8ub3JkZXJzLnYyIn0KEE9yZGVyUGVybWlzc2lvbnMSNAoRY2FuX2NoYW5nZV9zdGF0dXMYASADKA4yGS5oZXJvLmFzc2V0cy52Mi5Bc3NldFR5cGUSMwoQY2FuX2Fzc2lnbl9hc3NldBgCIAMoDjIZLmhlcm8uYXNzZXRzLnYyLkFzc2V0VHlwZSJvChBPcmRlclVwZGF0ZUVudHJ5Eg8KB21lc3NhZ2UYASABKAkSEQoJdGltZXN0YW1wGAIgASgJEjcKDXVwZGF0ZV9zb3VyY2UYAyABKA4yIC5oZXJvLnNpdHVhdGlvbnMudjIuVXBkYXRlU291cmNlIt0CChZPcmRlclN0YXR1c1VwZGF0ZUVudHJ5EhcKD2VudHJ5X3RpbWVzdGFtcBgBIAEoCRIvCgpuZXdfc3RhdHVzGAIgASgOMhsuaGVyby5vcmRlcnMudjIuT3JkZXJTdGF0dXMSNAoPcHJldmlvdXNfc3RhdHVzGAMgASgOMhsuaGVyby5vcmRlcnMudjIuT3JkZXJTdGF0dXMSIAoYbmV3X3R5cGVfc3BlY2lmaWNfc3RhdHVzGAQgASgJEiUKHXByZXZpb3VzX3R5cGVfc3BlY2lmaWNfc3RhdHVzGAUgASgJEgwKBG5vdGUYBiABKAkSEgoKdXBkYXRlcl9pZBgHIAEoCRI3Cg11cGRhdGVfc291cmNlGAggASgOMiAuaGVyby5zaXR1YXRpb25zLnYyLlVwZGF0ZVNvdXJjZRIfChdzdGF0dXNfdXBkYXRlX3RpbWVzdGFtcBgJIAEoCSKnBwoFT3JkZXISCgoCaWQYASABKAkSFAoMc2l0dWF0aW9uX2lkGAIgASgJEhAKCGFzc2V0X2lkGAMgASgJEicKBHR5cGUYBCABKA4yGS5oZXJvLm9yZGVycy52Mi5PcmRlclR5cGUSKwoGc3RhdHVzGAUgASgOMhsuaGVyby5vcmRlcnMudjIuT3JkZXJTdGF0dXMSFAoMaW5zdHJ1Y3Rpb25zGAYgASgJEhAKCHByaW9yaXR5GAcgASgFEhwKFGFkZGl0aW9uYWxfaW5mb19qc29uGAggASgJEhwKFHR5cGVfc3BlY2lmaWNfc3RhdHVzGAkgASgJEg0KBW5vdGVzGAogASgJEhMKC2NyZWF0ZV90aW1lGAsgASgJEhMKC3VwZGF0ZV90aW1lGAwgASgJEhcKD2NvbXBsZXRpb25fdGltZRgNIAEoCRIxCgd1cGRhdGVzGA4gAygLMiAuaGVyby5vcmRlcnMudjIuT3JkZXJVcGRhdGVFbnRyeRIVCg1hc3NpZ25lZF90aW1lGA8gASgJEhkKEWFja25vd2xlZGdlZF90aW1lGBAgASgJEiEKGWVzdGltYXRlZF9jb21wbGV0aW9uX3RpbWUYESABKAkSKAogY2FuY2VsbGF0aW9uX29yX3JlamVjdGlvbl9yZWFzb24YEiABKAkSEwoLcmV0cnlfY291bnQYEyABKAUSNAoKY3JlYXRlZF9ieRgUIAEoDjIgLmhlcm8uc2l0dWF0aW9ucy52Mi5VcGRhdGVTb3VyY2USDQoFdGl0bGUYFSABKAkSNgoTYWxsb3dlZF9hc3NldF90eXBlcxgXIAMoDjIZLmhlcm8uYXNzZXRzLnYyLkFzc2V0VHlwZRIVCg1zbm9vemVfcmVhc29uGBggASgJEhQKDHNub296ZV91bnRpbBgZIAEoCRIUCgxzbm9vemVfY291bnQYGiABKAUSHQoVYmxhY2tsaXN0ZWRfYXNzZXRfaWRzGBsgAygJEhUKDXJlc291cmNlX3R5cGUYHCABKAkSNQoLcGVybWlzc2lvbnMYHSABKAsyIC5oZXJvLm9yZGVycy52Mi5PcmRlclBlcm1pc3Npb25zEj4KDnN0YXR1c191cGRhdGVzGB4gAygLMiYuaGVyby5vcmRlcnMudjIuT3JkZXJTdGF0dXNVcGRhdGVFbnRyeRIRCglyZXBvcnRfaWQYHyABKAkSFwoPcmV2aWV3X3JvdW5kX2lkGCAgASgJIjoKEkNyZWF0ZU9yZGVyUmVxdWVzdBIkCgVvcmRlchgBIAEoCzIVLmhlcm8ub3JkZXJzLnYyLk9yZGVyIjsKE0NyZWF0ZU9yZGVyUmVzcG9uc2USJAoFb3JkZXIYASABKAsyFS5oZXJvLm9yZGVycy52Mi5PcmRlciIdCg9HZXRPcmRlclJlcXVlc3QSCgoCaWQYASABKAkiOgoSVXBkYXRlT3JkZXJSZXF1ZXN0EiQKBW9yZGVyGAEgASgLMhUuaGVyby5vcmRlcnMudjIuT3JkZXIiogEKEUxpc3RPcmRlcnNSZXF1ZXN0EhEKCXBhZ2Vfc2l6ZRgBIAEoBRISCgpwYWdlX3Rva2VuGAIgASgJEisKBnN0YXR1cxgDIAEoDjIbLmhlcm8ub3JkZXJzLnYyLk9yZGVyU3RhdHVzEicKBHR5cGUYBCABKA4yGS5oZXJvLm9yZGVycy52Mi5PcmRlclR5cGUSEAoIb3JkZXJfYnkYBSABKAkiVAoSTGlzdE9yZGVyc1Jlc3BvbnNlEiUKBm9yZGVycxgBIAMoCzIVLmhlcm8ub3JkZXJzLnYyLk9yZGVyEhcKD25leHRfcGFnZV90b2tlbhgCIAEoCSIgChJEZWxldGVPcmRlclJlcXVlc3QSCgoCaWQYASABKAkiVQoVQWRkT3JkZXJVcGRhdGVSZXF1ZXN0EgoKAmlkGAEgASgJEjAKBnVwZGF0ZRgCIAEoCzIgLmhlcm8ub3JkZXJzLnYyLk9yZGVyVXBkYXRlRW50cnkiWAoYUmVtb3ZlT3JkZXJVcGRhdGVSZXF1ZXN0EgoKAmlkGAEgASgJEjAKBnVwZGF0ZRgCIAEoCzIgLmhlcm8ub3JkZXJzLnYyLk9yZGVyVXBkYXRlRW50cnkiXwoaQWRkQWxsb3dlZEFzc2V0VHlwZVJlcXVlc3QSCgoCaWQYASABKAkSNQoSYWxsb3dlZF9hc3NldF90eXBlGAIgASgOMhkuaGVyby5hc3NldHMudjIuQXNzZXRUeXBlImIKHVJlbW92ZUFsbG93ZWRBc3NldFR5cGVSZXF1ZXN0EgoKAmlkGAEgASgJEjUKEmFsbG93ZWRfYXNzZXRfdHlwZRgCIAEoDjIZLmhlcm8uYXNzZXRzLnYyLkFzc2V0VHlwZSI8ChxBZGRCbGFja2xpc3RlZEFzc2V0SWRSZXF1ZXN0EgoKAmlkGAEgASgJEhAKCGFzc2V0X2lkGAIgASgJIj8KH1JlbW92ZUJsYWNrbGlzdGVkQXNzZXRJZFJlcXVlc3QSCgoCaWQYASABKAkSEAoIYXNzZXRfaWQYAiABKAkiJQoXQWNrbm93bGVkZ2VPcmRlclJlcXVlc3QSCgoCaWQYASABKAkiMAoSUmVqZWN0T3JkZXJSZXF1ZXN0EgoKAmlkGAEgASgJEg4KBnJlYXNvbhgCIAEoCSJNChJTbm9vemVPcmRlclJlcXVlc3QSCgoCaWQYASABKAkSFQoNc25vb3plX3JlYXNvbhgCIAEoCRIUCgxzbm9vemVfdW50aWwYAyABKAkiMAoSQ2FuY2VsT3JkZXJSZXF1ZXN0EgoKAmlkGAEgASgJEg4KBnJlYXNvbhgCIAEoCSIiChRDb21wbGV0ZU9yZGVyUmVxdWVzdBIKCgJpZBgBIAEoCSJiCidMaXN0QWN0aXZlQXNzaWduZWRPcmRlcnNGb3JBc3NldFJlcXVlc3QSEAoIYXNzZXRfaWQYASABKAkSEQoJcGFnZV9zaXplGAIgASgFEhIKCnBhZ2VfdG9rZW4YAyABKAkiagooTGlzdEFjdGl2ZUFzc2lnbmVkT3JkZXJzRm9yQXNzZXRSZXNwb25zZRIlCgZvcmRlcnMYASADKAsyFS5oZXJvLm9yZGVycy52Mi5PcmRlchIXCg9uZXh0X3BhZ2VfdG9rZW4YAiABKAkiVwocTGlzdE5ld09yZGVyc0ZvckFzc2V0UmVxdWVzdBIQCghhc3NldF9pZBgBIAEoCRIRCglwYWdlX3NpemUYAiABKAUSEgoKcGFnZV90b2tlbhgDIAEoCSJfCh1MaXN0TmV3T3JkZXJzRm9yQXNzZXRSZXNwb25zZRIlCgZvcmRlcnMYASADKAsyFS5oZXJvLm9yZGVycy52Mi5PcmRlchIXCg9uZXh0X3BhZ2VfdG9rZW4YAiABKAkigQEKGUxpc3RPcmRlcnNGb3JBc3NldFJlcXVlc3QSEAoIYXNzZXRfaWQYASABKAkSEQoJcGFnZV9zaXplGAIgASgFEhIKCnBhZ2VfdG9rZW4YAyABKAkSKwoGc3RhdHVzGAQgASgOMhsuaGVyby5vcmRlcnMudjIuT3JkZXJTdGF0dXMiXAoaTGlzdE9yZGVyc0ZvckFzc2V0UmVzcG9uc2USJQoGb3JkZXJzGAEgAygLMhUuaGVyby5vcmRlcnMudjIuT3JkZXISFwoPbmV4dF9wYWdlX3Rva2VuGAIgASgJIokBCh1MaXN0T3JkZXJzRm9yU2l0dWF0aW9uUmVxdWVzdBIUCgxzaXR1YXRpb25faWQYASABKAkSEQoJcGFnZV9zaXplGAIgASgFEhIKCnBhZ2VfdG9rZW4YAyABKAkSKwoGc3RhdHVzGAQgASgOMhsuaGVyby5vcmRlcnMudjIuT3JkZXJTdGF0dXMiYAoeTGlzdE9yZGVyc0ZvclNpdHVhdGlvblJlc3BvbnNlEiUKBm9yZGVycxgBIAMoCzIVLmhlcm8ub3JkZXJzLnYyLk9yZGVyEhcKD25leHRfcGFnZV90b2tlbhgCIAEoCSKDAQoaTGlzdE9yZGVyc0ZvclJlcG9ydFJlcXVlc3QSEQoJcmVwb3J0X2lkGAEgASgJEhEKCXBhZ2Vfc2l6ZRgCIAEoBRISCgpwYWdlX3Rva2VuGAMgASgJEisKBnN0YXR1cxgEIAEoDjIbLmhlcm8ub3JkZXJzLnYyLk9yZGVyU3RhdHVzIl0KG0xpc3RPcmRlcnNGb3JSZXBvcnRSZXNwb25zZRIlCgZvcmRlcnMYASADKAsyFS5oZXJvLm9yZGVycy52Mi5PcmRlchIXCg9uZXh0X3BhZ2VfdG9rZW4YAiABKAkijgEKH0xpc3RPcmRlcnNGb3JSZXZpZXdSb3VuZFJlcXVlc3QSFwoPcmV2aWV3X3JvdW5kX2lkGAEgASgJEhEKCXBhZ2Vfc2l6ZRgCIAEoBRISCgpwYWdlX3Rva2VuGAMgASgJEisKBnN0YXR1cxgEIAEoDjIbLmhlcm8ub3JkZXJzLnYyLk9yZGVyU3RhdHVzImIKIExpc3RPcmRlcnNGb3JSZXZpZXdSb3VuZFJlc3BvbnNlEiUKBm9yZGVycxgBIAMoCzIVLmhlcm8ub3JkZXJzLnYyLk9yZGVyEhcKD25leHRfcGFnZV90b2tlbhgCIAEoCSJiCh1VcGRhdGVPcmRlclBlcm1pc3Npb25zUmVxdWVzdBIKCgJpZBgBIAEoCRI1CgtwZXJtaXNzaW9ucxgCIAEoCzIgLmhlcm8ub3JkZXJzLnYyLk9yZGVyUGVybWlzc2lvbnMiRAoYQWRkQWRkaXRpb25hbEluZm9SZXF1ZXN0EgoKAmlkGAEgASgJEhwKFGFkZGl0aW9uYWxfaW5mb19qc29uGAIgASgJIkUKGUFkZEFkZGl0aW9uYWxJbmZvUmVzcG9uc2USCgoCaWQYASABKAkSHAoUYWRkaXRpb25hbF9pbmZvX2pzb24YAiABKAkiaAobQWRkT3JkZXJTdGF0dXNVcGRhdGVSZXF1ZXN0EgoKAmlkGAEgASgJEj0KDXN0YXR1c191cGRhdGUYAiABKAsyJi5oZXJvLm9yZGVycy52Mi5PcmRlclN0YXR1c1VwZGF0ZUVudHJ5Ku8BCgtPcmRlclN0YXR1cxIcChhPUkRFUl9TVEFUVVNfVU5TUEVDSUZJRUQQABIYChRPUkRFUl9TVEFUVVNfQ1JFQVRFRBABEh0KGU9SREVSX1NUQVRVU19BQ0tOT1dMRURHRUQQAhIZChVPUkRFUl9TVEFUVVNfUkVKRUNURUQQAxIYChRPUkRFUl9TVEFUVVNfU05PT1pFRBAEEhwKGE9SREVSX1NUQVRVU19JTl9QUk9HUkVTUxAFEhoKFk9SREVSX1NUQVRVU19DT01QTEVURUQQBhIaChZPUkRFUl9TVEFUVVNfQ0FOQ0VMTEVEEAcqzwIKCU9yZGVyVHlwZRIaChZPUkRFUl9UWVBFX1VOU1BFQ0lGSUVEEAASIwofT1JERVJfVFlQRV9UUklBR0VfTUVNQkVSX1JFUE9SVBABEhwKGE9SREVSX1RZUEVfQVNTSVNUX01FTUJFUhACEiIKHk9SREVSX1RZUEVfU1VCTUlUX0ZJTkFMX1JFUE9SVBADEhsKF09SREVSX1RZUEVfQVNTSUdOX0FHRU5UEAQSIgoeT1JERVJfVFlQRV9UUklBR0VfQUdFTlRfUkVQT1JUEAUSJQohT1JERVJfVFlQRV9UUklBR0VfQ0FNRVJBX0lOQ0lERU5UEAYSGwoXT1JERVJfVFlQRV9XUklURV9SRVBPUlQQBxIcChhPUkRFUl9UWVBFX1JFVklFV19SRVBPUlQQCBIcChhPUkRFUl9UWVBFX1JFVklTRV9SRVBPUlQQCTK/EgoMT3JkZXJTZXJ2aWNlElYKC0NyZWF0ZU9yZGVyEiIuaGVyby5vcmRlcnMudjIuQ3JlYXRlT3JkZXJSZXF1ZXN0GiMuaGVyby5vcmRlcnMudjIuQ3JlYXRlT3JkZXJSZXNwb25zZRJCCghHZXRPcmRlchIfLmhlcm8ub3JkZXJzLnYyLkdldE9yZGVyUmVxdWVzdBoVLmhlcm8ub3JkZXJzLnYyLk9yZGVyEkgKC1VwZGF0ZU9yZGVyEiIuaGVyby5vcmRlcnMudjIuVXBkYXRlT3JkZXJSZXF1ZXN0GhUuaGVyby5vcmRlcnMudjIuT3JkZXISUwoKTGlzdE9yZGVycxIhLmhlcm8ub3JkZXJzLnYyLkxpc3RPcmRlcnNSZXF1ZXN0GiIuaGVyby5vcmRlcnMudjIuTGlzdE9yZGVyc1Jlc3BvbnNlEkkKC0RlbGV0ZU9yZGVyEiIuaGVyby5vcmRlcnMudjIuRGVsZXRlT3JkZXJSZXF1ZXN0GhYuZ29vZ2xlLnByb3RvYnVmLkVtcHR5Ek4KDkFkZE9yZGVyVXBkYXRlEiUuaGVyby5vcmRlcnMudjIuQWRkT3JkZXJVcGRhdGVSZXF1ZXN0GhUuaGVyby5vcmRlcnMudjIuT3JkZXISVAoRUmVtb3ZlT3JkZXJVcGRhdGUSKC5oZXJvLm9yZGVycy52Mi5SZW1vdmVPcmRlclVwZGF0ZVJlcXVlc3QaFS5oZXJvLm9yZGVycy52Mi5PcmRlchJYChNBZGRBbGxvd2VkQXNzZXRUeXBlEiouaGVyby5vcmRlcnMudjIuQWRkQWxsb3dlZEFzc2V0VHlwZVJlcXVlc3QaFS5oZXJvLm9yZGVycy52Mi5PcmRlchJeChZSZW1vdmVBbGxvd2VkQXNzZXRUeXBlEi0uaGVyby5vcmRlcnMudjIuUmVtb3ZlQWxsb3dlZEFzc2V0VHlwZVJlcXVlc3QaFS5oZXJvLm9yZGVycy52Mi5PcmRlchJcChVBZGRCbGFja2xpc3RlZEFzc2V0SWQSLC5oZXJvLm9yZGVycy52Mi5BZGRCbGFja2xpc3RlZEFzc2V0SWRSZXF1ZXN0GhUuaGVyby5vcmRlcnMudjIuT3JkZXISYgoYUmVtb3ZlQmxhY2tsaXN0ZWRBc3NldElkEi8uaGVyby5vcmRlcnMudjIuUmVtb3ZlQmxhY2tsaXN0ZWRBc3NldElkUmVxdWVzdBoVLmhlcm8ub3JkZXJzLnYyLk9yZGVyElIKEEFja25vd2xlZGdlT3JkZXISJy5oZXJvLm9yZGVycy52Mi5BY2tub3dsZWRnZU9yZGVyUmVxdWVzdBoVLmhlcm8ub3JkZXJzLnYyLk9yZGVyEkgKC1JlamVjdE9yZGVyEiIuaGVyby5vcmRlcnMudjIuUmVqZWN0T3JkZXJSZXF1ZXN0GhUuaGVyby5vcmRlcnMudjIuT3JkZXISSAoLU25vb3plT3JkZXISIi5oZXJvLm9yZGVycy52Mi5Tbm9vemVPcmRlclJlcXVlc3QaFS5oZXJvLm9yZGVycy52Mi5PcmRlchJICgtDYW5jZWxPcmRlchIiLmhlcm8ub3JkZXJzLnYyLkNhbmNlbE9yZGVyUmVxdWVzdBoVLmhlcm8ub3JkZXJzLnYyLk9yZGVyEkwKDUNvbXBsZXRlT3JkZXISJC5oZXJvLm9yZGVycy52Mi5Db21wbGV0ZU9yZGVyUmVxdWVzdBoVLmhlcm8ub3JkZXJzLnYyLk9yZGVyEpUBCiBMaXN0QWN0aXZlQXNzaWduZWRPcmRlcnNGb3JBc3NldBI3Lmhlcm8ub3JkZXJzLnYyLkxpc3RBY3RpdmVBc3NpZ25lZE9yZGVyc0ZvckFzc2V0UmVxdWVzdBo4Lmhlcm8ub3JkZXJzLnYyLkxpc3RBY3RpdmVBc3NpZ25lZE9yZGVyc0ZvckFzc2V0UmVzcG9uc2USdAoVTGlzdE5ld09yZGVyc0ZvckFzc2V0EiwuaGVyby5vcmRlcnMudjIuTGlzdE5ld09yZGVyc0ZvckFzc2V0UmVxdWVzdBotLmhlcm8ub3JkZXJzLnYyLkxpc3ROZXdPcmRlcnNGb3JBc3NldFJlc3BvbnNlEl4KFlVwZGF0ZU9yZGVyUGVybWlzc2lvbnMSLS5oZXJvLm9yZGVycy52Mi5VcGRhdGVPcmRlclBlcm1pc3Npb25zUmVxdWVzdBoVLmhlcm8ub3JkZXJzLnYyLk9yZGVyEncKFkxpc3RPcmRlcnNGb3JTaXR1YXRpb24SLS5oZXJvLm9yZGVycy52Mi5MaXN0T3JkZXJzRm9yU2l0dWF0aW9uUmVxdWVzdBouLmhlcm8ub3JkZXJzLnYyLkxpc3RPcmRlcnNGb3JTaXR1YXRpb25SZXNwb25zZRJrChJMaXN0T3JkZXJzRm9yQXNzZXQSKS5oZXJvLm9yZGVycy52Mi5MaXN0T3JkZXJzRm9yQXNzZXRSZXF1ZXN0GiouaGVyby5vcmRlcnMudjIuTGlzdE9yZGVyc0ZvckFzc2V0UmVzcG9uc2USbgoTTGlzdE9yZGVyc0ZvclJlcG9ydBIqLmhlcm8ub3JkZXJzLnYyLkxpc3RPcmRlcnNGb3JSZXBvcnRSZXF1ZXN0GisuaGVyby5vcmRlcnMudjIuTGlzdE9yZGVyc0ZvclJlcG9ydFJlc3BvbnNlEn0KGExpc3RPcmRlcnNGb3JSZXZpZXdSb3VuZBIvLmhlcm8ub3JkZXJzLnYyLkxpc3RPcmRlcnNGb3JSZXZpZXdSb3VuZFJlcXVlc3QaMC5oZXJvLm9yZGVycy52Mi5MaXN0T3JkZXJzRm9yUmV2aWV3Um91bmRSZXNwb25zZRJoChFBZGRBZGRpdGlvbmFsSW5mbxIoLmhlcm8ub3JkZXJzLnYyLkFkZEFkZGl0aW9uYWxJbmZvUmVxdWVzdBopLmhlcm8ub3JkZXJzLnYyLkFkZEFkZGl0aW9uYWxJbmZvUmVzcG9uc2USWgoUQWRkT3JkZXJTdGF0dXNVcGRhdGUSKy5oZXJvLm9yZGVycy52Mi5BZGRPcmRlclN0YXR1c1VwZGF0ZVJlcXVlc3QaFS5oZXJvLm9yZGVycy52Mi5PcmRlckIdWhtwcm90by9oZXJvL29yZGVycy92MjtvcmRlcnNiBnByb3RvMw", [file_google_protobuf_empty, file_hero_assets_v2_assets, file_hero_situations_v2_situations]);

/**
 * ----------------------------------------------
 * Permissions sub-message
 * ----------------------------------------------
 *
 * @generated from message hero.orders.v2.OrderPermissions
 */
export type OrderPermissions = Message<"hero.orders.v2.OrderPermissions"> & {
  /**
   * Which AssetTypes are allowed to change this order's status.
   *
   * @generated from field: repeated hero.assets.v2.AssetType can_change_status = 1;
   */
  canChangeStatus: AssetType[];

  /**
   * Which AssetTypes are allowed to add/change the assigned asset_id.
   *
   * @generated from field: repeated hero.assets.v2.AssetType can_assign_asset = 2;
   */
  canAssignAsset: AssetType[];
};

/**
 * Describes the message hero.orders.v2.OrderPermissions.
 * Use `create(OrderPermissionsSchema)` to create a new message.
 */
export const OrderPermissionsSchema: GenMessage<OrderPermissions> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 0);

/**
 * ----------------------------------------------
 * Messages
 * ----------------------------------------------
 *
 * @generated from message hero.orders.v2.OrderUpdateEntry
 */
export type OrderUpdateEntry = Message<"hero.orders.v2.OrderUpdateEntry"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;

  /**
   * ISO8601 timestamp
   *
   * @generated from field: string timestamp = 2;
   */
  timestamp: string;

  /**
   * Re-use the shared UpdateSource enum from situation.proto.
   *
   * @generated from field: hero.situations.v2.UpdateSource update_source = 3;
   */
  updateSource: UpdateSource;
};

/**
 * Describes the message hero.orders.v2.OrderUpdateEntry.
 * Use `create(OrderUpdateEntrySchema)` to create a new message.
 */
export const OrderUpdateEntrySchema: GenMessage<OrderUpdateEntry> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 1);

/**
 * @generated from message hero.orders.v2.OrderStatusUpdateEntry
 */
export type OrderStatusUpdateEntry = Message<"hero.orders.v2.OrderStatusUpdateEntry"> & {
  /**
   * The time at which the status update occurred.
   *
   * ISO8601 timestamp
   *
   * @generated from field: string entry_timestamp = 1;
   */
  entryTimestamp: string;

  /**
   * The new status of the order.
   *
   * @generated from field: hero.orders.v2.OrderStatus new_status = 2;
   */
  newStatus: OrderStatus;

  /**
   * The previous status before this update.
   *
   * @generated from field: hero.orders.v2.OrderStatus previous_status = 3;
   */
  previousStatus: OrderStatus;

  /**
   * The new type-specific status of the order.
   *
   * @generated from field: string new_type_specific_status = 4;
   */
  newTypeSpecificStatus: string;

  /**
   * The previous type-specific status before this update.
   *
   * @generated from field: string previous_type_specific_status = 5;
   */
  previousTypeSpecificStatus: string;

  /**
   * (Optional) A note explaining the status change.
   *
   * @generated from field: string note = 6;
   */
  note: string;

  /**
   * (Optional) ID of the updater.
   *
   * @generated from field: string updater_id = 7;
   */
  updaterId: string;

  /**
   * (Optional) Source of the update.
   *
   * @generated from field: hero.situations.v2.UpdateSource update_source = 8;
   */
  updateSource: UpdateSource;

  /**
   * The time at which the status update was set to - defaults to the timestamp field unless manually overridden.
   *
   * @generated from field: string status_update_timestamp = 9;
   */
  statusUpdateTimestamp: string;
};

/**
 * Describes the message hero.orders.v2.OrderStatusUpdateEntry.
 * Use `create(OrderStatusUpdateEntrySchema)` to create a new message.
 */
export const OrderStatusUpdateEntrySchema: GenMessage<OrderStatusUpdateEntry> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 2);

/**
 * @generated from message hero.orders.v2.Order
 */
export type Order = Message<"hero.orders.v2.Order"> & {
  /**
   * Unique identifier for the order.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Reference to the associated situation.
   *
   * @generated from field: string situation_id = 2;
   */
  situationId: string;

  /**
   * Reference to the asset assigned to execute this order.
   *
   * @generated from field: string asset_id = 3;
   */
  assetId: string;

  /**
   * Specifies what type of order this is.
   *
   * @generated from field: hero.orders.v2.OrderType type = 4;
   */
  type: OrderType;

  /**
   * Indicates the current execution status of the order.
   *
   * @generated from field: hero.orders.v2.OrderStatus status = 5;
   */
  status: OrderStatus;

  /**
   * Detailed instructions for what the asset needs to do.
   *
   * @generated from field: string instructions = 6;
   */
  instructions: string;

  /**
   * Priority level of the order.
   *
   * @generated from field: int32 priority = 7;
   */
  priority: number;

  /**
   * JSON string (or any relevant format) containing order-specific parameters.
   *
   * @generated from field: string additional_info_json = 8;
   */
  additionalInfoJson: string;

  /**
   * Field to hold additional status information specific to the order type.
   *
   * @generated from field: string type_specific_status = 9;
   */
  typeSpecificStatus: string;

  /**
   * Free-form notes or comments regarding the order.
   *
   * @generated from field: string notes = 10;
   */
  notes: string;

  /**
   * Timestamp when the order was created.
   *
   * ISO8601 timestamp
   *
   * @generated from field: string create_time = 11;
   */
  createTime: string;

  /**
   * Timestamp when the order was last updated.
   *
   * ISO8601 timestamp
   *
   * @generated from field: string update_time = 12;
   */
  updateTime: string;

  /**
   * Timestamp when the order was completed (if applicable).
   *
   * ISO8601 timestamp
   *
   * @generated from field: string completion_time = 13;
   */
  completionTime: string;

  /**
   * A list of update entries to track changes and status transitions.
   *
   * @generated from field: repeated hero.orders.v2.OrderUpdateEntry updates = 14;
   */
  updates: OrderUpdateEntry[];

  /**
   * Timestamp when the order was actually assigned to an asset.
   *
   * ISO8601 timestamp
   *
   * @generated from field: string assigned_time = 15;
   */
  assignedTime: string;

  /**
   * Timestamp when the asset acknowledged the order.
   *
   * ISO8601 timestamp
   *
   * @generated from field: string acknowledged_time = 16;
   */
  acknowledgedTime: string;

  /**
   * Expected or estimated timestamp for order completion.
   *
   * ISO8601 timestamp
   *
   * @generated from field: string estimated_completion_time = 17;
   */
  estimatedCompletionTime: string;

  /**
   * Reason for cancellation or rejection of the order, if applicable.
   *
   * @generated from field: string cancellation_or_rejection_reason = 18;
   */
  cancellationOrRejectionReason: string;

  /**
   * Number of times the order has been retried.
   *
   * @generated from field: int32 retry_count = 19;
   */
  retryCount: number;

  /**
   * Indicates the source of order creation using the shared UpdateSource enum.
   *
   * @generated from field: hero.situations.v2.UpdateSource created_by = 20;
   */
  createdBy: UpdateSource;

  /**
   * A short descriptive title for the order.
   *
   * @generated from field: string title = 21;
   */
  title: string;

  /**
   * Indicates the type(s) of assets that can perform this order.
   *
   * @generated from field: repeated hero.assets.v2.AssetType allowed_asset_types = 23;
   */
  allowedAssetTypes: AssetType[];

  /**
   * Reason for snoozing the order.
   *
   * @generated from field: string snooze_reason = 24;
   */
  snoozeReason: string;

  /**
   * Timestamp until which the order is snoozed.
   *
   * ISO8601 timestamp
   *
   * @generated from field: string snooze_until = 25;
   */
  snoozeUntil: string;

  /**
   * Number of times the order has been snoozed.
   *
   * @generated from field: int32 snooze_count = 26;
   */
  snoozeCount: number;

  /**
   * List of asset IDs that should NOT be assigned to this order.
   *
   * @generated from field: repeated string blacklisted_asset_ids = 27;
   */
  blacklistedAssetIds: string[];

  /**
   * For orders it will be fixed value "ORDER".
   *
   * @generated from field: string resource_type = 28;
   */
  resourceType: string;

  /**
   * Permissions specifying which asset types can change status or reassign assets.
   *
   * @generated from field: hero.orders.v2.OrderPermissions permissions = 29;
   */
  permissions?: OrderPermissions;

  /**
   * Timeseries status updates for the order.
   *
   * @generated from field: repeated hero.orders.v2.OrderStatusUpdateEntry status_updates = 30;
   */
  statusUpdates: OrderStatusUpdateEntry[];

  /**
   * Reference this order to a report
   *
   * @generated from field: string report_id = 31;
   */
  reportId: string;

  /**
   * If this is a review order, point back at the ReviewRound
   *
   * @generated from field: string review_round_id = 32;
   */
  reviewRoundId: string;
};

/**
 * Describes the message hero.orders.v2.Order.
 * Use `create(OrderSchema)` to create a new message.
 */
export const OrderSchema: GenMessage<Order> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 3);

/**
 * ----------------------------------------------
 * Request/Response Messages
 * ----------------------------------------------
 *
 * @generated from message hero.orders.v2.CreateOrderRequest
 */
export type CreateOrderRequest = Message<"hero.orders.v2.CreateOrderRequest"> & {
  /**
   * Order to be created (id may be omitted and auto-generated by the system).
   *
   * @generated from field: hero.orders.v2.Order order = 1;
   */
  order?: Order;
};

/**
 * Describes the message hero.orders.v2.CreateOrderRequest.
 * Use `create(CreateOrderRequestSchema)` to create a new message.
 */
export const CreateOrderRequestSchema: GenMessage<CreateOrderRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 4);

/**
 * @generated from message hero.orders.v2.CreateOrderResponse
 */
export type CreateOrderResponse = Message<"hero.orders.v2.CreateOrderResponse"> & {
  /**
   * @generated from field: hero.orders.v2.Order order = 1;
   */
  order?: Order;
};

/**
 * Describes the message hero.orders.v2.CreateOrderResponse.
 * Use `create(CreateOrderResponseSchema)` to create a new message.
 */
export const CreateOrderResponseSchema: GenMessage<CreateOrderResponse> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 5);

/**
 * @generated from message hero.orders.v2.GetOrderRequest
 */
export type GetOrderRequest = Message<"hero.orders.v2.GetOrderRequest"> & {
  /**
   * ID of the order to retrieve.
   *
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.orders.v2.GetOrderRequest.
 * Use `create(GetOrderRequestSchema)` to create a new message.
 */
export const GetOrderRequestSchema: GenMessage<GetOrderRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 6);

/**
 * @generated from message hero.orders.v2.UpdateOrderRequest
 */
export type UpdateOrderRequest = Message<"hero.orders.v2.UpdateOrderRequest"> & {
  /**
   * The updated order object. Must contain the ID of the order to update.
   *
   * @generated from field: hero.orders.v2.Order order = 1;
   */
  order?: Order;
};

/**
 * Describes the message hero.orders.v2.UpdateOrderRequest.
 * Use `create(UpdateOrderRequestSchema)` to create a new message.
 */
export const UpdateOrderRequestSchema: GenMessage<UpdateOrderRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 7);

/**
 * Request message to list orders with pagination, filtering, and ordering options.
 *
 * @generated from message hero.orders.v2.ListOrdersRequest
 */
export type ListOrdersRequest = Message<"hero.orders.v2.ListOrdersRequest"> & {
  /**
   * Maximum number of orders to return in the response.
   *
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * A token identifying a specific page of results to retrieve.
   *
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  /**
   * Optional filter: Returns only orders matching the specified status.
   * A value of ORDER_STATUS_UNSPECIFIED indicates no filtering by status.
   *
   * @generated from field: hero.orders.v2.OrderStatus status = 3;
   */
  status: OrderStatus;

  /**
   * Optional filter: Returns only orders of the specified type.
   *
   * @generated from field: hero.orders.v2.OrderType type = 4;
   */
  type: OrderType;

  /**
   * Optional: Specifies the ordering of returned orders (e.g., "create_time desc").
   *
   * @generated from field: string order_by = 5;
   */
  orderBy: string;
};

/**
 * Describes the message hero.orders.v2.ListOrdersRequest.
 * Use `create(ListOrdersRequestSchema)` to create a new message.
 */
export const ListOrdersRequestSchema: GenMessage<ListOrdersRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 8);

/**
 * @generated from message hero.orders.v2.ListOrdersResponse
 */
export type ListOrdersResponse = Message<"hero.orders.v2.ListOrdersResponse"> & {
  /**
   * @generated from field: repeated hero.orders.v2.Order orders = 1;
   */
  orders: Order[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.orders.v2.ListOrdersResponse.
 * Use `create(ListOrdersResponseSchema)` to create a new message.
 */
export const ListOrdersResponseSchema: GenMessage<ListOrdersResponse> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 9);

/**
 * @generated from message hero.orders.v2.DeleteOrderRequest
 */
export type DeleteOrderRequest = Message<"hero.orders.v2.DeleteOrderRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.orders.v2.DeleteOrderRequest.
 * Use `create(DeleteOrderRequestSchema)` to create a new message.
 */
export const DeleteOrderRequestSchema: GenMessage<DeleteOrderRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 10);

/**
 * @generated from message hero.orders.v2.AddOrderUpdateRequest
 */
export type AddOrderUpdateRequest = Message<"hero.orders.v2.AddOrderUpdateRequest"> & {
  /**
   * The ID of the order to which we add an update.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * The new update entry.
   *
   * @generated from field: hero.orders.v2.OrderUpdateEntry update = 2;
   */
  update?: OrderUpdateEntry;
};

/**
 * Describes the message hero.orders.v2.AddOrderUpdateRequest.
 * Use `create(AddOrderUpdateRequestSchema)` to create a new message.
 */
export const AddOrderUpdateRequestSchema: GenMessage<AddOrderUpdateRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 11);

/**
 * @generated from message hero.orders.v2.RemoveOrderUpdateRequest
 */
export type RemoveOrderUpdateRequest = Message<"hero.orders.v2.RemoveOrderUpdateRequest"> & {
  /**
   * The ID of the order from which an update is removed.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * The update to remove; matching by fields such as message/timestamp.
   *
   * @generated from field: hero.orders.v2.OrderUpdateEntry update = 2;
   */
  update?: OrderUpdateEntry;
};

/**
 * Describes the message hero.orders.v2.RemoveOrderUpdateRequest.
 * Use `create(RemoveOrderUpdateRequestSchema)` to create a new message.
 */
export const RemoveOrderUpdateRequestSchema: GenMessage<RemoveOrderUpdateRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 12);

/**
 * New messages for managing allowed asset types.
 *
 * @generated from message hero.orders.v2.AddAllowedAssetTypeRequest
 */
export type AddAllowedAssetTypeRequest = Message<"hero.orders.v2.AddAllowedAssetTypeRequest"> & {
  /**
   * ID of the order to update.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Allowed asset type to add.
   *
   * @generated from field: hero.assets.v2.AssetType allowed_asset_type = 2;
   */
  allowedAssetType: AssetType;
};

/**
 * Describes the message hero.orders.v2.AddAllowedAssetTypeRequest.
 * Use `create(AddAllowedAssetTypeRequestSchema)` to create a new message.
 */
export const AddAllowedAssetTypeRequestSchema: GenMessage<AddAllowedAssetTypeRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 13);

/**
 * @generated from message hero.orders.v2.RemoveAllowedAssetTypeRequest
 */
export type RemoveAllowedAssetTypeRequest = Message<"hero.orders.v2.RemoveAllowedAssetTypeRequest"> & {
  /**
   * ID of the order to update.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Allowed asset type to remove.
   *
   * @generated from field: hero.assets.v2.AssetType allowed_asset_type = 2;
   */
  allowedAssetType: AssetType;
};

/**
 * Describes the message hero.orders.v2.RemoveAllowedAssetTypeRequest.
 * Use `create(RemoveAllowedAssetTypeRequestSchema)` to create a new message.
 */
export const RemoveAllowedAssetTypeRequestSchema: GenMessage<RemoveAllowedAssetTypeRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 14);

/**
 * New messages for managing blacklisted asset IDs.
 *
 * @generated from message hero.orders.v2.AddBlacklistedAssetIdRequest
 */
export type AddBlacklistedAssetIdRequest = Message<"hero.orders.v2.AddBlacklistedAssetIdRequest"> & {
  /**
   * ID of the order to update.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Asset ID to add to the blacklist.
   *
   * @generated from field: string asset_id = 2;
   */
  assetId: string;
};

/**
 * Describes the message hero.orders.v2.AddBlacklistedAssetIdRequest.
 * Use `create(AddBlacklistedAssetIdRequestSchema)` to create a new message.
 */
export const AddBlacklistedAssetIdRequestSchema: GenMessage<AddBlacklistedAssetIdRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 15);

/**
 * @generated from message hero.orders.v2.RemoveBlacklistedAssetIdRequest
 */
export type RemoveBlacklistedAssetIdRequest = Message<"hero.orders.v2.RemoveBlacklistedAssetIdRequest"> & {
  /**
   * ID of the order to update.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Asset ID to remove from the blacklist.
   *
   * @generated from field: string asset_id = 2;
   */
  assetId: string;
};

/**
 * Describes the message hero.orders.v2.RemoveBlacklistedAssetIdRequest.
 * Use `create(RemoveBlacklistedAssetIdRequestSchema)` to create a new message.
 */
export const RemoveBlacklistedAssetIdRequestSchema: GenMessage<RemoveBlacklistedAssetIdRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 16);

/**
 * Status transition requests
 *
 * @generated from message hero.orders.v2.AcknowledgeOrderRequest
 */
export type AcknowledgeOrderRequest = Message<"hero.orders.v2.AcknowledgeOrderRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.orders.v2.AcknowledgeOrderRequest.
 * Use `create(AcknowledgeOrderRequestSchema)` to create a new message.
 */
export const AcknowledgeOrderRequestSchema: GenMessage<AcknowledgeOrderRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 17);

/**
 * @generated from message hero.orders.v2.RejectOrderRequest
 */
export type RejectOrderRequest = Message<"hero.orders.v2.RejectOrderRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Optionally include a reason for rejection.
   *
   * @generated from field: string reason = 2;
   */
  reason: string;
};

/**
 * Describes the message hero.orders.v2.RejectOrderRequest.
 * Use `create(RejectOrderRequestSchema)` to create a new message.
 */
export const RejectOrderRequestSchema: GenMessage<RejectOrderRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 18);

/**
 * @generated from message hero.orders.v2.SnoozeOrderRequest
 */
export type SnoozeOrderRequest = Message<"hero.orders.v2.SnoozeOrderRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string snooze_reason = 2;
   */
  snoozeReason: string;

  /**
   * ISO8601 timestamp
   *
   * @generated from field: string snooze_until = 3;
   */
  snoozeUntil: string;
};

/**
 * Describes the message hero.orders.v2.SnoozeOrderRequest.
 * Use `create(SnoozeOrderRequestSchema)` to create a new message.
 */
export const SnoozeOrderRequestSchema: GenMessage<SnoozeOrderRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 19);

/**
 * @generated from message hero.orders.v2.CancelOrderRequest
 */
export type CancelOrderRequest = Message<"hero.orders.v2.CancelOrderRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string reason = 2;
   */
  reason: string;
};

/**
 * Describes the message hero.orders.v2.CancelOrderRequest.
 * Use `create(CancelOrderRequestSchema)` to create a new message.
 */
export const CancelOrderRequestSchema: GenMessage<CancelOrderRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 20);

/**
 * @generated from message hero.orders.v2.CompleteOrderRequest
 */
export type CompleteOrderRequest = Message<"hero.orders.v2.CompleteOrderRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.orders.v2.CompleteOrderRequest.
 * Use `create(CompleteOrderRequestSchema)` to create a new message.
 */
export const CompleteOrderRequestSchema: GenMessage<CompleteOrderRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 21);

/**
 * *
 * 1. List "Active Assigned" orders for a particular asset.
 *    This includes only orders with status in [CREATED, ACKNOWLEDGED, SNOOZED, or IN_PROGRESS].
 *
 * @generated from message hero.orders.v2.ListActiveAssignedOrdersForAssetRequest
 */
export type ListActiveAssignedOrdersForAssetRequest = Message<"hero.orders.v2.ListActiveAssignedOrdersForAssetRequest"> & {
  /**
   * Asset ID for which to retrieve active assigned orders.
   *
   * @generated from field: string asset_id = 1;
   */
  assetId: string;

  /**
   * Optional pagination.
   *
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.orders.v2.ListActiveAssignedOrdersForAssetRequest.
 * Use `create(ListActiveAssignedOrdersForAssetRequestSchema)` to create a new message.
 */
export const ListActiveAssignedOrdersForAssetRequestSchema: GenMessage<ListActiveAssignedOrdersForAssetRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 22);

/**
 * @generated from message hero.orders.v2.ListActiveAssignedOrdersForAssetResponse
 */
export type ListActiveAssignedOrdersForAssetResponse = Message<"hero.orders.v2.ListActiveAssignedOrdersForAssetResponse"> & {
  /**
   * @generated from field: repeated hero.orders.v2.Order orders = 1;
   */
  orders: Order[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.orders.v2.ListActiveAssignedOrdersForAssetResponse.
 * Use `create(ListActiveAssignedOrdersForAssetResponseSchema)` to create a new message.
 */
export const ListActiveAssignedOrdersForAssetResponseSchema: GenMessage<ListActiveAssignedOrdersForAssetResponse> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 23);

/**
 * *
 * 2. List orders that are in the CREATED status for a particular asset.
 *    Typically, this implies the asset_id is assigned to the order,
 *    but you can also choose how your service filters the data (e.g., created for no assigned asset yet).
 *
 * @generated from message hero.orders.v2.ListNewOrdersForAssetRequest
 */
export type ListNewOrdersForAssetRequest = Message<"hero.orders.v2.ListNewOrdersForAssetRequest"> & {
  /**
   * Asset ID for which to retrieve created orders.
   *
   * @generated from field: string asset_id = 1;
   */
  assetId: string;

  /**
   * Optional pagination.
   *
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.orders.v2.ListNewOrdersForAssetRequest.
 * Use `create(ListNewOrdersForAssetRequestSchema)` to create a new message.
 */
export const ListNewOrdersForAssetRequestSchema: GenMessage<ListNewOrdersForAssetRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 24);

/**
 * @generated from message hero.orders.v2.ListNewOrdersForAssetResponse
 */
export type ListNewOrdersForAssetResponse = Message<"hero.orders.v2.ListNewOrdersForAssetResponse"> & {
  /**
   * @generated from field: repeated hero.orders.v2.Order orders = 1;
   */
  orders: Order[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.orders.v2.ListNewOrdersForAssetResponse.
 * Use `create(ListNewOrdersForAssetResponseSchema)` to create a new message.
 */
export const ListNewOrdersForAssetResponseSchema: GenMessage<ListNewOrdersForAssetResponse> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 25);

/**
 * *
 * 3. List orders for an asset with an optional filter by OrderStatus.
 *
 * @generated from message hero.orders.v2.ListOrdersForAssetRequest
 */
export type ListOrdersForAssetRequest = Message<"hero.orders.v2.ListOrdersForAssetRequest"> & {
  /**
   * Asset ID for which to retrieve orders.
   *
   * @generated from field: string asset_id = 1;
   */
  assetId: string;

  /**
   * Optional pagination: number of orders to return.
   *
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * Optional pagination token.
   *
   * @generated from field: string page_token = 3;
   */
  pageToken: string;

  /**
   * Optional filter by order status.
   *
   * @generated from field: hero.orders.v2.OrderStatus status = 4;
   */
  status: OrderStatus;
};

/**
 * Describes the message hero.orders.v2.ListOrdersForAssetRequest.
 * Use `create(ListOrdersForAssetRequestSchema)` to create a new message.
 */
export const ListOrdersForAssetRequestSchema: GenMessage<ListOrdersForAssetRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 26);

/**
 * @generated from message hero.orders.v2.ListOrdersForAssetResponse
 */
export type ListOrdersForAssetResponse = Message<"hero.orders.v2.ListOrdersForAssetResponse"> & {
  /**
   * @generated from field: repeated hero.orders.v2.Order orders = 1;
   */
  orders: Order[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.orders.v2.ListOrdersForAssetResponse.
 * Use `create(ListOrdersForAssetResponseSchema)` to create a new message.
 */
export const ListOrdersForAssetResponseSchema: GenMessage<ListOrdersForAssetResponse> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 27);

/**
 * Modified listing request for orders in a situation.
 *
 * @generated from message hero.orders.v2.ListOrdersForSituationRequest
 */
export type ListOrdersForSituationRequest = Message<"hero.orders.v2.ListOrdersForSituationRequest"> & {
  /**
   * The ID of the situation for which to list orders.
   *
   * @generated from field: string situation_id = 1;
   */
  situationId: string;

  /**
   * Optional pagination: number of orders to return.
   *
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * Optional pagination token.
   *
   * @generated from field: string page_token = 3;
   */
  pageToken: string;

  /**
   * Optional filter by order status.
   *
   * @generated from field: hero.orders.v2.OrderStatus status = 4;
   */
  status: OrderStatus;
};

/**
 * Describes the message hero.orders.v2.ListOrdersForSituationRequest.
 * Use `create(ListOrdersForSituationRequestSchema)` to create a new message.
 */
export const ListOrdersForSituationRequestSchema: GenMessage<ListOrdersForSituationRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 28);

/**
 * @generated from message hero.orders.v2.ListOrdersForSituationResponse
 */
export type ListOrdersForSituationResponse = Message<"hero.orders.v2.ListOrdersForSituationResponse"> & {
  /**
   * The list of orders associated with the given situation.
   *
   * @generated from field: repeated hero.orders.v2.Order orders = 1;
   */
  orders: Order[];

  /**
   * Token to retrieve the next page of results, if any.
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.orders.v2.ListOrdersForSituationResponse.
 * Use `create(ListOrdersForSituationResponseSchema)` to create a new message.
 */
export const ListOrdersForSituationResponseSchema: GenMessage<ListOrdersForSituationResponse> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 29);

/**
 * Listing orders by report ID
 *
 * @generated from message hero.orders.v2.ListOrdersForReportRequest
 */
export type ListOrdersForReportRequest = Message<"hero.orders.v2.ListOrdersForReportRequest"> & {
  /**
   * The ID of the report for which to list orders.
   *
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * Optional pagination: number of orders to return.
   *
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * Optional pagination token.
   *
   * @generated from field: string page_token = 3;
   */
  pageToken: string;

  /**
   * Optional filter by order status.
   *
   * @generated from field: hero.orders.v2.OrderStatus status = 4;
   */
  status: OrderStatus;
};

/**
 * Describes the message hero.orders.v2.ListOrdersForReportRequest.
 * Use `create(ListOrdersForReportRequestSchema)` to create a new message.
 */
export const ListOrdersForReportRequestSchema: GenMessage<ListOrdersForReportRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 30);

/**
 * @generated from message hero.orders.v2.ListOrdersForReportResponse
 */
export type ListOrdersForReportResponse = Message<"hero.orders.v2.ListOrdersForReportResponse"> & {
  /**
   * The list of orders associated with the given report.
   *
   * @generated from field: repeated hero.orders.v2.Order orders = 1;
   */
  orders: Order[];

  /**
   * Token to retrieve the next page of results, if any.
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.orders.v2.ListOrdersForReportResponse.
 * Use `create(ListOrdersForReportResponseSchema)` to create a new message.
 */
export const ListOrdersForReportResponseSchema: GenMessage<ListOrdersForReportResponse> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 31);

/**
 * Listing orders by review round ID
 *
 * @generated from message hero.orders.v2.ListOrdersForReviewRoundRequest
 */
export type ListOrdersForReviewRoundRequest = Message<"hero.orders.v2.ListOrdersForReviewRoundRequest"> & {
  /**
   * The ID of the review round for which to list orders.
   *
   * @generated from field: string review_round_id = 1;
   */
  reviewRoundId: string;

  /**
   * Optional pagination: number of orders to return.
   *
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * Optional pagination token.
   *
   * @generated from field: string page_token = 3;
   */
  pageToken: string;

  /**
   * Optional filter by order status.
   *
   * @generated from field: hero.orders.v2.OrderStatus status = 4;
   */
  status: OrderStatus;
};

/**
 * Describes the message hero.orders.v2.ListOrdersForReviewRoundRequest.
 * Use `create(ListOrdersForReviewRoundRequestSchema)` to create a new message.
 */
export const ListOrdersForReviewRoundRequestSchema: GenMessage<ListOrdersForReviewRoundRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 32);

/**
 * @generated from message hero.orders.v2.ListOrdersForReviewRoundResponse
 */
export type ListOrdersForReviewRoundResponse = Message<"hero.orders.v2.ListOrdersForReviewRoundResponse"> & {
  /**
   * The list of orders associated with the given review round.
   *
   * @generated from field: repeated hero.orders.v2.Order orders = 1;
   */
  orders: Order[];

  /**
   * Token to retrieve the next page of results, if any.
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.orders.v2.ListOrdersForReviewRoundResponse.
 * Use `create(ListOrdersForReviewRoundResponseSchema)` to create a new message.
 */
export const ListOrdersForReviewRoundResponseSchema: GenMessage<ListOrdersForReviewRoundResponse> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 33);

/**
 * @generated from message hero.orders.v2.UpdateOrderPermissionsRequest
 */
export type UpdateOrderPermissionsRequest = Message<"hero.orders.v2.UpdateOrderPermissionsRequest"> & {
  /**
   * The ID of the order whose permissions are being updated.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * The new permissions object to overwrite existing permissions.
   * (Alternatively, you can define logic to merge, but typically you replace the entire field).
   *
   * @generated from field: hero.orders.v2.OrderPermissions permissions = 2;
   */
  permissions?: OrderPermissions;
};

/**
 * Describes the message hero.orders.v2.UpdateOrderPermissionsRequest.
 * Use `create(UpdateOrderPermissionsRequestSchema)` to create a new message.
 */
export const UpdateOrderPermissionsRequestSchema: GenMessage<UpdateOrderPermissionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 34);

/**
 * @generated from message hero.orders.v2.AddAdditionalInfoRequest
 */
export type AddAdditionalInfoRequest = Message<"hero.orders.v2.AddAdditionalInfoRequest"> & {
  /**
   * The ID of the order to update.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * A JSON string containing the additional info to be merged.
   *
   * @generated from field: string additional_info_json = 2;
   */
  additionalInfoJson: string;
};

/**
 * Describes the message hero.orders.v2.AddAdditionalInfoRequest.
 * Use `create(AddAdditionalInfoRequestSchema)` to create a new message.
 */
export const AddAdditionalInfoRequestSchema: GenMessage<AddAdditionalInfoRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 35);

/**
 * @generated from message hero.orders.v2.AddAdditionalInfoResponse
 */
export type AddAdditionalInfoResponse = Message<"hero.orders.v2.AddAdditionalInfoResponse"> & {
  /**
   * The id of the updated order 
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * The updated additional_info_json object.
   *
   * @generated from field: string additional_info_json = 2;
   */
  additionalInfoJson: string;
};

/**
 * Describes the message hero.orders.v2.AddAdditionalInfoResponse.
 * Use `create(AddAdditionalInfoResponseSchema)` to create a new message.
 */
export const AddAdditionalInfoResponseSchema: GenMessage<AddAdditionalInfoResponse> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 36);

/**
 * Request message to manually add a status update entry to an order.
 *
 * @generated from message hero.orders.v2.AddOrderStatusUpdateRequest
 */
export type AddOrderStatusUpdateRequest = Message<"hero.orders.v2.AddOrderStatusUpdateRequest"> & {
  /**
   * The ID of the order to which we add a status update.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * The new status update entry.
   *
   * @generated from field: hero.orders.v2.OrderStatusUpdateEntry status_update = 2;
   */
  statusUpdate?: OrderStatusUpdateEntry;
};

/**
 * Describes the message hero.orders.v2.AddOrderStatusUpdateRequest.
 * Use `create(AddOrderStatusUpdateRequestSchema)` to create a new message.
 */
export const AddOrderStatusUpdateRequestSchema: GenMessage<AddOrderStatusUpdateRequest> = /*@__PURE__*/
  messageDesc(file_hero_orders_v2_orders, 37);

/**
 * ----------------------------------------------
 * Enums
 * ----------------------------------------------
 *
 * @generated from enum hero.orders.v2.OrderStatus
 */
export enum OrderStatus {
  /**
   * Default unspecified order status.
   *
   * @generated from enum value: ORDER_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Order has been created.
   *
   * @generated from enum value: ORDER_STATUS_CREATED = 1;
   */
  CREATED = 1,

  /**
   * Asset has acknowledged the order.
   *
   * @generated from enum value: ORDER_STATUS_ACKNOWLEDGED = 2;
   */
  ACKNOWLEDGED = 2,

  /**
   * Order was rejected by the asset.
   *
   * @generated from enum value: ORDER_STATUS_REJECTED = 3;
   */
  REJECTED = 3,

  /**
   * Order has been snoozed (delayed).
   *
   * @generated from enum value: ORDER_STATUS_SNOOZED = 4;
   */
  SNOOZED = 4,

  /**
   * Order is actively being worked on.
   *
   * @generated from enum value: ORDER_STATUS_IN_PROGRESS = 5;
   */
  IN_PROGRESS = 5,

  /**
   * Order has been completed.
   *
   * @generated from enum value: ORDER_STATUS_COMPLETED = 6;
   */
  COMPLETED = 6,

  /**
   * Order has been cancelled.
   *
   * @generated from enum value: ORDER_STATUS_CANCELLED = 7;
   */
  CANCELLED = 7,
}

/**
 * Describes the enum hero.orders.v2.OrderStatus.
 */
export const OrderStatusSchema: GenEnum<OrderStatus> = /*@__PURE__*/
  enumDesc(file_hero_orders_v2_orders, 0);

/**
 * @generated from enum hero.orders.v2.OrderType
 */
export enum OrderType {
  /**
   * Default unspecified order type.
   *
   * @generated from enum value: ORDER_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Order for triaging a situation reported by a member.
   *
   * @generated from enum value: ORDER_TYPE_TRIAGE_MEMBER_REPORT = 1;
   */
  TRIAGE_MEMBER_REPORT = 1,

  /**
   * Order for assisting a member.
   *
   * @generated from enum value: ORDER_TYPE_ASSIST_MEMBER = 2;
   */
  ASSIST_MEMBER = 2,

  /**
   * Order to submit a final report.
   *
   * @generated from enum value: ORDER_TYPE_SUBMIT_FINAL_REPORT = 3;
   */
  SUBMIT_FINAL_REPORT = 3,

  /**
   * Order to assign an agent to a task.
   *
   * @generated from enum value: ORDER_TYPE_ASSIGN_AGENT = 4;
   */
  ASSIGN_AGENT = 4,

  /**
   * Order for triaging a situation reported by an agent.
   *
   * @generated from enum value: ORDER_TYPE_TRIAGE_AGENT_REPORT = 5;
   */
  TRIAGE_AGENT_REPORT = 5,

  /**
   * Order for triaging an incident captured by a camera.
   *
   * @generated from enum value: ORDER_TYPE_TRIAGE_CAMERA_INCIDENT = 6;
   */
  TRIAGE_CAMERA_INCIDENT = 6,

  /**
   * Order for writing a report.
   *
   * @generated from enum value: ORDER_TYPE_WRITE_REPORT = 7;
   */
  WRITE_REPORT = 7,

  /**
   * Order for reviewing a report.
   *
   * @generated from enum value: ORDER_TYPE_REVIEW_REPORT = 8;
   */
  REVIEW_REPORT = 8,

  /**
   * Order for revising a report.
   *
   * @generated from enum value: ORDER_TYPE_REVISE_REPORT = 9;
   */
  REVISE_REPORT = 9,
}

/**
 * Describes the enum hero.orders.v2.OrderType.
 */
export const OrderTypeSchema: GenEnum<OrderType> = /*@__PURE__*/
  enumDesc(file_hero_orders_v2_orders, 1);

/**
 * ----------------------------------------------
 * Service Definition
 * ----------------------------------------------
 *
 * @generated from service hero.orders.v2.OrderService
 */
export const OrderService: GenService<{
  /**
   * Create a new order.
   *
   * @generated from rpc hero.orders.v2.OrderService.CreateOrder
   */
  createOrder: {
    methodKind: "unary";
    input: typeof CreateOrderRequestSchema;
    output: typeof CreateOrderResponseSchema;
  },
  /**
   * Retrieve an order by its ID.
   *
   * @generated from rpc hero.orders.v2.OrderService.GetOrder
   */
  getOrder: {
    methodKind: "unary";
    input: typeof GetOrderRequestSchema;
    output: typeof OrderSchema;
  },
  /**
   * Update an existing order.
   *
   * @generated from rpc hero.orders.v2.OrderService.UpdateOrder
   */
  updateOrder: {
    methodKind: "unary";
    input: typeof UpdateOrderRequestSchema;
    output: typeof OrderSchema;
  },
  /**
   * List orders with optional filtering, ordering, and pagination.
   *
   * @generated from rpc hero.orders.v2.OrderService.ListOrders
   */
  listOrders: {
    methodKind: "unary";
    input: typeof ListOrdersRequestSchema;
    output: typeof ListOrdersResponseSchema;
  },
  /**
   * Delete an order by its ID.
   *
   * @generated from rpc hero.orders.v2.OrderService.DeleteOrder
   */
  deleteOrder: {
    methodKind: "unary";
    input: typeof DeleteOrderRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * Add an update entry to an existing order.
   *
   * @generated from rpc hero.orders.v2.OrderService.AddOrderUpdate
   */
  addOrderUpdate: {
    methodKind: "unary";
    input: typeof AddOrderUpdateRequestSchema;
    output: typeof OrderSchema;
  },
  /**
   * Remove an update entry from an existing order.
   *
   * @generated from rpc hero.orders.v2.OrderService.RemoveOrderUpdate
   */
  removeOrderUpdate: {
    methodKind: "unary";
    input: typeof RemoveOrderUpdateRequestSchema;
    output: typeof OrderSchema;
  },
  /**
   * Add an allowed asset type to an order.
   *
   * @generated from rpc hero.orders.v2.OrderService.AddAllowedAssetType
   */
  addAllowedAssetType: {
    methodKind: "unary";
    input: typeof AddAllowedAssetTypeRequestSchema;
    output: typeof OrderSchema;
  },
  /**
   * Remove an allowed asset type from an order.
   *
   * @generated from rpc hero.orders.v2.OrderService.RemoveAllowedAssetType
   */
  removeAllowedAssetType: {
    methodKind: "unary";
    input: typeof RemoveAllowedAssetTypeRequestSchema;
    output: typeof OrderSchema;
  },
  /**
   * Add a blacklisted asset ID to an order.
   *
   * @generated from rpc hero.orders.v2.OrderService.AddBlacklistedAssetId
   */
  addBlacklistedAssetId: {
    methodKind: "unary";
    input: typeof AddBlacklistedAssetIdRequestSchema;
    output: typeof OrderSchema;
  },
  /**
   * Remove a blacklisted asset ID from an order.
   *
   * @generated from rpc hero.orders.v2.OrderService.RemoveBlacklistedAssetId
   */
  removeBlacklistedAssetId: {
    methodKind: "unary";
    input: typeof RemoveBlacklistedAssetIdRequestSchema;
    output: typeof OrderSchema;
  },
  /**
   * Acknowledge an order.
   *
   * @generated from rpc hero.orders.v2.OrderService.AcknowledgeOrder
   */
  acknowledgeOrder: {
    methodKind: "unary";
    input: typeof AcknowledgeOrderRequestSchema;
    output: typeof OrderSchema;
  },
  /**
   * Reject an order.
   *
   * @generated from rpc hero.orders.v2.OrderService.RejectOrder
   */
  rejectOrder: {
    methodKind: "unary";
    input: typeof RejectOrderRequestSchema;
    output: typeof OrderSchema;
  },
  /**
   * Snooze an order.
   *
   * @generated from rpc hero.orders.v2.OrderService.SnoozeOrder
   */
  snoozeOrder: {
    methodKind: "unary";
    input: typeof SnoozeOrderRequestSchema;
    output: typeof OrderSchema;
  },
  /**
   * Cancel an order.
   *
   * @generated from rpc hero.orders.v2.OrderService.CancelOrder
   */
  cancelOrder: {
    methodKind: "unary";
    input: typeof CancelOrderRequestSchema;
    output: typeof OrderSchema;
  },
  /**
   * Complete an order.
   *
   * @generated from rpc hero.orders.v2.OrderService.CompleteOrder
   */
  completeOrder: {
    methodKind: "unary";
    input: typeof CompleteOrderRequestSchema;
    output: typeof OrderSchema;
  },
  /**
   * ListActiveAssignedOrdersForAsset returns orders for the given asset that are in statuses: CREATED, ACKNOWLEDGED, SNOOZED, or IN_PROGRESS.
   *
   * @generated from rpc hero.orders.v2.OrderService.ListActiveAssignedOrdersForAsset
   */
  listActiveAssignedOrdersForAsset: {
    methodKind: "unary";
    input: typeof ListActiveAssignedOrdersForAssetRequestSchema;
    output: typeof ListActiveAssignedOrdersForAssetResponseSchema;
  },
  /**
   * ListNewOrdersForAsset returns orders in CREATED status for the given asset.
   *
   * @generated from rpc hero.orders.v2.OrderService.ListNewOrdersForAsset
   */
  listNewOrdersForAsset: {
    methodKind: "unary";
    input: typeof ListNewOrdersForAssetRequestSchema;
    output: typeof ListNewOrdersForAssetResponseSchema;
  },
  /**
   * UpdateOrderPermissions updates the permissions for an existing order.
   *
   * @generated from rpc hero.orders.v2.OrderService.UpdateOrderPermissions
   */
  updateOrderPermissions: {
    methodKind: "unary";
    input: typeof UpdateOrderPermissionsRequestSchema;
    output: typeof OrderSchema;
  },
  /**
   * ListOrdersForSituation returns a paginated list of orders for the given situation with optional status filter.
   *
   * @generated from rpc hero.orders.v2.OrderService.ListOrdersForSituation
   */
  listOrdersForSituation: {
    methodKind: "unary";
    input: typeof ListOrdersForSituationRequestSchema;
    output: typeof ListOrdersForSituationResponseSchema;
  },
  /**
   * ListOrdersForAsset returns a paginated list of orders for the given asset with optional status filter.
   *
   * @generated from rpc hero.orders.v2.OrderService.ListOrdersForAsset
   */
  listOrdersForAsset: {
    methodKind: "unary";
    input: typeof ListOrdersForAssetRequestSchema;
    output: typeof ListOrdersForAssetResponseSchema;
  },
  /**
   * ListOrdersForReport returns orders for the given report with optional status filter.
   *
   * @generated from rpc hero.orders.v2.OrderService.ListOrdersForReport
   */
  listOrdersForReport: {
    methodKind: "unary";
    input: typeof ListOrdersForReportRequestSchema;
    output: typeof ListOrdersForReportResponseSchema;
  },
  /**
   * ListOrdersForReviewRound returns orders for the given review round with optional status filter.
   *
   * @generated from rpc hero.orders.v2.OrderService.ListOrdersForReviewRound
   */
  listOrdersForReviewRound: {
    methodKind: "unary";
    input: typeof ListOrdersForReviewRoundRequestSchema;
    output: typeof ListOrdersForReviewRoundResponseSchema;
  },
  /**
   * Add additional info to an order by merging provided JSON into the existing additional_info_json.
   *
   * @generated from rpc hero.orders.v2.OrderService.AddAdditionalInfo
   */
  addAdditionalInfo: {
    methodKind: "unary";
    input: typeof AddAdditionalInfoRequestSchema;
    output: typeof AddAdditionalInfoResponseSchema;
  },
  /**
   * Add a status update to an order.
   *
   * @generated from rpc hero.orders.v2.OrderService.AddOrderStatusUpdate
   */
  addOrderStatusUpdate: {
    methodKind: "unary";
    input: typeof AddOrderStatusUpdateRequestSchema;
    output: typeof OrderSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_orders_v2_orders, 0);

