// @generated by protoc-gen-es v2.6.1 with parameter "target=ts"
// @generated from file hero/reports/v1/reports.proto (package hero.reports.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { EmptySchema, Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_empty, file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/reports/v1/reports.proto.
 */
export const file_hero_reports_v1_reports: GenFile = /*@__PURE__*/
  fileDesc("Ch1oZXJvL3JlcG9ydHMvdjEvcmVwb3J0cy5wcm90bxIPaGVyby5yZXBvcnRzLnYxIuABCgtIZWFkZXJGbGFncxIZChFqdXZlbmlsZV9pbnZvbHZlZBgBIAEoCBIUCgxjb25maWRlbnRpYWwYAiABKAgSIQoZcHJvcGVydHlfZXZpZGVuY2VfYm9va2luZxgDIAEoCBIOCgZwaG90b3MYBCABKAgSDgoGcHJpbnRzGAUgASgIEg8KB2FsY29ob2wYBiABKAgSCgoCY3IYByABKAgSCgoCaXIYCCABKAgSCwoDY2FzGAkgASgIEgoKAm1wGAogASgIEg8KB3dfYW5kX2kYCyABKAgSCgoCd2EYDCABKAgivQEKB1ZlaGljbGUSCgoCaWQYASABKAkSEQoJcmVwb3J0X2lkGAIgAygJEh4KEWludm9sdmVkX3BhcnR5X2lkGAMgASgJSACIAQESDQoFcGxhdGUYBCABKAkSDAoEeWVhchgFIAEoBRIMCgRtYWtlGAYgASgJEg0KBW1vZGVsGAcgASgJEg0KBWNvbG9yGAggASgJEhQKDHZlaGljbGVfdHlwZRgJIAEoCUIUChJfaW52b2x2ZWRfcGFydHlfaWQitgUKDUludm9sdmVkUGFydHkSLwoLcGFydHlfdHlwZXMYASADKA4yGi5oZXJvLnJlcG9ydHMudjEuUGFydHlUeXBlEgwKBG5hbWUYAiABKAkSCwoDYWdlGAMgASgFEicKA2RvYhgEIAEoCzIaLmdvb2dsZS5wcm90b2J1Zi5UaW1lc3RhbXASDwoHY291bnRyeRgFIAEoBRIYChB1bml2X2Fzc29jaWF0aW9uGAYgASgJEg8KB2FkZHJlc3MYByABKAkSFQoNcHJpbWFyeV9waG9uZRgIIAEoCRIXCg9zZWNvbmRhcnlfcGhvbmUYCSABKAkSCwoDc2V4GAogASgJEgwKBHJhY2UYCyABKAkSFwoPZHJpdmVyc19saWNlbnNlGAwgASgJEg4KBmhlaWdodBgNIAEoAhIOCgZ3ZWlnaHQYDiABKAISDAoEaGFpchgPIAEoCRINCgVidWlsZBgQIAEoCRIMCgRleWVzGBEgASgJEhIKCmNvbXBsZXhpb24YEiABKAkSCgoCbW8YEyABKAgSDwoHbW9fdGV4dBgUIAEoCRIWCg5wb2ludF9vZl9lbnRyeRgVIAEoCBIbChNwb2ludF9vZl9lbnRyeV90ZXh0GBYgASgJEhMKC21ldGhvZF91c2VkGBcgASgIEhgKEG1ldGhvZF91c2VkX3RleHQYGCABKAkSDwoHd2VhcG9ucxgZIAEoCBIUCgx3ZWFwb25zX3RleHQYGiABKAkSGwoTc2NhcnNfbWFya3NfdGF0dG9vcxgbIAEoCBIgChhzY2Fyc19tYXJrc190YXR0b29zX3RleHQYHCABKAkSEgoKb3RoZXJfaW5mbxgdIAEoCBIXCg9vdGhlcl9pbmZvX3RleHQYHiABKAkSCgoCaWQYHyABKAkSEQoJcmVwb3J0X2lkGCAgAygJIm8KDFByb3BlcnR5SXRlbRIMCgRpdGVtGAEgASgJEg4KBnN0YXR1cxgCIAEoCRITCgtkZXNjcmlwdGlvbhgDIAEoCRINCgV2YWx1ZRgEIAEoAhIKCgJpZBgFIAEoCRIRCglyZXBvcnRfaWQYBiADKAkizwYKBlJlcG9ydBIKCgJpZBgBIAEoCRINCgV0aXRsZRgCIAEoCRITCgtkZXNjcmlwdGlvbhgDIAEoCRItCgZzdGF0dXMYBCABKA4yHS5oZXJvLnJlcG9ydHMudjEuUmVwb3J0U3RhdHVzEhQKDHNpdHVhdGlvbl9pZBgFIAEoCRIvCgtjcmVhdGVfdGltZRgGIAEoCzIaLmdvb2dsZS5wcm90b2J1Zi5UaW1lc3RhbXASLwoLdXBkYXRlX3RpbWUYByABKAsyGi5nb29nbGUucHJvdG9idWYuVGltZXN0YW1wEjIKDGhlYWRlcl9mbGFncxgIIAEoCzIcLmhlcm8ucmVwb3J0cy52MS5IZWFkZXJGbGFncxIyCg5jYWxsX3RpbWVzdGFtcBgJIAEoCzIaLmdvb2dsZS5wcm90b2J1Zi5UaW1lc3RhbXASHgoWbG9jYXRpb25fb2Zfb2NjdXJyZW5jZRgKIAEoCRIaChJsb2NhdGlvbl9vbl9jYW1wdXMYCyABKAkSFQoNcmVwb3J0X251bWJlchgMIAEoCRIYChBjcmltZV9kZWZpbml0aW9uGA0gASgJEhQKDGNvZGVfc2VjdGlvbhgOIAEoCRIOCgZzb3VyY2UYDyABKAkSFAoMZXZlbnRfbnVtYmVyGBAgASgJEhwKFGNvbm5lY3RlZF9yZXBvcnRzX2lkGBEgAygJEg4KBmFnZW5jeRgSIAEoCRITCgtvY2N1cnJlZF9vbhgTIAEoCRITCgtkYXlfb2Zfd2VlaxgUIAEoCRIVCg10aW1lX29jY3VycmVkGBUgASgJEhAKCGxhdF9sb25nGBYgASgJEg8KB3N1bW1hcnkYFyABKAkSEwoLY2FzZV9zdGF0dXMYGCADKAkSFwoPbm90aWZpY2F0aW9uX3RvGBkgAygJEhQKDGRhdGVfd3JpdHRlbhgaIAEoCRIUCgx0aW1lX3dyaXR0ZW4YGyABKAkSGQoRbm90aWZpY2F0aW9uX3RleHQYHCABKAkSFgoObmFycmF0aXZlX2JvZHkYHSABKAkSGAoQaW52b2x2ZWRfcGFydGllcxgeIAMoCRISCgpwcm9wZXJ0aWVzGB8gAygJEhAKCHZlaGljbGVzGCAgAygJIj4KE0NyZWF0ZVJlcG9ydFJlcXVlc3QSJwoGcmVwb3J0GAEgASgLMhcuaGVyby5yZXBvcnRzLnYxLlJlcG9ydCI/ChRDcmVhdGVSZXBvcnRSZXNwb25zZRInCgZyZXBvcnQYASABKAsyFy5oZXJvLnJlcG9ydHMudjEuUmVwb3J0Ih4KEEdldFJlcG9ydFJlcXVlc3QSCgoCaWQYASABKAkiPgoTVXBkYXRlUmVwb3J0UmVxdWVzdBInCgZyZXBvcnQYASABKAsyFy5oZXJvLnJlcG9ydHMudjEuUmVwb3J0IjsKEkxpc3RSZXBvcnRzUmVxdWVzdBIRCglwYWdlX3NpemUYASABKAUSEgoKcGFnZV90b2tlbhgCIAEoCSJYChNMaXN0UmVwb3J0c1Jlc3BvbnNlEigKB3JlcG9ydHMYASADKAsyFy5oZXJvLnJlcG9ydHMudjEuUmVwb3J0EhcKD25leHRfcGFnZV90b2tlbhgCIAEoCSIhChNEZWxldGVSZXBvcnRSZXF1ZXN0EgoKAmlkGAEgASgJIkkKHUdldFJlcG9ydEJ5U2l0dWF0aW9uSWRSZXF1ZXN0EhQKDHNpdHVhdGlvbl9pZBgBIAEoCRISCgpwYWdlX3Rva2VuGAIgASgJImMKHkdldFJlcG9ydEJ5U2l0dWF0aW9uSWRSZXNwb25zZRIoCgdyZXBvcnRzGAEgAygLMhcuaGVyby5yZXBvcnRzLnYxLlJlcG9ydBIXCg9uZXh0X3BhZ2VfdG9rZW4YAiABKAkiQQoUQ3JlYXRlVmVoaWNsZVJlcXVlc3QSKQoHdmVoaWNsZRgBIAEoCzIYLmhlcm8ucmVwb3J0cy52MS5WZWhpY2xlIkIKFUNyZWF0ZVZlaGljbGVSZXNwb25zZRIpCgd2ZWhpY2xlGAEgASgLMhguaGVyby5yZXBvcnRzLnYxLlZlaGljbGUiHwoRR2V0VmVoaWNsZVJlcXVlc3QSCgoCaWQYASABKAkiQQoUVXBkYXRlVmVoaWNsZVJlcXVlc3QSKQoHdmVoaWNsZRgBIAEoCzIYLmhlcm8ucmVwb3J0cy52MS5WZWhpY2xlIiIKFERlbGV0ZVZlaGljbGVSZXF1ZXN0EgoKAmlkGAEgASgJIjwKE0xpc3RWZWhpY2xlc1JlcXVlc3QSEQoJcGFnZV9zaXplGAEgASgFEhIKCnBhZ2VfdG9rZW4YAiABKAkiWwoUTGlzdFZlaGljbGVzUmVzcG9uc2USKgoIdmVoaWNsZXMYASADKAsyGC5oZXJvLnJlcG9ydHMudjEuVmVoaWNsZRIXCg9uZXh0X3BhZ2VfdG9rZW4YAiABKAkiVAoaQ3JlYXRlSW52b2x2ZWRQYXJ0eVJlcXVlc3QSNgoOaW52b2x2ZWRfcGFydHkYASABKAsyHi5oZXJvLnJlcG9ydHMudjEuSW52b2x2ZWRQYXJ0eSJVChtDcmVhdGVJbnZvbHZlZFBhcnR5UmVzcG9uc2USNgoOaW52b2x2ZWRfcGFydHkYASABKAsyHi5oZXJvLnJlcG9ydHMudjEuSW52b2x2ZWRQYXJ0eSIlChdHZXRJbnZvbHZlZFBhcnR5UmVxdWVzdBIKCgJpZBgBIAEoCSJUChpVcGRhdGVJbnZvbHZlZFBhcnR5UmVxdWVzdBI2Cg5pbnZvbHZlZF9wYXJ0eRgBIAEoCzIeLmhlcm8ucmVwb3J0cy52MS5JbnZvbHZlZFBhcnR5IigKGkRlbGV0ZUludm9sdmVkUGFydHlSZXF1ZXN0EgoKAmlkGAEgASgJIkMKGkxpc3RJbnZvbHZlZFBhcnRpZXNSZXF1ZXN0EhEKCXBhZ2Vfc2l6ZRgBIAEoBRISCgpwYWdlX3Rva2VuGAIgASgJInAKG0xpc3RJbnZvbHZlZFBhcnRpZXNSZXNwb25zZRI4ChBpbnZvbHZlZF9wYXJ0aWVzGAEgAygLMh4uaGVyby5yZXBvcnRzLnYxLkludm9sdmVkUGFydHkSFwoPbmV4dF9wYWdlX3Rva2VuGAIgASgJIlEKGUNyZWF0ZVByb3BlcnR5SXRlbVJlcXVlc3QSNAoNcHJvcGVydHlfaXRlbRgBIAEoCzIdLmhlcm8ucmVwb3J0cy52MS5Qcm9wZXJ0eUl0ZW0iUgoaQ3JlYXRlUHJvcGVydHlJdGVtUmVzcG9uc2USNAoNcHJvcGVydHlfaXRlbRgBIAEoCzIdLmhlcm8ucmVwb3J0cy52MS5Qcm9wZXJ0eUl0ZW0iJAoWR2V0UHJvcGVydHlJdGVtUmVxdWVzdBIKCgJpZBgBIAEoCSJRChlVcGRhdGVQcm9wZXJ0eUl0ZW1SZXF1ZXN0EjQKDXByb3BlcnR5X2l0ZW0YASABKAsyHS5oZXJvLnJlcG9ydHMudjEuUHJvcGVydHlJdGVtIicKGURlbGV0ZVByb3BlcnR5SXRlbVJlcXVlc3QSCgoCaWQYASABKAkiQQoYTGlzdFByb3BlcnR5SXRlbXNSZXF1ZXN0EhEKCXBhZ2Vfc2l6ZRgBIAEoBRISCgpwYWdlX3Rva2VuGAIgASgJImsKGUxpc3RQcm9wZXJ0eUl0ZW1zUmVzcG9uc2USNQoOcHJvcGVydHlfaXRlbXMYASADKAsyHS5oZXJvLnJlcG9ydHMudjEuUHJvcGVydHlJdGVtEhcKD25leHRfcGFnZV90b2tlbhgCIAEoCSqDAQoMUmVwb3J0U3RhdHVzEg8KC1VOU1BFQ0lGSUVEEAASCwoHQ1JFQVRFRBABEgkKBURSQUZUEAISDQoJSU5fUkVWSUVXEAMSDQoJU1VCTUlUVEVEEAQSDwoLRURJVF9ORUVERUQQBRINCglDT01QTEVURUQQBhIMCghBQ0NFUFRFRBAHKmYKCVBhcnR5VHlwZRIaChZQQVJUWV9UWVBFX1VOU1BFQ0lGSUVEEAASCgoGVklDVElNEAESCwoHU1VTUEVDVBACEgsKB1dJVE5FU1MQAxIMCghSRVBPUlRFUhAEEgkKBU9USEVSEAUypwQKDVJlcG9ydFNlcnZpY2USWwoMQ3JlYXRlUmVwb3J0EiQuaGVyby5yZXBvcnRzLnYxLkNyZWF0ZVJlcG9ydFJlcXVlc3QaJS5oZXJvLnJlcG9ydHMudjEuQ3JlYXRlUmVwb3J0UmVzcG9uc2USRwoJR2V0UmVwb3J0EiEuaGVyby5yZXBvcnRzLnYxLkdldFJlcG9ydFJlcXVlc3QaFy5oZXJvLnJlcG9ydHMudjEuUmVwb3J0Ek0KDFVwZGF0ZVJlcG9ydBIkLmhlcm8ucmVwb3J0cy52MS5VcGRhdGVSZXBvcnRSZXF1ZXN0GhcuaGVyby5yZXBvcnRzLnYxLlJlcG9ydBJYCgtMaXN0UmVwb3J0cxIjLmhlcm8ucmVwb3J0cy52MS5MaXN0UmVwb3J0c1JlcXVlc3QaJC5oZXJvLnJlcG9ydHMudjEuTGlzdFJlcG9ydHNSZXNwb25zZRJMCgxEZWxldGVSZXBvcnQSJC5oZXJvLnJlcG9ydHMudjEuRGVsZXRlUmVwb3J0UmVxdWVzdBoWLmdvb2dsZS5wcm90b2J1Zi5FbXB0eRJ5ChZHZXRSZXBvcnRCeVNpdHVhdGlvbklkEi4uaGVyby5yZXBvcnRzLnYxLkdldFJlcG9ydEJ5U2l0dWF0aW9uSWRSZXF1ZXN0Gi8uaGVyby5yZXBvcnRzLnYxLkdldFJlcG9ydEJ5U2l0dWF0aW9uSWRSZXNwb25zZTK7AwoOVmVoaWNsZVNlcnZpY2USXgoNQ3JlYXRlVmVoaWNsZRIlLmhlcm8ucmVwb3J0cy52MS5DcmVhdGVWZWhpY2xlUmVxdWVzdBomLmhlcm8ucmVwb3J0cy52MS5DcmVhdGVWZWhpY2xlUmVzcG9uc2USSgoKR2V0VmVoaWNsZRIiLmhlcm8ucmVwb3J0cy52MS5HZXRWZWhpY2xlUmVxdWVzdBoYLmhlcm8ucmVwb3J0cy52MS5WZWhpY2xlElAKDVVwZGF0ZVZlaGljbGUSJS5oZXJvLnJlcG9ydHMudjEuVXBkYXRlVmVoaWNsZVJlcXVlc3QaGC5oZXJvLnJlcG9ydHMudjEuVmVoaWNsZRJbCgxMaXN0VmVoaWNsZXMSJC5oZXJvLnJlcG9ydHMudjEuTGlzdFZlaGljbGVzUmVxdWVzdBolLmhlcm8ucmVwb3J0cy52MS5MaXN0VmVoaWNsZXNSZXNwb25zZRJOCg1EZWxldGVWZWhpY2xlEiUuaGVyby5yZXBvcnRzLnYxLkRlbGV0ZVZlaGljbGVSZXF1ZXN0GhYuZ29vZ2xlLnByb3RvYnVmLkVtcHR5MpgEChRJbnZvbHZlZFBhcnR5U2VydmljZRJwChNDcmVhdGVJbnZvbHZlZFBhcnR5EisuaGVyby5yZXBvcnRzLnYxLkNyZWF0ZUludm9sdmVkUGFydHlSZXF1ZXN0GiwuaGVyby5yZXBvcnRzLnYxLkNyZWF0ZUludm9sdmVkUGFydHlSZXNwb25zZRJcChBHZXRJbnZvbHZlZFBhcnR5EiguaGVyby5yZXBvcnRzLnYxLkdldEludm9sdmVkUGFydHlSZXF1ZXN0Gh4uaGVyby5yZXBvcnRzLnYxLkludm9sdmVkUGFydHkSYgoTVXBkYXRlSW52b2x2ZWRQYXJ0eRIrLmhlcm8ucmVwb3J0cy52MS5VcGRhdGVJbnZvbHZlZFBhcnR5UmVxdWVzdBoeLmhlcm8ucmVwb3J0cy52MS5JbnZvbHZlZFBhcnR5EnAKE0xpc3RJbnZvbHZlZFBhcnRpZXMSKy5oZXJvLnJlcG9ydHMudjEuTGlzdEludm9sdmVkUGFydGllc1JlcXVlc3QaLC5oZXJvLnJlcG9ydHMudjEuTGlzdEludm9sdmVkUGFydGllc1Jlc3BvbnNlEloKE0RlbGV0ZUludm9sdmVkUGFydHkSKy5oZXJvLnJlcG9ydHMudjEuRGVsZXRlSW52b2x2ZWRQYXJ0eVJlcXVlc3QaFi5nb29nbGUucHJvdG9idWYuRW1wdHkyhgQKE1Byb3BlcnR5SXRlbVNlcnZpY2USbQoSQ3JlYXRlUHJvcGVydHlJdGVtEiouaGVyby5yZXBvcnRzLnYxLkNyZWF0ZVByb3BlcnR5SXRlbVJlcXVlc3QaKy5oZXJvLnJlcG9ydHMudjEuQ3JlYXRlUHJvcGVydHlJdGVtUmVzcG9uc2USWQoPR2V0UHJvcGVydHlJdGVtEicuaGVyby5yZXBvcnRzLnYxLkdldFByb3BlcnR5SXRlbVJlcXVlc3QaHS5oZXJvLnJlcG9ydHMudjEuUHJvcGVydHlJdGVtEl8KElVwZGF0ZVByb3BlcnR5SXRlbRIqLmhlcm8ucmVwb3J0cy52MS5VcGRhdGVQcm9wZXJ0eUl0ZW1SZXF1ZXN0Gh0uaGVyby5yZXBvcnRzLnYxLlByb3BlcnR5SXRlbRJqChFMaXN0UHJvcGVydHlJdGVtcxIpLmhlcm8ucmVwb3J0cy52MS5MaXN0UHJvcGVydHlJdGVtc1JlcXVlc3QaKi5oZXJvLnJlcG9ydHMudjEuTGlzdFByb3BlcnR5SXRlbXNSZXNwb25zZRJYChJEZWxldGVQcm9wZXJ0eUl0ZW0SKi5oZXJvLnJlcG9ydHMudjEuRGVsZXRlUHJvcGVydHlJdGVtUmVxdWVzdBoWLmdvb2dsZS5wcm90b2J1Zi5FbXB0eUIfWh1wcm90by9oZXJvL3JlcG9ydHMvdjE7cmVwb3J0c2IGcHJvdG8z", [file_google_protobuf_timestamp, file_google_protobuf_empty]);

/**
 * Message for header flags (checkbox fields)
 *
 * @generated from message hero.reports.v1.HeaderFlags
 */
export type HeaderFlags = Message<"hero.reports.v1.HeaderFlags"> & {
  /**
   * Checked if juvenile involved
   *
   * @generated from field: bool juvenile_involved = 1;
   */
  juvenileInvolved: boolean;

  /**
   * Checked if crime is one of the following codes or victim requests confidentiality
   *
   * @generated from field: bool confidential = 2;
   */
  confidential: boolean;

  /**
   * If property or evidence was booked in association to report
   *
   * @generated from field: bool property_evidence_booking = 3;
   */
  propertyEvidenceBooking: boolean;

  /**
   * If photographs are included with report
   *
   * @generated from field: bool photos = 4;
   */
  photos: boolean;

  /**
   * If fingerprints were successfully lifted in assocation to report
   *
   * @generated from field: bool prints = 5;
   */
  prints: boolean;

  /**
   * If alcohol was involved in incident or crime
   *
   * @generated from field: bool alcohol = 6;
   */
  alcohol: boolean;

  /**
   * If report is a crime report, defined as: any occurrence in which a criminal violation occurred, even if the victim does not want prosecution, or only wants the report for "documentation purposes"
   *
   * @generated from field: bool cr = 7;
   */
  cr: boolean;

  /**
   * If report is an incident report, with no crime involved
   *
   * @generated from field: bool ir = 8;
   */
  ir: boolean;

  /**
   * If report involves a casualty, defined as: any injury or complaint of pain that occurs to a citizen, regardless of the mode or method of injury. A report is a casualty report even if the citizen does not request medical aid, refuses medical aid, or states they will handle with personal physician
   *
   * @generated from field: bool cas = 9;
   */
  cas: boolean;

  /**
   * If report is a missing person report
   *
   * @generated from field: bool mp = 10;
   */
  mp: boolean;

  /**
   * If report is a Welfare and Institutions Code report
   *
   * @generated from field: bool w_and_i = 11;
   */
  wAndI: boolean;

  /**
   * If report is a warrants arrest
   *
   * @generated from field: bool wa = 12;
   */
  wa: boolean;
};

/**
 * Describes the message hero.reports.v1.HeaderFlags.
 * Use `create(HeaderFlagsSchema)` to create a new message.
 */
export const HeaderFlagsSchema: GenMessage<HeaderFlags> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 0);

/**
 * New message for vehicle info.
 *
 * @generated from message hero.reports.v1.Vehicle
 */
export type Vehicle = Message<"hero.reports.v1.Vehicle"> & {
  /**
   * unique vehicle ID
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * reference to the parent report (always present)
   *
   * @generated from field: repeated string report_id = 2;
   */
  reportId: string[];

  /**
   * optional reference to an involved party
   *
   * @generated from field: optional string involved_party_id = 3;
   */
  involvedPartyId?: string;

  /**
   * @generated from field: string plate = 4;
   */
  plate: string;

  /**
   * @generated from field: int32 year = 5;
   */
  year: number;

  /**
   * @generated from field: string make = 6;
   */
  make: string;

  /**
   * @generated from field: string model = 7;
   */
  model: string;

  /**
   * @generated from field: string color = 8;
   */
  color: string;

  /**
   * e.g. "Sedan", "SUV", "Truck", "Van", "Motorcycle", "Other"
   *
   * @generated from field: string vehicle_type = 9;
   */
  vehicleType: string;
};

/**
 * Describes the message hero.reports.v1.Vehicle.
 * Use `create(VehicleSchema)` to create a new message.
 */
export const VehicleSchema: GenMessage<Vehicle> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 1);

/**
 * InvolvedParty remains a separate entity.
 * (Vehicles here are repeated inside InvolvedParty.)
 *
 * @generated from message hero.reports.v1.InvolvedParty
 */
export type InvolvedParty = Message<"hero.reports.v1.InvolvedParty"> & {
  /**
   * @generated from field: repeated hero.reports.v1.PartyType party_types = 1;
   */
  partyTypes: PartyType[];

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: int32 age = 3;
   */
  age: number;

  /**
   * @generated from field: google.protobuf.Timestamp dob = 4;
   */
  dob?: Timestamp;

  /**
   * Country XREF number
   *
   * @generated from field: int32 country = 5;
   */
  country: number;

  /**
   * @generated from field: string univ_association = 6;
   */
  univAssociation: string;

  /**
   * @generated from field: string address = 7;
   */
  address: string;

  /**
   * @generated from field: string primary_phone = 8;
   */
  primaryPhone: string;

  /**
   * @generated from field: string secondary_phone = 9;
   */
  secondaryPhone: string;

  /**
   * @generated from field: string sex = 10;
   */
  sex: string;

  /**
   * @generated from field: string race = 11;
   */
  race: string;

  /**
   * @generated from field: string drivers_license = 12;
   */
  driversLicense: string;

  /**
   * @generated from field: float height = 13;
   */
  height: number;

  /**
   * @generated from field: float weight = 14;
   */
  weight: number;

  /**
   * @generated from field: string hair = 15;
   */
  hair: string;

  /**
   * @generated from field: string build = 16;
   */
  build: string;

  /**
   * @generated from field: string eyes = 17;
   */
  eyes: string;

  /**
   * @generated from field: string complexion = 18;
   */
  complexion: string;

  /**
   * Checkbox for known modus operandi
   *
   * @generated from field: bool mo = 19;
   */
  mo: boolean;

  /**
   * "if box checked, enter the actions used by the individual(s) to execute the crime, prevent its detection and/or facilitate escape, and enter the method of operation in the free form text box below the checkbox if the information is applicable to the crime beingreported.  If not known, leave blank"
   *
   * @generated from field: string mo_text = 20;
   */
  moText: string;

  /**
   * checkbox for point of entry 
   *
   * @generated from field: bool point_of_entry = 21;
   */
  pointOfEntry: boolean;

  /**
   * @generated from field: string point_of_entry_text = 22;
   */
  pointOfEntryText: string;

  /**
   * checkbox for method used
   *
   * @generated from field: bool method_used = 23;
   */
  methodUsed: boolean;

  /**
   * "if box checked, enter the method used by the individual(s) to execute the crime, and enter the method of operation in the free form text box below the checkbox if the information is applicable to the crime being reported.  If not known, leave blank"
   *
   * @generated from field: string method_used_text = 24;
   */
  methodUsedText: string;

  /**
   * checkbox for weapons
   *
   * @generated from field: bool weapons = 25;
   */
  weapons: boolean;

  /**
   * "if box checked, enter the type of weapon used, and enter the weapon description in the free form text box below the checkbox if the information is applicable to the crime being reported.  If not known, leave blank"
   *
   * @generated from field: string weapons_text = 26;
   */
  weaponsText: string;

  /**
   * checkbox for scars, marks, or tattoos
   *
   * @generated from field: bool scars_marks_tattoos = 27;
   */
  scarsMarksTattoos: boolean;

  /**
   * "if box checked, enter the description of scars, marks, or tattoos, and enter the location of the scars, marks, or tattoos in the free form text box below the checkbox if the information is applicable to the crime being reported.  If not known, leave blank"
   *
   * @generated from field: string scars_marks_tattoos_text = 28;
   */
  scarsMarksTattoosText: string;

  /**
   * checkbox for other information
   *
   * @generated from field: bool other_info = 29;
   */
  otherInfo: boolean;

  /**
   * @generated from field: string other_info_text = 30;
   */
  otherInfoText: string;

  /**
   * Unique identifier for InvolvedParty
   *
   * @generated from field: string id = 31;
   */
  id: string;

  /**
   * Reference to the parent report
   *
   * @generated from field: repeated string report_id = 32;
   */
  reportId: string[];
};

/**
 * Describes the message hero.reports.v1.InvolvedParty.
 * Use `create(InvolvedPartySchema)` to create a new message.
 */
export const InvolvedPartySchema: GenMessage<InvolvedParty> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 2);

/**
 * PropertyItem remains a separate entity.
 *
 * @generated from message hero.reports.v1.PropertyItem
 */
export type PropertyItem = Message<"hero.reports.v1.PropertyItem"> & {
  /**
   * @generated from field: string item = 1;
   */
  item: string;

  /**
   * @generated from field: string status = 2;
   */
  status: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: float value = 4;
   */
  value: number;

  /**
   * Unique identifier for PropertyItem
   *
   * @generated from field: string id = 5;
   */
  id: string;

  /**
   * Reference to the parent report
   *
   * @generated from field: repeated string report_id = 6;
   */
  reportId: string[];
};

/**
 * Describes the message hero.reports.v1.PropertyItem.
 * Use `create(PropertyItemSchema)` to create a new message.
 */
export const PropertyItemSchema: GenMessage<PropertyItem> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 3);

/**
 * The flattened Report message
 *
 * @generated from message hero.reports.v1.Report
 */
export type Report = Message<"hero.reports.v1.Report"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: hero.reports.v1.ReportStatus status = 4;
   */
  status: ReportStatus;

  /**
   * @generated from field: string situation_id = 5;
   */
  situationId: string;

  /**
   * @generated from field: google.protobuf.Timestamp create_time = 6;
   */
  createTime?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp update_time = 7;
   */
  updateTime?: Timestamp;

  /**
   * Flattened header information
   *
   * @generated from field: hero.reports.v1.HeaderFlags header_flags = 8;
   */
  headerFlags?: HeaderFlags;

  /**
   * @generated from field: google.protobuf.Timestamp call_timestamp = 9;
   */
  callTimestamp?: Timestamp;

  /**
   * @generated from field: string location_of_occurrence = 10;
   */
  locationOfOccurrence: string;

  /**
   * @generated from field: string location_on_campus = 11;
   */
  locationOnCampus: string;

  /**
   * @generated from field: string report_number = 12;
   */
  reportNumber: string;

  /**
   * @generated from field: string crime_definition = 13;
   */
  crimeDefinition: string;

  /**
   * @generated from field: string code_section = 14;
   */
  codeSection: string;

  /**
   * @generated from field: string source = 15;
   */
  source: string;

  /**
   * @generated from field: string event_number = 16;
   */
  eventNumber: string;

  /**
   * @generated from field: repeated string connected_reports_id = 17;
   */
  connectedReportsId: string[];

  /**
   * @generated from field: string agency = 18;
   */
  agency: string;

  /**
   * @generated from field: string occurred_on = 19;
   */
  occurredOn: string;

  /**
   * @generated from field: string day_of_week = 20;
   */
  dayOfWeek: string;

  /**
   * @generated from field: string time_occurred = 21;
   */
  timeOccurred: string;

  /**
   * @generated from field: string lat_long = 22;
   */
  latLong: string;

  /**
   * Flattened case summary information
   *
   * @generated from field: string summary = 23;
   */
  summary: string;

  /**
   * e.g., Cleared, closed, pending, unfounded, other
   *
   * @generated from field: repeated string case_status = 24;
   */
  caseStatus: string[];

  /**
   * e.g., DA, Detectives, Outside Agency, Other
   *
   * @generated from field: repeated string notification_to = 25;
   */
  notificationTo: string[];

  /**
   * @generated from field: string date_written = 26;
   */
  dateWritten: string;

  /**
   * @generated from field: string time_written = 27;
   */
  timeWritten: string;

  /**
   * Flattened narrative details
   *
   * @generated from field: string notification_text = 28;
   */
  notificationText: string;

  /**
   * @generated from field: string narrative_body = 29;
   */
  narrativeBody: string;

  /**
   * Related entities stored in separate tables but represented here for API completeness.
   *
   * IDs of InvolvedParty entities
   *
   * @generated from field: repeated string involved_parties = 30;
   */
  involvedParties: string[];

  /**
   * IDs of PropertyItem entities
   *
   * @generated from field: repeated string properties = 31;
   */
  properties: string[];

  /**
   * IDs of Vehicle entities
   *
   * @generated from field: repeated string vehicles = 32;
   */
  vehicles: string[];
};

/**
 * Describes the message hero.reports.v1.Report.
 * Use `create(ReportSchema)` to create a new message.
 */
export const ReportSchema: GenMessage<Report> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 4);

/**
 * --- Requests & Responses ---
 *
 * @generated from message hero.reports.v1.CreateReportRequest
 */
export type CreateReportRequest = Message<"hero.reports.v1.CreateReportRequest"> & {
  /**
   * @generated from field: hero.reports.v1.Report report = 1;
   */
  report?: Report;
};

/**
 * Describes the message hero.reports.v1.CreateReportRequest.
 * Use `create(CreateReportRequestSchema)` to create a new message.
 */
export const CreateReportRequestSchema: GenMessage<CreateReportRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 5);

/**
 * @generated from message hero.reports.v1.CreateReportResponse
 */
export type CreateReportResponse = Message<"hero.reports.v1.CreateReportResponse"> & {
  /**
   * @generated from field: hero.reports.v1.Report report = 1;
   */
  report?: Report;
};

/**
 * Describes the message hero.reports.v1.CreateReportResponse.
 * Use `create(CreateReportResponseSchema)` to create a new message.
 */
export const CreateReportResponseSchema: GenMessage<CreateReportResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 6);

/**
 * @generated from message hero.reports.v1.GetReportRequest
 */
export type GetReportRequest = Message<"hero.reports.v1.GetReportRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.reports.v1.GetReportRequest.
 * Use `create(GetReportRequestSchema)` to create a new message.
 */
export const GetReportRequestSchema: GenMessage<GetReportRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 7);

/**
 * @generated from message hero.reports.v1.UpdateReportRequest
 */
export type UpdateReportRequest = Message<"hero.reports.v1.UpdateReportRequest"> & {
  /**
   * @generated from field: hero.reports.v1.Report report = 1;
   */
  report?: Report;
};

/**
 * Describes the message hero.reports.v1.UpdateReportRequest.
 * Use `create(UpdateReportRequestSchema)` to create a new message.
 */
export const UpdateReportRequestSchema: GenMessage<UpdateReportRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 8);

/**
 * @generated from message hero.reports.v1.ListReportsRequest
 */
export type ListReportsRequest = Message<"hero.reports.v1.ListReportsRequest"> & {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;
};

/**
 * Describes the message hero.reports.v1.ListReportsRequest.
 * Use `create(ListReportsRequestSchema)` to create a new message.
 */
export const ListReportsRequestSchema: GenMessage<ListReportsRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 9);

/**
 * @generated from message hero.reports.v1.ListReportsResponse
 */
export type ListReportsResponse = Message<"hero.reports.v1.ListReportsResponse"> & {
  /**
   * @generated from field: repeated hero.reports.v1.Report reports = 1;
   */
  reports: Report[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.reports.v1.ListReportsResponse.
 * Use `create(ListReportsResponseSchema)` to create a new message.
 */
export const ListReportsResponseSchema: GenMessage<ListReportsResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 10);

/**
 * @generated from message hero.reports.v1.DeleteReportRequest
 */
export type DeleteReportRequest = Message<"hero.reports.v1.DeleteReportRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.reports.v1.DeleteReportRequest.
 * Use `create(DeleteReportRequestSchema)` to create a new message.
 */
export const DeleteReportRequestSchema: GenMessage<DeleteReportRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 11);

/**
 * @generated from message hero.reports.v1.GetReportBySituationIdRequest
 */
export type GetReportBySituationIdRequest = Message<"hero.reports.v1.GetReportBySituationIdRequest"> & {
  /**
   * @generated from field: string situation_id = 1;
   */
  situationId: string;

  /**
   * Optional pagination token
   *
   * @generated from field: string page_token = 2;
   */
  pageToken: string;
};

/**
 * Describes the message hero.reports.v1.GetReportBySituationIdRequest.
 * Use `create(GetReportBySituationIdRequestSchema)` to create a new message.
 */
export const GetReportBySituationIdRequestSchema: GenMessage<GetReportBySituationIdRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 12);

/**
 * @generated from message hero.reports.v1.GetReportBySituationIdResponse
 */
export type GetReportBySituationIdResponse = Message<"hero.reports.v1.GetReportBySituationIdResponse"> & {
  /**
   * @generated from field: repeated hero.reports.v1.Report reports = 1;
   */
  reports: Report[];

  /**
   * Token for retrieving the next page of results
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.reports.v1.GetReportBySituationIdResponse.
 * Use `create(GetReportBySituationIdResponseSchema)` to create a new message.
 */
export const GetReportBySituationIdResponseSchema: GenMessage<GetReportBySituationIdResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 13);

/**
 * --- Vehicle CRUD Messages ---
 *
 * @generated from message hero.reports.v1.CreateVehicleRequest
 */
export type CreateVehicleRequest = Message<"hero.reports.v1.CreateVehicleRequest"> & {
  /**
   * @generated from field: hero.reports.v1.Vehicle vehicle = 1;
   */
  vehicle?: Vehicle;
};

/**
 * Describes the message hero.reports.v1.CreateVehicleRequest.
 * Use `create(CreateVehicleRequestSchema)` to create a new message.
 */
export const CreateVehicleRequestSchema: GenMessage<CreateVehicleRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 14);

/**
 * @generated from message hero.reports.v1.CreateVehicleResponse
 */
export type CreateVehicleResponse = Message<"hero.reports.v1.CreateVehicleResponse"> & {
  /**
   * @generated from field: hero.reports.v1.Vehicle vehicle = 1;
   */
  vehicle?: Vehicle;
};

/**
 * Describes the message hero.reports.v1.CreateVehicleResponse.
 * Use `create(CreateVehicleResponseSchema)` to create a new message.
 */
export const CreateVehicleResponseSchema: GenMessage<CreateVehicleResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 15);

/**
 * @generated from message hero.reports.v1.GetVehicleRequest
 */
export type GetVehicleRequest = Message<"hero.reports.v1.GetVehicleRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.reports.v1.GetVehicleRequest.
 * Use `create(GetVehicleRequestSchema)` to create a new message.
 */
export const GetVehicleRequestSchema: GenMessage<GetVehicleRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 16);

/**
 * @generated from message hero.reports.v1.UpdateVehicleRequest
 */
export type UpdateVehicleRequest = Message<"hero.reports.v1.UpdateVehicleRequest"> & {
  /**
   * @generated from field: hero.reports.v1.Vehicle vehicle = 1;
   */
  vehicle?: Vehicle;
};

/**
 * Describes the message hero.reports.v1.UpdateVehicleRequest.
 * Use `create(UpdateVehicleRequestSchema)` to create a new message.
 */
export const UpdateVehicleRequestSchema: GenMessage<UpdateVehicleRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 17);

/**
 * @generated from message hero.reports.v1.DeleteVehicleRequest
 */
export type DeleteVehicleRequest = Message<"hero.reports.v1.DeleteVehicleRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.reports.v1.DeleteVehicleRequest.
 * Use `create(DeleteVehicleRequestSchema)` to create a new message.
 */
export const DeleteVehicleRequestSchema: GenMessage<DeleteVehicleRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 18);

/**
 * @generated from message hero.reports.v1.ListVehiclesRequest
 */
export type ListVehiclesRequest = Message<"hero.reports.v1.ListVehiclesRequest"> & {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;
};

/**
 * Describes the message hero.reports.v1.ListVehiclesRequest.
 * Use `create(ListVehiclesRequestSchema)` to create a new message.
 */
export const ListVehiclesRequestSchema: GenMessage<ListVehiclesRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 19);

/**
 * @generated from message hero.reports.v1.ListVehiclesResponse
 */
export type ListVehiclesResponse = Message<"hero.reports.v1.ListVehiclesResponse"> & {
  /**
   * @generated from field: repeated hero.reports.v1.Vehicle vehicles = 1;
   */
  vehicles: Vehicle[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.reports.v1.ListVehiclesResponse.
 * Use `create(ListVehiclesResponseSchema)` to create a new message.
 */
export const ListVehiclesResponseSchema: GenMessage<ListVehiclesResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 20);

/**
 * --- InvolvedParty CRUD Messages ---
 *
 * @generated from message hero.reports.v1.CreateInvolvedPartyRequest
 */
export type CreateInvolvedPartyRequest = Message<"hero.reports.v1.CreateInvolvedPartyRequest"> & {
  /**
   * @generated from field: hero.reports.v1.InvolvedParty involved_party = 1;
   */
  involvedParty?: InvolvedParty;
};

/**
 * Describes the message hero.reports.v1.CreateInvolvedPartyRequest.
 * Use `create(CreateInvolvedPartyRequestSchema)` to create a new message.
 */
export const CreateInvolvedPartyRequestSchema: GenMessage<CreateInvolvedPartyRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 21);

/**
 * @generated from message hero.reports.v1.CreateInvolvedPartyResponse
 */
export type CreateInvolvedPartyResponse = Message<"hero.reports.v1.CreateInvolvedPartyResponse"> & {
  /**
   * @generated from field: hero.reports.v1.InvolvedParty involved_party = 1;
   */
  involvedParty?: InvolvedParty;
};

/**
 * Describes the message hero.reports.v1.CreateInvolvedPartyResponse.
 * Use `create(CreateInvolvedPartyResponseSchema)` to create a new message.
 */
export const CreateInvolvedPartyResponseSchema: GenMessage<CreateInvolvedPartyResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 22);

/**
 * @generated from message hero.reports.v1.GetInvolvedPartyRequest
 */
export type GetInvolvedPartyRequest = Message<"hero.reports.v1.GetInvolvedPartyRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.reports.v1.GetInvolvedPartyRequest.
 * Use `create(GetInvolvedPartyRequestSchema)` to create a new message.
 */
export const GetInvolvedPartyRequestSchema: GenMessage<GetInvolvedPartyRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 23);

/**
 * @generated from message hero.reports.v1.UpdateInvolvedPartyRequest
 */
export type UpdateInvolvedPartyRequest = Message<"hero.reports.v1.UpdateInvolvedPartyRequest"> & {
  /**
   * @generated from field: hero.reports.v1.InvolvedParty involved_party = 1;
   */
  involvedParty?: InvolvedParty;
};

/**
 * Describes the message hero.reports.v1.UpdateInvolvedPartyRequest.
 * Use `create(UpdateInvolvedPartyRequestSchema)` to create a new message.
 */
export const UpdateInvolvedPartyRequestSchema: GenMessage<UpdateInvolvedPartyRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 24);

/**
 * @generated from message hero.reports.v1.DeleteInvolvedPartyRequest
 */
export type DeleteInvolvedPartyRequest = Message<"hero.reports.v1.DeleteInvolvedPartyRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.reports.v1.DeleteInvolvedPartyRequest.
 * Use `create(DeleteInvolvedPartyRequestSchema)` to create a new message.
 */
export const DeleteInvolvedPartyRequestSchema: GenMessage<DeleteInvolvedPartyRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 25);

/**
 * @generated from message hero.reports.v1.ListInvolvedPartiesRequest
 */
export type ListInvolvedPartiesRequest = Message<"hero.reports.v1.ListInvolvedPartiesRequest"> & {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;
};

/**
 * Describes the message hero.reports.v1.ListInvolvedPartiesRequest.
 * Use `create(ListInvolvedPartiesRequestSchema)` to create a new message.
 */
export const ListInvolvedPartiesRequestSchema: GenMessage<ListInvolvedPartiesRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 26);

/**
 * @generated from message hero.reports.v1.ListInvolvedPartiesResponse
 */
export type ListInvolvedPartiesResponse = Message<"hero.reports.v1.ListInvolvedPartiesResponse"> & {
  /**
   * @generated from field: repeated hero.reports.v1.InvolvedParty involved_parties = 1;
   */
  involvedParties: InvolvedParty[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.reports.v1.ListInvolvedPartiesResponse.
 * Use `create(ListInvolvedPartiesResponseSchema)` to create a new message.
 */
export const ListInvolvedPartiesResponseSchema: GenMessage<ListInvolvedPartiesResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 27);

/**
 * --- PropertyItem CRUD Messages ---
 *
 * @generated from message hero.reports.v1.CreatePropertyItemRequest
 */
export type CreatePropertyItemRequest = Message<"hero.reports.v1.CreatePropertyItemRequest"> & {
  /**
   * @generated from field: hero.reports.v1.PropertyItem property_item = 1;
   */
  propertyItem?: PropertyItem;
};

/**
 * Describes the message hero.reports.v1.CreatePropertyItemRequest.
 * Use `create(CreatePropertyItemRequestSchema)` to create a new message.
 */
export const CreatePropertyItemRequestSchema: GenMessage<CreatePropertyItemRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 28);

/**
 * @generated from message hero.reports.v1.CreatePropertyItemResponse
 */
export type CreatePropertyItemResponse = Message<"hero.reports.v1.CreatePropertyItemResponse"> & {
  /**
   * @generated from field: hero.reports.v1.PropertyItem property_item = 1;
   */
  propertyItem?: PropertyItem;
};

/**
 * Describes the message hero.reports.v1.CreatePropertyItemResponse.
 * Use `create(CreatePropertyItemResponseSchema)` to create a new message.
 */
export const CreatePropertyItemResponseSchema: GenMessage<CreatePropertyItemResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 29);

/**
 * @generated from message hero.reports.v1.GetPropertyItemRequest
 */
export type GetPropertyItemRequest = Message<"hero.reports.v1.GetPropertyItemRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.reports.v1.GetPropertyItemRequest.
 * Use `create(GetPropertyItemRequestSchema)` to create a new message.
 */
export const GetPropertyItemRequestSchema: GenMessage<GetPropertyItemRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 30);

/**
 * @generated from message hero.reports.v1.UpdatePropertyItemRequest
 */
export type UpdatePropertyItemRequest = Message<"hero.reports.v1.UpdatePropertyItemRequest"> & {
  /**
   * @generated from field: hero.reports.v1.PropertyItem property_item = 1;
   */
  propertyItem?: PropertyItem;
};

/**
 * Describes the message hero.reports.v1.UpdatePropertyItemRequest.
 * Use `create(UpdatePropertyItemRequestSchema)` to create a new message.
 */
export const UpdatePropertyItemRequestSchema: GenMessage<UpdatePropertyItemRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 31);

/**
 * @generated from message hero.reports.v1.DeletePropertyItemRequest
 */
export type DeletePropertyItemRequest = Message<"hero.reports.v1.DeletePropertyItemRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.reports.v1.DeletePropertyItemRequest.
 * Use `create(DeletePropertyItemRequestSchema)` to create a new message.
 */
export const DeletePropertyItemRequestSchema: GenMessage<DeletePropertyItemRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 32);

/**
 * @generated from message hero.reports.v1.ListPropertyItemsRequest
 */
export type ListPropertyItemsRequest = Message<"hero.reports.v1.ListPropertyItemsRequest"> & {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;
};

/**
 * Describes the message hero.reports.v1.ListPropertyItemsRequest.
 * Use `create(ListPropertyItemsRequestSchema)` to create a new message.
 */
export const ListPropertyItemsRequestSchema: GenMessage<ListPropertyItemsRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 33);

/**
 * @generated from message hero.reports.v1.ListPropertyItemsResponse
 */
export type ListPropertyItemsResponse = Message<"hero.reports.v1.ListPropertyItemsResponse"> & {
  /**
   * @generated from field: repeated hero.reports.v1.PropertyItem property_items = 1;
   */
  propertyItems: PropertyItem[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.reports.v1.ListPropertyItemsResponse.
 * Use `create(ListPropertyItemsResponseSchema)` to create a new message.
 */
export const ListPropertyItemsResponseSchema: GenMessage<ListPropertyItemsResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v1_reports, 34);

/**
 * Enum for the state of a report.
 *
 * @generated from enum hero.reports.v1.ReportStatus
 */
export enum ReportStatus {
  /**
   * @generated from enum value: UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: CREATED = 1;
   */
  CREATED = 1,

  /**
   * @generated from enum value: DRAFT = 2;
   */
  DRAFT = 2,

  /**
   * @generated from enum value: IN_REVIEW = 3;
   */
  IN_REVIEW = 3,

  /**
   * @generated from enum value: SUBMITTED = 4;
   */
  SUBMITTED = 4,

  /**
   * @generated from enum value: EDIT_NEEDED = 5;
   */
  EDIT_NEEDED = 5,

  /**
   * @generated from enum value: COMPLETED = 6;
   */
  COMPLETED = 6,

  /**
   * @generated from enum value: ACCEPTED = 7;
   */
  ACCEPTED = 7,
}

/**
 * Describes the enum hero.reports.v1.ReportStatus.
 */
export const ReportStatusSchema: GenEnum<ReportStatus> = /*@__PURE__*/
  enumDesc(file_hero_reports_v1_reports, 0);

/**
 * Enum for party types for involved parties.
 *
 * @generated from enum hero.reports.v1.PartyType
 */
export enum PartyType {
  /**
   * @generated from enum value: PARTY_TYPE_UNSPECIFIED = 0;
   */
  PARTY_TYPE_UNSPECIFIED = 0,

  /**
   * @generated from enum value: VICTIM = 1;
   */
  VICTIM = 1,

  /**
   * @generated from enum value: SUSPECT = 2;
   */
  SUSPECT = 2,

  /**
   * @generated from enum value: WITNESS = 3;
   */
  WITNESS = 3,

  /**
   * @generated from enum value: REPORTER = 4;
   */
  REPORTER = 4,

  /**
   * @generated from enum value: OTHER = 5;
   */
  OTHER = 5,
}

/**
 * Describes the enum hero.reports.v1.PartyType.
 */
export const PartyTypeSchema: GenEnum<PartyType> = /*@__PURE__*/
  enumDesc(file_hero_reports_v1_reports, 1);

/**
 * --- Service Definition ---
 *
 * @generated from service hero.reports.v1.ReportService
 */
export const ReportService: GenService<{
  /**
   * @generated from rpc hero.reports.v1.ReportService.CreateReport
   */
  createReport: {
    methodKind: "unary";
    input: typeof CreateReportRequestSchema;
    output: typeof CreateReportResponseSchema;
  },
  /**
   * @generated from rpc hero.reports.v1.ReportService.GetReport
   */
  getReport: {
    methodKind: "unary";
    input: typeof GetReportRequestSchema;
    output: typeof ReportSchema;
  },
  /**
   * @generated from rpc hero.reports.v1.ReportService.UpdateReport
   */
  updateReport: {
    methodKind: "unary";
    input: typeof UpdateReportRequestSchema;
    output: typeof ReportSchema;
  },
  /**
   * @generated from rpc hero.reports.v1.ReportService.ListReports
   */
  listReports: {
    methodKind: "unary";
    input: typeof ListReportsRequestSchema;
    output: typeof ListReportsResponseSchema;
  },
  /**
   * @generated from rpc hero.reports.v1.ReportService.DeleteReport
   */
  deleteReport: {
    methodKind: "unary";
    input: typeof DeleteReportRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc hero.reports.v1.ReportService.GetReportBySituationId
   */
  getReportBySituationId: {
    methodKind: "unary";
    input: typeof GetReportBySituationIdRequestSchema;
    output: typeof GetReportBySituationIdResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_reports_v1_reports, 0);

/**
 * --- Vehicle Service ---
 *
 * @generated from service hero.reports.v1.VehicleService
 */
export const VehicleService: GenService<{
  /**
   * @generated from rpc hero.reports.v1.VehicleService.CreateVehicle
   */
  createVehicle: {
    methodKind: "unary";
    input: typeof CreateVehicleRequestSchema;
    output: typeof CreateVehicleResponseSchema;
  },
  /**
   * @generated from rpc hero.reports.v1.VehicleService.GetVehicle
   */
  getVehicle: {
    methodKind: "unary";
    input: typeof GetVehicleRequestSchema;
    output: typeof VehicleSchema;
  },
  /**
   * @generated from rpc hero.reports.v1.VehicleService.UpdateVehicle
   */
  updateVehicle: {
    methodKind: "unary";
    input: typeof UpdateVehicleRequestSchema;
    output: typeof VehicleSchema;
  },
  /**
   * @generated from rpc hero.reports.v1.VehicleService.ListVehicles
   */
  listVehicles: {
    methodKind: "unary";
    input: typeof ListVehiclesRequestSchema;
    output: typeof ListVehiclesResponseSchema;
  },
  /**
   * @generated from rpc hero.reports.v1.VehicleService.DeleteVehicle
   */
  deleteVehicle: {
    methodKind: "unary";
    input: typeof DeleteVehicleRequestSchema;
    output: typeof EmptySchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_reports_v1_reports, 1);

/**
 * --- InvolvedParty Service ---
 *
 * @generated from service hero.reports.v1.InvolvedPartyService
 */
export const InvolvedPartyService: GenService<{
  /**
   * @generated from rpc hero.reports.v1.InvolvedPartyService.CreateInvolvedParty
   */
  createInvolvedParty: {
    methodKind: "unary";
    input: typeof CreateInvolvedPartyRequestSchema;
    output: typeof CreateInvolvedPartyResponseSchema;
  },
  /**
   * @generated from rpc hero.reports.v1.InvolvedPartyService.GetInvolvedParty
   */
  getInvolvedParty: {
    methodKind: "unary";
    input: typeof GetInvolvedPartyRequestSchema;
    output: typeof InvolvedPartySchema;
  },
  /**
   * @generated from rpc hero.reports.v1.InvolvedPartyService.UpdateInvolvedParty
   */
  updateInvolvedParty: {
    methodKind: "unary";
    input: typeof UpdateInvolvedPartyRequestSchema;
    output: typeof InvolvedPartySchema;
  },
  /**
   * @generated from rpc hero.reports.v1.InvolvedPartyService.ListInvolvedParties
   */
  listInvolvedParties: {
    methodKind: "unary";
    input: typeof ListInvolvedPartiesRequestSchema;
    output: typeof ListInvolvedPartiesResponseSchema;
  },
  /**
   * @generated from rpc hero.reports.v1.InvolvedPartyService.DeleteInvolvedParty
   */
  deleteInvolvedParty: {
    methodKind: "unary";
    input: typeof DeleteInvolvedPartyRequestSchema;
    output: typeof EmptySchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_reports_v1_reports, 2);

/**
 * --- PropertyItem Service ---
 *
 * @generated from service hero.reports.v1.PropertyItemService
 */
export const PropertyItemService: GenService<{
  /**
   * @generated from rpc hero.reports.v1.PropertyItemService.CreatePropertyItem
   */
  createPropertyItem: {
    methodKind: "unary";
    input: typeof CreatePropertyItemRequestSchema;
    output: typeof CreatePropertyItemResponseSchema;
  },
  /**
   * @generated from rpc hero.reports.v1.PropertyItemService.GetPropertyItem
   */
  getPropertyItem: {
    methodKind: "unary";
    input: typeof GetPropertyItemRequestSchema;
    output: typeof PropertyItemSchema;
  },
  /**
   * @generated from rpc hero.reports.v1.PropertyItemService.UpdatePropertyItem
   */
  updatePropertyItem: {
    methodKind: "unary";
    input: typeof UpdatePropertyItemRequestSchema;
    output: typeof PropertyItemSchema;
  },
  /**
   * @generated from rpc hero.reports.v1.PropertyItemService.ListPropertyItems
   */
  listPropertyItems: {
    methodKind: "unary";
    input: typeof ListPropertyItemsRequestSchema;
    output: typeof ListPropertyItemsResponseSchema;
  },
  /**
   * @generated from rpc hero.reports.v1.PropertyItemService.DeletePropertyItem
   */
  deletePropertyItem: {
    methodKind: "unary";
    input: typeof DeletePropertyItemRequestSchema;
    output: typeof EmptySchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_reports_v1_reports, 3);

