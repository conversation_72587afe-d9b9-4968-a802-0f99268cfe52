// @generated by protoc-gen-es v2.6.1 with parameter "target=ts"
// @generated from file hero/reports/v2/reports.proto (package hero.reports.v2, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { EmptySchema } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_empty, file_google_protobuf_struct } from "@bufbuild/protobuf/wkt";
import { file_hero_assets_v2_assets } from "../../assets/v2/assets_pb";
import type { Reference } from "../../entity/v1/entity_pb";
import { file_hero_entity_v1_entity } from "../../entity/v1/entity_pb";
import type { SituationType } from "../../situations/v2/situations_pb";
import { file_hero_situations_v2_situations } from "../../situations/v2/situations_pb";
import type { JsonObject, Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/reports/v2/reports.proto.
 */
export const file_hero_reports_v2_reports: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_empty, file_hero_assets_v2_assets, file_hero_entity_v1_entity, file_hero_situations_v2_situations, file_google_protobuf_struct]);

/**
 * -----------------------------------------------------------------------------
 * Object Reference (for Relations)
 * -----------------------------------------------------------------------------
 * Flexible reference to any object within a report that can participate in relations.
 * Uses string-based types for maximum flexibility without requiring protobuf changes.
 *
 * VALIDATION: At least one of report_scoped_id, global_id, or external_id MUST be set.
 *
 * ID Types:
 *   - report_scoped_id: IDs unique within this report (e.g., responder.id, reporting_person.id, section content IDs)
 *   - global_id: System-wide unique IDs (e.g., asset_id, entity_id, case_id, situation_id)
 *   - external_id: IDs from external systems or custom references
 *
 * Common object_type values (extensible):
 *   - "entity"           - Reference to an entity (person, vehicle, property) - use global_id
 *   - "section"          - Reference to a report section - use global_id (section.id)
 *   - "offense"          - Reference to an offense within a section - use report_scoped_id (offense.id)
 *   - "incident_details" - Reference to incident details within a section - use report_scoped_id (incident_details.id)
 *   - "narrative"        - Reference to narrative content within a section - use report_scoped_id (narrative.id)
 *   - "comment"          - Reference to a comment - use global_id (comment.id)
 *   - "responder"        - Reference to a specific responder in incident details - use report_scoped_id (responder.id)
 *   - "reporting_person" - Reference to the reporting person in incident details - use report_scoped_id (reporting_person.id)
 *   - "asset"            - Reference to an asset - use global_id (asset_id)
 *   - "case"             - Reference to a case - use global_id (case_id)
 *   - "situation"        - Reference to a situation - use global_id (situation_id)
 *   - "file_reference"   - Reference to a file reference within a media section - use report_scoped_id (file_reference.id)
 *   - "media_content"    - Reference to media content within a section - use report_scoped_id (media_content.id)
 *   - "custom_*"         - Any custom object type
 *
 * @generated from message hero.reports.v2.ObjectReference
 */
export type ObjectReference = Message<"hero.reports.v2.ObjectReference"> & {
  /**
   * Type of the referenced object (flexible string) - REQUIRED
   *
   * @generated from field: string object_type = 1;
   */
  objectType: string;

  /**
   * ID unique within this report (e.g., responder.id, content.id)
   *
   * @generated from field: string report_scoped_id = 2;
   */
  reportScopedId: string;

  /**
   * System-wide unique ID (e.g., asset_id, entity_id, section.id)
   *
   * @generated from field: string global_id = 3;
   */
  globalId: string;

  /**
   * External system ID or custom reference
   *
   * @generated from field: string external_id = 4;
   */
  externalId: string;

  /**
   * If object is within a section, the section ID for context
   *
   * @generated from field: string section_id = 5;
   */
  sectionId: string;

  /**
   * Human-readable name for the referenced object (recommended)
   *
   * @generated from field: string display_name = 6;
   */
  displayName: string;

  /**
   * Additional metadata about the reference
   *
   * @generated from field: google.protobuf.Struct metadata = 7;
   */
  metadata?: JsonObject;
};

/**
 * Describes the message hero.reports.v2.ObjectReference.
 * Use `create(ObjectReferenceSchema)` to create a new message.
 */
export const ObjectReferenceSchema: GenMessage<ObjectReference> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 0);

/**
 * -----------------------------------------------------------------------------
 * Relation
 * -----------------------------------------------------------------------------
 * Establishes a semantic connection between any two objects within a report.
 * Uses string-based relation types for maximum flexibility without requiring protobuf changes.
 * Relationships are bidirectional - both objects have equal weight in the relationship.
 *
 * VALIDATION: 
 *   - object_a and object_b MUST be valid ObjectReference instances
 *   - relation_type MUST be non-empty
 *   - object_a and object_b SHOULD NOT reference the same object (no self-relations)
 *
 * Common relation_type values (extensible - contextualized for college campus incidents):
 *   - "associated_with"    - General association between objects
 *   - "connected_to"       - Direct connection between objects  
 *   - "related_to"         - General relation between objects
 *   - "involved_in"        - Both objects involved in same incident
 *   - "occurred_with"      - Objects occurred together in incident
 *   - "located_at"         - Object/person located at facility/room/building
 *   - "part_of"            - Object is part of another (e.g., room part of building)
 *   - "witnessed_by"       - Incident/event witnessed by person
 *   - "reported_by"        - Incident reported by person/student/staff
 *   - "responded_to_by"    - Incident responded to by officer/staff/security
 *   - "victim_of"          - Person was victim of incident/violation
 *   - "perpetrator_of"     - Person was perpetrator of incident/violation
 *   - "suspect_in"         - Person is suspect in incident
 *   - "complainant_in"     - Person filed complaint in incident
 *   - "violates_policy"    - Incident violates specific campus policy
 *   - "assigned_to"        - Case/incident assigned to officer/staff
 *   - "escalated_to"       - Incident escalated to higher authority
 *   - "follows_up_on"      - Report follows up on previous incident
 *   - "references"         - Object references another object
 *   - "occurred_in"        - Incident occurred in specific location/timeframe
 *   - "involves_substance" - Incident involves drugs/alcohol
 *   - "involves_property"  - Incident involves campus property/equipment
 *   - "custom_*"           - Any custom relation type
 *
 * @generated from message hero.reports.v2.Relation
 */
export type Relation = Message<"hero.reports.v2.Relation"> & {
  /**
   * Unique relation identifier - auto-generated
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * The report this relation belongs to - REQUIRED
   *
   * @generated from field: string report_id = 2;
   */
  reportId: string;

  /**
   * First object in the bidirectional relationship - REQUIRED
   *
   * @generated from field: hero.reports.v2.ObjectReference object_a = 3;
   */
  objectA?: ObjectReference;

  /**
   * Second object in the bidirectional relationship - REQUIRED
   *
   * @generated from field: hero.reports.v2.ObjectReference object_b = 4;
   */
  objectB?: ObjectReference;

  /**
   * Semantic meaning of the relationship (flexible string) - REQUIRED
   *
   * @generated from field: string relation_type = 5;
   */
  relationType: string;

  /**
   * Optional human-readable description of the relationship
   *
   * @generated from field: string description = 6;
   */
  description: string;

  /**
   * Flexible JSON for additional relationship data
   *
   * @generated from field: google.protobuf.Struct metadata = 7;
   */
  metadata?: JsonObject;

  /**
   * ISO8601 timestamp when relation was created - auto-generated
   *
   * @generated from field: string created_at = 8;
   */
  createdAt: string;

  /**
   * ISO8601 timestamp for last relation update - auto-managed
   *
   * @generated from field: string updated_at = 9;
   */
  updatedAt: string;

  /**
   * Asset ID who created this relation - REQUIRED
   *
   * @generated from field: string created_by_asset_id = 10;
   */
  createdByAssetId: string;
};

/**
 * Describes the message hero.reports.v2.Relation.
 * Use `create(RelationSchema)` to create a new message.
 */
export const RelationSchema: GenMessage<Relation> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 1);

/**
 * -----------------------------------------------------------------------------
 * Comment
 * -----------------------------------------------------------------------------
 * Represents either a global report comment or one tied to a specific section.
 *
 * @generated from message hero.reports.v2.Comment
 */
export type Comment = Message<"hero.reports.v2.Comment"> & {
  /**
   * Unique comment identifier
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * The report this comment belongs to
   *
   * @generated from field: string report_id = 2;
   */
  reportId: string;

  /**
   * If set, ties comment to a particular section
   *
   * @generated from field: string section_id = 3;
   */
  sectionId: string;

  /**
   * If set, this is a threaded reply
   *
   * @generated from field: string reply_to_comment_id = 4;
   */
  replyToCommentId: string;

  /**
   * Asset ID of the comment author
   *
   * @generated from field: string author_asset_id = 5;
   */
  authorAssetId: string;

  /**
   * Comment body text
   *
   * @generated from field: string text = 6;
   */
  text: string;

  /**
   * ISO8601 timestamp when created
   *
   * @generated from field: string created_at = 7;
   */
  createdAt: string;

  /**
   * True if comment has been marked resolved
   *
   * @generated from field: bool resolved = 8;
   */
  resolved: boolean;

  /**
   * ISO8601 timestamp when resolved
   *
   * @generated from field: string resolved_at = 9;
   */
  resolvedAt: string;

  /**
   * Asset ID of user who resolved it
   *
   * @generated from field: string resolved_by_asset_id = 10;
   */
  resolvedByAssetId: string;

  /**
   * ISO8601 timestamp for last edit
   *
   * @generated from field: string updated_at = 11;
   */
  updatedAt: string;

  /**
   * Constant string "COMMENT"
   *
   * @generated from field: string resource_type = 12;
   */
  resourceType: string;

  /**
   * Display name of the comment author
   *
   * @generated from field: string display_name = 13;
   */
  displayName: string;
};

/**
 * Describes the message hero.reports.v2.Comment.
 * Use `create(CommentSchema)` to create a new message.
 */
export const CommentSchema: GenMessage<Comment> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 2);

/**
 * -----------------------------------------------------------------------------
 * Narrative Content
 * -----------------------------------------------------------------------------
 * Free-text rich content section (HTML/Markdown).
 *
 * @generated from message hero.reports.v2.NarrativeContent
 */
export type NarrativeContent = Message<"hero.reports.v2.NarrativeContent"> & {
  /**
   * Unique identifier for this narrative content
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Raw rich-text blob
   *
   * @generated from field: string rich_text = 2;
   */
  richText: string;
};

/**
 * Describes the message hero.reports.v2.NarrativeContent.
 * Use `create(NarrativeContentSchema)` to create a new message.
 */
export const NarrativeContentSchema: GenMessage<NarrativeContent> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 3);

/**
 * -----------------------------------------------------------------------------
 * Offense
 * -----------------------------------------------------------------------------
 * Individual offense information within an offense content section.
 *
 * @generated from message hero.reports.v2.Offense
 */
export type Offense = Message<"hero.reports.v2.Offense"> & {
  /**
   * Unique identifier for this individual offense
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Type or classification of offense 
   *
   * @generated from field: string offense_type = 2;
   */
  offenseType: string;

  /**
   * Flexible JSON structure for additional data
   *
   * @generated from field: google.protobuf.Struct data = 3;
   */
  data?: JsonObject;

  /**
   * Schema for this specific offense
   *
   * @generated from field: google.protobuf.Struct schema = 4;
   */
  schema?: JsonObject;
};

/**
 * Describes the message hero.reports.v2.Offense.
 * Use `create(OffenseSchema)` to create a new message.
 */
export const OffenseSchema: GenMessage<Offense> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 4);

/**
 * -----------------------------------------------------------------------------
 * Offense Content
 * -----------------------------------------------------------------------------
 * Section containing multiple offenses.
 *
 * @generated from message hero.reports.v2.OffenseContent
 */
export type OffenseContent = Message<"hero.reports.v2.OffenseContent"> & {
  /**
   * Unique identifier for this offense content section
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * List of individual offenses
   *
   * @generated from field: repeated hero.reports.v2.Offense offenses = 2;
   */
  offenses: Offense[];

  /**
   * Section-level metadata
   *
   * @generated from field: google.protobuf.Struct metadata = 3;
   */
  metadata?: JsonObject;
};

/**
 * Describes the message hero.reports.v2.OffenseContent.
 * Use `create(OffenseContentSchema)` to create a new message.
 */
export const OffenseContentSchema: GenMessage<OffenseContent> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 5);

/**
 * -----------------------------------------------------------------------------
 * Entity List Content
 * -----------------------------------------------------------------------------
 * Section containing a title and references to existing entities.
 *
 * @generated from message hero.reports.v2.EntityListContent
 */
export type EntityListContent = Message<"hero.reports.v2.EntityListContent"> & {
  /**
   * Unique identifier for this entity list content
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Human-readable section header
   *
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * Links to other entities
   *
   * @generated from field: repeated hero.entity.v1.Reference entity_refs = 3;
   */
  entityRefs: Reference[];
};

/**
 * Describes the message hero.reports.v2.EntityListContent.
 * Use `create(EntityListContentSchema)` to create a new message.
 */
export const EntityListContentSchema: GenMessage<EntityListContent> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 6);

/**
 * -----------------------------------------------------------------------------
 * Incident Details Content
 * -----------------------------------------------------------------------------
 * Editable snapshot of the situation that triggered the report.
 *
 * @generated from message hero.reports.v2.IncidentResponder
 */
export type IncidentResponder = Message<"hero.reports.v2.IncidentResponder"> & {
  /**
   * Report-scoped unique identifier for this responder
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Asset ID of the responder
   *
   * @generated from field: string asset_id = 2;
   */
  assetId: string;

  /**
   * Responder's name
   *
   * @generated from field: string display_name = 3;
   */
  displayName: string;

  /**
   * Responder's role or title
   *
   * @generated from field: string role = 4;
   */
  role: string;
};

/**
 * Describes the message hero.reports.v2.IncidentResponder.
 * Use `create(IncidentResponderSchema)` to create a new message.
 */
export const IncidentResponderSchema: GenMessage<IncidentResponder> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 7);

/**
 * @generated from message hero.reports.v2.ReportingPerson
 */
export type ReportingPerson = Message<"hero.reports.v2.ReportingPerson"> & {
  /**
   * Report-scoped unique identifier for this reporting person
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Asset ID of the reporting individual
   *
   * @generated from field: string asset_id = 2;
   */
  assetId: string;

  /**
   * First name
   *
   * @generated from field: string first_name = 3;
   */
  firstName: string;

  /**
   * Middle name (optional)
   *
   * @generated from field: string middle_name = 4;
   */
  middleName: string;

  /**
   * Last name
   *
   * @generated from field: string last_name = 5;
   */
  lastName: string;

  /**
   * Contact number
   *
   * @generated from field: string phone_number = 6;
   */
  phoneNumber: string;

  /**
   * Role of the reporter (e.g., witness, victim)
   *
   * @generated from field: string reporter_role = 7;
   */
  reporterRole: string;
};

/**
 * Describes the message hero.reports.v2.ReportingPerson.
 * Use `create(ReportingPersonSchema)` to create a new message.
 */
export const ReportingPersonSchema: GenMessage<ReportingPerson> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 8);

/**
 * @generated from message hero.reports.v2.InvolvedAgency
 */
export type InvolvedAgency = Message<"hero.reports.v2.InvolvedAgency"> & {
  /**
   * unique identier for the content
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Name of the agency
   *
   * @generated from field: string agency_name = 2;
   */
  agencyName: string;

  /**
   * Role of the agency
   *
   * @generated from field: string incident_reference_number = 3;
   */
  incidentReferenceNumber: string;
};

/**
 * Describes the message hero.reports.v2.InvolvedAgency.
 * Use `create(InvolvedAgencySchema)` to create a new message.
 */
export const InvolvedAgencySchema: GenMessage<InvolvedAgency> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 9);

/**
 * @generated from message hero.reports.v2.IncidentDetailsContent
 */
export type IncidentDetailsContent = Message<"hero.reports.v2.IncidentDetailsContent"> & {
  /**
   * Unique identifier for this incident details content
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Original situation type enum
   *
   * @generated from field: hero.situations.v2.SituationType initial_type = 2;
   */
  initialType: SituationType;

  /**
   * ISO8601 start time
   *
   * @generated from field: string incident_start_time = 3;
   */
  incidentStartTime: string;

  /**
   * ISO8601 end time
   *
   * @generated from field: string incident_end_time = 4;
   */
  incidentEndTime: string;

  /**
   * ISO8601 reported time
   *
   * @generated from field: string reported_time = 5;
   */
  reportedTime: string;

  /**
   * CLERY location type (Optional)
   *
   * @generated from field: string incident_location_clery_type = 6;
   */
  incidentLocationCleryType: string;

  /**
   * Street address
   *
   * @generated from field: string incident_location_street_address = 7;
   */
  incidentLocationStreetAddress: string;

  /**
   * Apt/Suite/Unit # (Optional)
   *
   * @generated from field: string incident_location_unit_info = 8;
   */
  incidentLocationUnitInfo: string;

  /**
   * Location type (e.g., Dorm, Building, etc.)
   *
   * @generated from field: string incident_location_type = 9;
   */
  incidentLocationType: string;

  /**
   * Common name for the location (Optional)
   *
   * @generated from field: string incident_location_common_name = 10;
   */
  incidentLocationCommonName: string;

  /**
   * City
   *
   * @generated from field: string incident_location_city = 11;
   */
  incidentLocationCity: string;

  /**
   * State
   *
   * @generated from field: string incident_location_state = 12;
   */
  incidentLocationState: string;

  /**
   * Zip code
   *
   * @generated from field: string incident_location_zip_code = 13;
   */
  incidentLocationZipCode: string;

  /**
   * Country
   *
   * @generated from field: string incident_location_country = 14;
   */
  incidentLocationCountry: string;

  /**
   * Latitude coordinate
   *
   * @generated from field: double incident_location_latitude = 15;
   */
  incidentLocationLatitude: number;

  /**
   * Longitude coordinate
   *
   * @generated from field: double incident_location_longitude = 16;
   */
  incidentLocationLongitude: number;

  /**
   * List of responders
   *
   * @generated from field: repeated hero.reports.v2.IncidentResponder responders = 17;
   */
  responders: IncidentResponder[];

  /**
   * Person who reported the incident
   *
   * @generated from field: hero.reports.v2.ReportingPerson reporting_person = 18;
   */
  reportingPerson?: ReportingPerson;

  /**
   * Final situation type enum
   *
   * @generated from field: hero.situations.v2.SituationType final_type = 19;
   */
  finalType: SituationType;

  /**
   * List of involved agencies
   *
   * @generated from field: repeated hero.reports.v2.InvolvedAgency involved_agencies = 20;
   */
  involvedAgencies: InvolvedAgency[];

  /**
   * Description of the incident
   *
   * @generated from field: string description = 21;
   */
  description: string;
};

/**
 * Describes the message hero.reports.v2.IncidentDetailsContent.
 * Use `create(IncidentDetailsContentSchema)` to create a new message.
 */
export const IncidentDetailsContentSchema: GenMessage<IncidentDetailsContent> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 10);

/**
 * @generated from message hero.reports.v2.Arrest
 */
export type Arrest = Message<"hero.reports.v2.Arrest"> & {
  /**
   * Unique identifier for this individual arrest
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Type or classification of arrest
   *
   * @generated from field: string arrest_type = 2;
   */
  arrestType: string;

  /**
   * Flexible JSON structure for additional data
   *
   * @generated from field: google.protobuf.Struct data = 3;
   */
  data?: JsonObject;

  /**
   * Schema for this specific arrest
   *
   * @generated from field: google.protobuf.Struct schema = 4;
   */
  schema?: JsonObject;
};

/**
 * Describes the message hero.reports.v2.Arrest.
 * Use `create(ArrestSchema)` to create a new message.
 */
export const ArrestSchema: GenMessage<Arrest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 11);

/**
 * -----------------------------------------------------------------------------
 * Arrest Content
 * -----------------------------------------------------------------------------
 * Section containing multiple arrests.
 *
 * @generated from message hero.reports.v2.ArrestContent
 */
export type ArrestContent = Message<"hero.reports.v2.ArrestContent"> & {
  /**
   * Unique identifier for this arrest content section
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * List of individual arrests
   *
   * @generated from field: repeated hero.reports.v2.Arrest arrests = 2;
   */
  arrests: Arrest[];

  /**
   * Section-level metadata
   *
   * @generated from field: google.protobuf.Struct metadata = 3;
   */
  metadata?: JsonObject;
};

/**
 * Describes the message hero.reports.v2.ArrestContent.
 * Use `create(ArrestContentSchema)` to create a new message.
 */
export const ArrestContentSchema: GenMessage<ArrestContent> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 12);

/**
 * -----------------------------------------------------------------------------
 * File Reference
 * -----------------------------------------------------------------------------
 * Individual file reference for media sections, linking to filerepository service.
 *
 * @generated from message hero.reports.v2.FileReference
 */
export type FileReference = Message<"hero.reports.v2.FileReference"> & {
  /**
   * Unique identifier for this file reference within the section
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * FileMetadata.id from filerepository service - REQUIRED
   *
   * @generated from field: string file_id = 2;
   */
  fileId: string;

  /**
   * Optional caption/description for the file
   *
   * @generated from field: string caption = 3;
   */
  caption: string;

  /**
   * Optional display name (fallback to original filename)
   *
   * @generated from field: string display_name = 4;
   */
  displayName: string;

  /**
   * Order for displaying files in UI (0-based)
   *
   * @generated from field: int32 display_order = 5;
   */
  displayOrder: number;

  /**
   * Category of the file (e.g., "incident_photo", "incident_video", "incident_audio", "incident_document", "incident_other")
   *
   * @generated from field: string file_category = 6;
   */
  fileCategory: string;

  /**
   * Additional metadata about the file reference
   *
   * @generated from field: google.protobuf.Struct metadata = 7;
   */
  metadata?: JsonObject;
};

/**
 * Describes the message hero.reports.v2.FileReference.
 * Use `create(FileReferenceSchema)` to create a new message.
 */
export const FileReferenceSchema: GenMessage<FileReference> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 13);

/**
 * -----------------------------------------------------------------------------
 * Media Content
 * -----------------------------------------------------------------------------
 * Section containing media attachments and file references.
 *
 * @generated from message hero.reports.v2.MediaContent
 */
export type MediaContent = Message<"hero.reports.v2.MediaContent"> & {
  /**
   * Unique identifier for this media content section
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Human-readable section header
   *
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * References to files in filerepository
   *
   * @generated from field: repeated hero.reports.v2.FileReference file_refs = 3;
   */
  fileRefs: FileReference[];

  /**
   * Section-level metadata
   *
   * @generated from field: google.protobuf.Struct metadata = 4;
   */
  metadata?: JsonObject;
};

/**
 * Describes the message hero.reports.v2.MediaContent.
 * Use `create(MediaContentSchema)` to create a new message.
 */
export const MediaContentSchema: GenMessage<MediaContent> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 14);

/**
 * -----------------------------------------------------------------------------
 * Report Section
 * -----------------------------------------------------------------------------
 * A single section within a report; content is polymorphic via `oneof`.
 *
 * @generated from message hero.reports.v2.ReportSection
 */
export type ReportSection = Message<"hero.reports.v2.ReportSection"> & {
  /**
   * Unique section ID
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Determines which `oneof` field is set
   *
   * @generated from field: hero.reports.v2.SectionType type = 2;
   */
  type: SectionType;

  /**
   * Exactly one content type per section
   *
   * @generated from oneof hero.reports.v2.ReportSection.content
   */
  content: {
    /**
     * @generated from field: hero.reports.v2.NarrativeContent narrative = 3;
     */
    value: NarrativeContent;
    case: "narrative";
  } | {
    /**
     * @generated from field: hero.reports.v2.EntityListContent entity_list = 4;
     */
    value: EntityListContent;
    case: "entityList";
  } | {
    /**
     * @generated from field: hero.reports.v2.IncidentDetailsContent incident_details = 5;
     */
    value: IncidentDetailsContent;
    case: "incidentDetails";
  } | {
    /**
     * @generated from field: hero.reports.v2.OffenseContent offense_list = 6;
     */
    value: OffenseContent;
    case: "offenseList";
  } | {
    /**
     * @generated from field: hero.reports.v2.ArrestContent arrest_list = 11;
     */
    value: ArrestContent;
    case: "arrestList";
  } | {
    /**
     * @generated from field: hero.reports.v2.MediaContent media_list = 12;
     */
    value: MediaContent;
    case: "mediaList";
  } | { case: undefined; value?: undefined };

  /**
   * Comments attached to this section
   *
   * @generated from field: repeated hero.reports.v2.Comment comments = 7;
   */
  comments: Comment[];

  /**
   * ISO8601 timestamp when section was created
   *
   * @generated from field: string created_at = 8;
   */
  createdAt: string;

  /**
   * ISO8601 timestamp for last section update
   *
   * @generated from field: string updated_at = 9;
   */
  updatedAt: string;

  /**
   * Parent report ID
   *
   * @generated from field: string report_id = 10;
   */
  reportId: string;
};

/**
 * Describes the message hero.reports.v2.ReportSection.
 * Use `create(ReportSectionSchema)` to create a new message.
 */
export const ReportSectionSchema: GenMessage<ReportSection> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 15);

/**
 * -----------------------------------------------------------------------------
 * Review Round
 * -----------------------------------------------------------------------------
 * Tracks each iteration in a multi-level review workflow.
 *
 * @generated from message hero.reports.v2.ReviewRound
 */
export type ReviewRound = Message<"hero.reports.v2.ReviewRound"> & {
  /**
   * Unique review round ID
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Parent report identifier
   *
   * @generated from field: string report_id = 2;
   */
  reportId: string;

  /**
   * Who is reviewing
   *
   * @generated from field: string reviewer_asset_id = 3;
   */
  reviewerAssetId: string;

  /**
   * Hierarchy level (e.g., 1 = peer, 2 = manager)
   *
   * @generated from field: int32 level = 4;
   */
  level: number;

  /**
   * Current review state
   *
   * @generated from field: hero.reports.v2.ReviewStatus status = 5;
   */
  status: ReviewStatus;

  /**
   * Next level to route (0 returns to author)
   *
   * @generated from field: int32 sent_to_level = 6;
   */
  sentToLevel: number;

  /**
   * Specific asset for next routing
   *
   * @generated from field: string sent_to_asset_id = 7;
   */
  sentToAssetId: string;

  /**
   * ISO8601 timestamp when round was created
   *
   * @generated from field: string requested_at = 8;
   */
  requestedAt: string;

  /**
   * ISO8601 timestamp when round was finalized
   *
   * @generated from field: string resolved_at = 9;
   */
  resolvedAt: string;

  /**
   * Reviewer's comment for this round
   *
   * @generated from field: string round_note = 10;
   */
  roundNote: string;

  /**
   * Report.version at start of round
   *
   * @generated from field: int32 snapshot_version = 11;
   */
  snapshotVersion: number;

  /**
   * ISO8601 timestamp for review deadline
   *
   * @generated from field: string due_at = 12;
   */
  dueAt: string;

  /**
   * Asset ID who initiated this round
   *
   * @generated from field: string create_by_asset_id = 13;
   */
  createByAssetId: string;

  /**
   * Optional instructions/note
   *
   * @generated from field: string note_for_reviewer = 14;
   */
  noteForReviewer: string;
};

/**
 * Describes the message hero.reports.v2.ReviewRound.
 * Use `create(ReviewRoundSchema)` to create a new message.
 */
export const ReviewRoundSchema: GenMessage<ReviewRound> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 16);

/**
 * -----------------------------------------------------------------------------
 * Report
 * -----------------------------------------------------------------------------
 * Aggregates sections, comments, review rounds, and metadata.
 *
 * @generated from message hero.reports.v2.Report
 */
export type Report = Message<"hero.reports.v2.Report"> & {
  /**
   * Primary key
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Tenant/organization ID
   *
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * Who authored it
   *
   * @generated from field: string author_asset_id = 3;
   */
  authorAssetId: string;

  /**
   * User-defined title
   *
   * @generated from field: string title = 4;
   */
  title: string;

  /**
   * All sections in order
   *
   * @generated from field: repeated hero.reports.v2.ReportSection sections = 5;
   */
  sections: ReportSection[];

  /**
   * Overall report state
   *
   * @generated from field: hero.reports.v2.ReportStatus status = 6;
   */
  status: ReportStatus;

  /**
   * Historical review data
   *
   * @generated from field: repeated hero.reports.v2.ReviewRound review_rounds = 7;
   */
  reviewRounds: ReviewRound[];

  /**
   * Object relationships within the report
   *
   * @generated from field: repeated hero.reports.v2.Relation relations = 8;
   */
  relations: Relation[];

  /**
   * Global, report-level comments
   *
   * @generated from field: repeated hero.reports.v2.Comment comments = 9;
   */
  comments: Comment[];

  /**
   * ISO8601 timestamp when first assigned
   *
   * @generated from field: string assigned_at = 10;
   */
  assignedAt: string;

  /**
   * ISO8601 timestamp for last update
   *
   * @generated from field: string updated_at = 11;
   */
  updatedAt: string;

  /**
   * ISO8601 timestamp when terminal
   *
   * @generated from field: string completed_at = 12;
   */
  completedAt: string;

  /**
   * Constant string "REPORT"
   *
   * @generated from field: string resource_type = 13;
   */
  resourceType: string;

  /**
   * Arbitrary JSON key/value pairs
   *
   * @generated from field: google.protobuf.Struct additional_info_json = 14;
   */
  additionalInfoJson?: JsonObject;

  /**
   * Monotonic version number
   *
   * @generated from field: int32 version = 15;
   */
  version: number;

  /**
   * Link back to a Situation entity
   *
   * @generated from field: string situation_id = 16;
   */
  situationId: string;

  /**
   * Link back to an Incident Case
   *
   * @generated from field: string case_id = 17;
   */
  caseId: string;

  /**
   * Users subscribed for updates
   *
   * @generated from field: repeated string watcher_asset_ids = 18;
   */
  watcherAssetIds: string[];

  /**
   * ISO8601 timestamp when report was created
   *
   * @generated from field: string created_at = 19;
   */
  createdAt: string;

  /**
   * Asset ID of the report author
   *
   * @generated from field: string created_by_asset_id = 20;
   */
  createdByAssetId: string;

  /**
   * Type of the report
   *
   * @generated from field: hero.reports.v2.ReportType report_type = 21;
   */
  reportType: ReportType;
};

/**
 * Describes the message hero.reports.v2.Report.
 * Use `create(ReportSchema)` to create a new message.
 */
export const ReportSchema: GenMessage<Report> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 17);

/**
 * -----------------------------------------------------------------------------
 * Report Snapshot
 * -----------------------------------------------------------------------------
 * Immutable capture of a report at a specific version.
 *
 * @generated from message hero.reports.v2.ReportSnapshot
 */
export type ReportSnapshot = Message<"hero.reports.v2.ReportSnapshot"> & {
  /**
   * Parent report ID
   *
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * Version number
   *
   * @generated from field: int32 version = 2;
   */
  version: number;

  /**
   * Full report state
   *
   * @generated from field: hero.reports.v2.Report report = 3;
   */
  report?: Report;

  /**
   * ISO8601 timestamp when snapshot was taken
   *
   * @generated from field: string created_at = 4;
   */
  createdAt: string;
};

/**
 * Describes the message hero.reports.v2.ReportSnapshot.
 * Use `create(ReportSnapshotSchema)` to create a new message.
 */
export const ReportSnapshotSchema: GenMessage<ReportSnapshot> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 18);

/**
 * -----------------------------------------------------------------------------
 * ResolveCommentRequest
 * -----------------------------------------------------------------------------
 * Marks a comment as resolved (idempotent).
 *
 * @generated from message hero.reports.v2.ResolveCommentRequest
 */
export type ResolveCommentRequest = Message<"hero.reports.v2.ResolveCommentRequest"> & {
  /**
   * Target comment
   *
   * @generated from field: string comment_id = 1;
   */
  commentId: string;
};

/**
 * Describes the message hero.reports.v2.ResolveCommentRequest.
 * Use `create(ResolveCommentRequestSchema)` to create a new message.
 */
export const ResolveCommentRequestSchema: GenMessage<ResolveCommentRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 19);

/**
 * -----------------------------------------------------------------------------
 * Report RPC Payloads
 * -----------------------------------------------------------------------------
 *
 * @generated from message hero.reports.v2.CreateReportRequest
 */
export type CreateReportRequest = Message<"hero.reports.v2.CreateReportRequest"> & {
  /**
   * @generated from field: hero.reports.v2.Report report = 1;
   */
  report?: Report;
};

/**
 * Describes the message hero.reports.v2.CreateReportRequest.
 * Use `create(CreateReportRequestSchema)` to create a new message.
 */
export const CreateReportRequestSchema: GenMessage<CreateReportRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 20);

/**
 * @generated from message hero.reports.v2.CreateReportResponse
 */
export type CreateReportResponse = Message<"hero.reports.v2.CreateReportResponse"> & {
  /**
   * @generated from field: hero.reports.v2.Report report = 1;
   */
  report?: Report;
};

/**
 * Describes the message hero.reports.v2.CreateReportResponse.
 * Use `create(CreateReportResponseSchema)` to create a new message.
 */
export const CreateReportResponseSchema: GenMessage<CreateReportResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 21);

/**
 * @generated from message hero.reports.v2.GetReportRequest
 */
export type GetReportRequest = Message<"hero.reports.v2.GetReportRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.reports.v2.GetReportRequest.
 * Use `create(GetReportRequestSchema)` to create a new message.
 */
export const GetReportRequestSchema: GenMessage<GetReportRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 22);

/**
 * @generated from message hero.reports.v2.UpdateReportRequest
 */
export type UpdateReportRequest = Message<"hero.reports.v2.UpdateReportRequest"> & {
  /**
   * @generated from field: hero.reports.v2.Report report = 1;
   */
  report?: Report;
};

/**
 * Describes the message hero.reports.v2.UpdateReportRequest.
 * Use `create(UpdateReportRequestSchema)` to create a new message.
 */
export const UpdateReportRequestSchema: GenMessage<UpdateReportRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 23);

/**
 * @generated from message hero.reports.v2.ListReportsRequest
 */
export type ListReportsRequest = Message<"hero.reports.v2.ListReportsRequest"> & {
  /**
   * Max items per page
   *
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * Cursor for pagination
   *
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  /**
   * Filter by status (optional)
   *
   * @generated from field: hero.reports.v2.ReportStatus status = 3;
   */
  status: ReportStatus;

  /**
   * Filter by organization (optional)
   *
   * @generated from field: int32 org_id = 4;
   */
  orgId: number;
};

/**
 * Describes the message hero.reports.v2.ListReportsRequest.
 * Use `create(ListReportsRequestSchema)` to create a new message.
 */
export const ListReportsRequestSchema: GenMessage<ListReportsRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 24);

/**
 * @generated from message hero.reports.v2.ListReportsResponse
 */
export type ListReportsResponse = Message<"hero.reports.v2.ListReportsResponse"> & {
  /**
   * Page of reports
   *
   * @generated from field: repeated hero.reports.v2.Report reports = 1;
   */
  reports: Report[];

  /**
   * Cursor for next page
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.reports.v2.ListReportsResponse.
 * Use `create(ListReportsResponseSchema)` to create a new message.
 */
export const ListReportsResponseSchema: GenMessage<ListReportsResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 25);

/**
 * @generated from message hero.reports.v2.DeleteReportRequest
 */
export type DeleteReportRequest = Message<"hero.reports.v2.DeleteReportRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.reports.v2.DeleteReportRequest.
 * Use `create(DeleteReportRequestSchema)` to create a new message.
 */
export const DeleteReportRequestSchema: GenMessage<DeleteReportRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 26);

/**
 * @generated from message hero.reports.v2.UpdateReportStatusRequest
 */
export type UpdateReportStatusRequest = Message<"hero.reports.v2.UpdateReportStatusRequest"> & {
  /**
   * ID of the report to update
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * The new status to set
   *
   * @generated from field: hero.reports.v2.ReportStatus status = 2;
   */
  status: ReportStatus;
};

/**
 * Describes the message hero.reports.v2.UpdateReportStatusRequest.
 * Use `create(UpdateReportStatusRequestSchema)` to create a new message.
 */
export const UpdateReportStatusRequestSchema: GenMessage<UpdateReportStatusRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 27);

/**
 * @generated from message hero.reports.v2.UpdateReportStatusResponse
 */
export type UpdateReportStatusResponse = Message<"hero.reports.v2.UpdateReportStatusResponse"> & {
  /**
   * The updated report
   *
   * @generated from field: hero.reports.v2.Report report = 1;
   */
  report?: Report;
};

/**
 * Describes the message hero.reports.v2.UpdateReportStatusResponse.
 * Use `create(UpdateReportStatusResponseSchema)` to create a new message.
 */
export const UpdateReportStatusResponseSchema: GenMessage<UpdateReportStatusResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 28);

/**
 * Comment operations
 *
 * @generated from message hero.reports.v2.AddCommentRequest
 */
export type AddCommentRequest = Message<"hero.reports.v2.AddCommentRequest"> & {
  /**
   * @generated from field: hero.reports.v2.Comment comment = 1;
   */
  comment?: Comment;
};

/**
 * Describes the message hero.reports.v2.AddCommentRequest.
 * Use `create(AddCommentRequestSchema)` to create a new message.
 */
export const AddCommentRequestSchema: GenMessage<AddCommentRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 29);

/**
 * @generated from message hero.reports.v2.GetCommentsRequest
 */
export type GetCommentsRequest = Message<"hero.reports.v2.GetCommentsRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: string section_id = 2;
   */
  sectionId: string;

  /**
   * @generated from field: int32 page_size = 3;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 4;
   */
  pageToken: string;
};

/**
 * Describes the message hero.reports.v2.GetCommentsRequest.
 * Use `create(GetCommentsRequestSchema)` to create a new message.
 */
export const GetCommentsRequestSchema: GenMessage<GetCommentsRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 30);

/**
 * @generated from message hero.reports.v2.GetCommentsResponse
 */
export type GetCommentsResponse = Message<"hero.reports.v2.GetCommentsResponse"> & {
  /**
   * @generated from field: repeated hero.reports.v2.Comment comments = 1;
   */
  comments: Comment[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.reports.v2.GetCommentsResponse.
 * Use `create(GetCommentsResponseSchema)` to create a new message.
 */
export const GetCommentsResponseSchema: GenMessage<GetCommentsResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 31);

/**
 * @generated from message hero.reports.v2.UpdateCommentRequest
 */
export type UpdateCommentRequest = Message<"hero.reports.v2.UpdateCommentRequest"> & {
  /**
   * @generated from field: hero.reports.v2.Comment comment = 1;
   */
  comment?: Comment;
};

/**
 * Describes the message hero.reports.v2.UpdateCommentRequest.
 * Use `create(UpdateCommentRequestSchema)` to create a new message.
 */
export const UpdateCommentRequestSchema: GenMessage<UpdateCommentRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 32);

/**
 * @generated from message hero.reports.v2.DeleteCommentRequest
 */
export type DeleteCommentRequest = Message<"hero.reports.v2.DeleteCommentRequest"> & {
  /**
   * @generated from field: string comment_id = 1;
   */
  commentId: string;
};

/**
 * Describes the message hero.reports.v2.DeleteCommentRequest.
 * Use `create(DeleteCommentRequestSchema)` to create a new message.
 */
export const DeleteCommentRequestSchema: GenMessage<DeleteCommentRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 33);

/**
 * -----------------------------------------------------------------------------
 * Review Workflow Payloads
 * -----------------------------------------------------------------------------
 *
 * @generated from message hero.reports.v2.SubmitForReviewRequest
 */
export type SubmitForReviewRequest = Message<"hero.reports.v2.SubmitForReviewRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: string note = 2;
   */
  note: string;
};

/**
 * Describes the message hero.reports.v2.SubmitForReviewRequest.
 * Use `create(SubmitForReviewRequestSchema)` to create a new message.
 */
export const SubmitForReviewRequestSchema: GenMessage<SubmitForReviewRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 34);

/**
 * @generated from message hero.reports.v2.AddReviewRoundRequest
 */
export type AddReviewRoundRequest = Message<"hero.reports.v2.AddReviewRoundRequest"> & {
  /**
   * @generated from field: hero.reports.v2.ReviewRound review_round = 1;
   */
  reviewRound?: ReviewRound;
};

/**
 * Describes the message hero.reports.v2.AddReviewRoundRequest.
 * Use `create(AddReviewRoundRequestSchema)` to create a new message.
 */
export const AddReviewRoundRequestSchema: GenMessage<AddReviewRoundRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 35);

/**
 * @generated from message hero.reports.v2.GetReviewRoundRequest
 */
export type GetReviewRoundRequest = Message<"hero.reports.v2.GetReviewRoundRequest"> & {
  /**
   * @generated from field: string review_round_id = 1;
   */
  reviewRoundId: string;
};

/**
 * Describes the message hero.reports.v2.GetReviewRoundRequest.
 * Use `create(GetReviewRoundRequestSchema)` to create a new message.
 */
export const GetReviewRoundRequestSchema: GenMessage<GetReviewRoundRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 36);

/**
 * @generated from message hero.reports.v2.UpdateReviewRoundRequest
 */
export type UpdateReviewRoundRequest = Message<"hero.reports.v2.UpdateReviewRoundRequest"> & {
  /**
   * @generated from field: hero.reports.v2.ReviewRound review_round = 1;
   */
  reviewRound?: ReviewRound;
};

/**
 * Describes the message hero.reports.v2.UpdateReviewRoundRequest.
 * Use `create(UpdateReviewRoundRequestSchema)` to create a new message.
 */
export const UpdateReviewRoundRequestSchema: GenMessage<UpdateReviewRoundRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 37);

/**
 * @generated from message hero.reports.v2.DeleteReviewRoundRequest
 */
export type DeleteReviewRoundRequest = Message<"hero.reports.v2.DeleteReviewRoundRequest"> & {
  /**
   * @generated from field: string review_round_id = 1;
   */
  reviewRoundId: string;
};

/**
 * Describes the message hero.reports.v2.DeleteReviewRoundRequest.
 * Use `create(DeleteReviewRoundRequestSchema)` to create a new message.
 */
export const DeleteReviewRoundRequestSchema: GenMessage<DeleteReviewRoundRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 38);

/**
 * @generated from message hero.reports.v2.ApproveReviewRoundRequest
 */
export type ApproveReviewRoundRequest = Message<"hero.reports.v2.ApproveReviewRoundRequest"> & {
  /**
   * @generated from field: string review_round_id = 1;
   */
  reviewRoundId: string;

  /**
   * @generated from field: string note = 2;
   */
  note: string;
};

/**
 * Describes the message hero.reports.v2.ApproveReviewRoundRequest.
 * Use `create(ApproveReviewRoundRequestSchema)` to create a new message.
 */
export const ApproveReviewRoundRequestSchema: GenMessage<ApproveReviewRoundRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 39);

/**
 * @generated from message hero.reports.v2.RequestChangesRequest
 */
export type RequestChangesRequest = Message<"hero.reports.v2.RequestChangesRequest"> & {
  /**
   * @generated from field: string review_round_id = 1;
   */
  reviewRoundId: string;

  /**
   * @generated from field: string note = 2;
   */
  note: string;

  /**
   * @generated from field: int32 send_to_level = 3;
   */
  sendToLevel: number;

  /**
   * @generated from field: string send_to_asset_id = 4;
   */
  sendToAssetId: string;
};

/**
 * Describes the message hero.reports.v2.RequestChangesRequest.
 * Use `create(RequestChangesRequestSchema)` to create a new message.
 */
export const RequestChangesRequestSchema: GenMessage<RequestChangesRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 40);

/**
 * @generated from message hero.reports.v2.ListReviewRoundsForReportRequest
 */
export type ListReviewRoundsForReportRequest = Message<"hero.reports.v2.ListReviewRoundsForReportRequest"> & {
  /**
   * Target report ID
   *
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * Max items per page
   *
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * Cursor for pagination
   *
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.reports.v2.ListReviewRoundsForReportRequest.
 * Use `create(ListReviewRoundsForReportRequestSchema)` to create a new message.
 */
export const ListReviewRoundsForReportRequestSchema: GenMessage<ListReviewRoundsForReportRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 41);

/**
 * @generated from message hero.reports.v2.ListReviewRoundsForReportResponse
 */
export type ListReviewRoundsForReportResponse = Message<"hero.reports.v2.ListReviewRoundsForReportResponse"> & {
  /**
   * Page of review rounds
   *
   * @generated from field: repeated hero.reports.v2.ReviewRound review_rounds = 1;
   */
  reviewRounds: ReviewRound[];

  /**
   * Cursor for next page
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.reports.v2.ListReviewRoundsForReportResponse.
 * Use `create(ListReviewRoundsForReportResponseSchema)` to create a new message.
 */
export const ListReviewRoundsForReportResponseSchema: GenMessage<ListReviewRoundsForReportResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 42);

/**
 * -----------------------------------------------------------------------------
 * Relation Payloads
 * -----------------------------------------------------------------------------
 *
 * @generated from message hero.reports.v2.CreateRelationRequest
 */
export type CreateRelationRequest = Message<"hero.reports.v2.CreateRelationRequest"> & {
  /**
   * Target report ID
   *
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * Relation object to create
   *
   * @generated from field: hero.reports.v2.Relation relation = 2;
   */
  relation?: Relation;
};

/**
 * Describes the message hero.reports.v2.CreateRelationRequest.
 * Use `create(CreateRelationRequestSchema)` to create a new message.
 */
export const CreateRelationRequestSchema: GenMessage<CreateRelationRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 43);

/**
 * @generated from message hero.reports.v2.GetRelationRequest
 */
export type GetRelationRequest = Message<"hero.reports.v2.GetRelationRequest"> & {
  /**
   * Target report ID
   *
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * Target relation ID
   *
   * @generated from field: string relation_id = 2;
   */
  relationId: string;
};

/**
 * Describes the message hero.reports.v2.GetRelationRequest.
 * Use `create(GetRelationRequestSchema)` to create a new message.
 */
export const GetRelationRequestSchema: GenMessage<GetRelationRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 44);

/**
 * @generated from message hero.reports.v2.UpdateRelationRequest
 */
export type UpdateRelationRequest = Message<"hero.reports.v2.UpdateRelationRequest"> & {
  /**
   * Target report ID
   *
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * Relation object with updates
   *
   * @generated from field: hero.reports.v2.Relation relation = 2;
   */
  relation?: Relation;
};

/**
 * Describes the message hero.reports.v2.UpdateRelationRequest.
 * Use `create(UpdateRelationRequestSchema)` to create a new message.
 */
export const UpdateRelationRequestSchema: GenMessage<UpdateRelationRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 45);

/**
 * @generated from message hero.reports.v2.DeleteRelationRequest
 */
export type DeleteRelationRequest = Message<"hero.reports.v2.DeleteRelationRequest"> & {
  /**
   * Target report ID
   *
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * Target relation ID
   *
   * @generated from field: string relation_id = 2;
   */
  relationId: string;
};

/**
 * Describes the message hero.reports.v2.DeleteRelationRequest.
 * Use `create(DeleteRelationRequestSchema)` to create a new message.
 */
export const DeleteRelationRequestSchema: GenMessage<DeleteRelationRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 46);

/**
 * @generated from message hero.reports.v2.ListRelationsRequest
 */
export type ListRelationsRequest = Message<"hero.reports.v2.ListRelationsRequest"> & {
  /**
   * Target report ID
   *
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * Max items per page
   *
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * Cursor for pagination
   *
   * @generated from field: string page_token = 3;
   */
  pageToken: string;

  /**
   * Optional filters
   *
   * Filter by relation type
   *
   * @generated from field: string relation_type = 4;
   */
  relationType: string;

  /**
   * Filter by any object (object_a OR object_b) having this type
   *
   * @generated from field: string involves_object_type = 5;
   */
  involvesObjectType: string;

  /**
   * Filter by any object (object_a OR object_b) having this ID
   *
   * @generated from field: string involves_object_id = 6;
   */
  involvesObjectId: string;
};

/**
 * Describes the message hero.reports.v2.ListRelationsRequest.
 * Use `create(ListRelationsRequestSchema)` to create a new message.
 */
export const ListRelationsRequestSchema: GenMessage<ListRelationsRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 47);

/**
 * @generated from message hero.reports.v2.ListRelationsResponse
 */
export type ListRelationsResponse = Message<"hero.reports.v2.ListRelationsResponse"> & {
  /**
   * Page of relations
   *
   * @generated from field: repeated hero.reports.v2.Relation relations = 1;
   */
  relations: Relation[];

  /**
   * Cursor for next page
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.reports.v2.ListRelationsResponse.
 * Use `create(ListRelationsResponseSchema)` to create a new message.
 */
export const ListRelationsResponseSchema: GenMessage<ListRelationsResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 48);

/**
 * -----------------------------------------------------------------------------
 * JSON Metadata Payloads
 * -----------------------------------------------------------------------------
 *
 * @generated from message hero.reports.v2.UpdateAdditionalInfoJsonRequest
 */
export type UpdateAdditionalInfoJsonRequest = Message<"hero.reports.v2.UpdateAdditionalInfoJsonRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: string additional_info_json = 2;
   */
  additionalInfoJson: string;
};

/**
 * Describes the message hero.reports.v2.UpdateAdditionalInfoJsonRequest.
 * Use `create(UpdateAdditionalInfoJsonRequestSchema)` to create a new message.
 */
export const UpdateAdditionalInfoJsonRequestSchema: GenMessage<UpdateAdditionalInfoJsonRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 49);

/**
 * @generated from message hero.reports.v2.UpdateAdditionalInfoJsonResponse
 */
export type UpdateAdditionalInfoJsonResponse = Message<"hero.reports.v2.UpdateAdditionalInfoJsonResponse"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: string additional_info_json = 2;
   */
  additionalInfoJson: string;
};

/**
 * Describes the message hero.reports.v2.UpdateAdditionalInfoJsonResponse.
 * Use `create(UpdateAdditionalInfoJsonResponseSchema)` to create a new message.
 */
export const UpdateAdditionalInfoJsonResponseSchema: GenMessage<UpdateAdditionalInfoJsonResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 50);

/**
 * @generated from message hero.reports.v2.GetAdditionalInfoRequest
 */
export type GetAdditionalInfoRequest = Message<"hero.reports.v2.GetAdditionalInfoRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;
};

/**
 * Describes the message hero.reports.v2.GetAdditionalInfoRequest.
 * Use `create(GetAdditionalInfoRequestSchema)` to create a new message.
 */
export const GetAdditionalInfoRequestSchema: GenMessage<GetAdditionalInfoRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 51);

/**
 * @generated from message hero.reports.v2.GetAdditionalInfoResponse
 */
export type GetAdditionalInfoResponse = Message<"hero.reports.v2.GetAdditionalInfoResponse"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: string additional_info_json = 2;
   */
  additionalInfoJson: string;
};

/**
 * Describes the message hero.reports.v2.GetAdditionalInfoResponse.
 * Use `create(GetAdditionalInfoResponseSchema)` to create a new message.
 */
export const GetAdditionalInfoResponseSchema: GenMessage<GetAdditionalInfoResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 52);

/**
 * -----------------------------------------------------------------------------
 * Versioning Payloads
 * -----------------------------------------------------------------------------
 *
 * @generated from message hero.reports.v2.GetReportVersionRequest
 */
export type GetReportVersionRequest = Message<"hero.reports.v2.GetReportVersionRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: int32 version = 2;
   */
  version: number;
};

/**
 * Describes the message hero.reports.v2.GetReportVersionRequest.
 * Use `create(GetReportVersionRequestSchema)` to create a new message.
 */
export const GetReportVersionRequestSchema: GenMessage<GetReportVersionRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 53);

/**
 * @generated from message hero.reports.v2.ListReportVersionsRequest
 */
export type ListReportVersionsRequest = Message<"hero.reports.v2.ListReportVersionsRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;
};

/**
 * Describes the message hero.reports.v2.ListReportVersionsRequest.
 * Use `create(ListReportVersionsRequestSchema)` to create a new message.
 */
export const ListReportVersionsRequestSchema: GenMessage<ListReportVersionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 54);

/**
 * @generated from message hero.reports.v2.ListReportVersionsResponse
 */
export type ListReportVersionsResponse = Message<"hero.reports.v2.ListReportVersionsResponse"> & {
  /**
   * @generated from field: repeated int32 versions = 1;
   */
  versions: number[];
};

/**
 * Describes the message hero.reports.v2.ListReportVersionsResponse.
 * Use `create(ListReportVersionsResponseSchema)` to create a new message.
 */
export const ListReportVersionsResponseSchema: GenMessage<ListReportVersionsResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 55);

/**
 * @generated from message hero.reports.v2.BatchGetReportsRequest
 */
export type BatchGetReportsRequest = Message<"hero.reports.v2.BatchGetReportsRequest"> & {
  /**
   * @generated from field: repeated string report_ids = 1;
   */
  reportIds: string[];
};

/**
 * Describes the message hero.reports.v2.BatchGetReportsRequest.
 * Use `create(BatchGetReportsRequestSchema)` to create a new message.
 */
export const BatchGetReportsRequestSchema: GenMessage<BatchGetReportsRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 56);

/**
 * @generated from message hero.reports.v2.BatchGetReportsResponse
 */
export type BatchGetReportsResponse = Message<"hero.reports.v2.BatchGetReportsResponse"> & {
  /**
   * @generated from field: repeated hero.reports.v2.Report reports = 1;
   */
  reports: Report[];
};

/**
 * Describes the message hero.reports.v2.BatchGetReportsResponse.
 * Use `create(BatchGetReportsResponseSchema)` to create a new message.
 */
export const BatchGetReportsResponseSchema: GenMessage<BatchGetReportsResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 57);

/**
 * -----------------------------------------------------------------------------
 * List by Situation / Case
 * -----------------------------------------------------------------------------
 *
 * @generated from message hero.reports.v2.ListReportsBySituationIdRequest
 */
export type ListReportsBySituationIdRequest = Message<"hero.reports.v2.ListReportsBySituationIdRequest"> & {
  /**
   * Target Situation ID
   *
   * @generated from field: string situation_id = 1;
   */
  situationId: string;

  /**
   * Max items per page
   *
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * Cursor for pagination
   *
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.reports.v2.ListReportsBySituationIdRequest.
 * Use `create(ListReportsBySituationIdRequestSchema)` to create a new message.
 */
export const ListReportsBySituationIdRequestSchema: GenMessage<ListReportsBySituationIdRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 58);

/**
 * @generated from message hero.reports.v2.ListReportsByCaseIdRequest
 */
export type ListReportsByCaseIdRequest = Message<"hero.reports.v2.ListReportsByCaseIdRequest"> & {
  /**
   * Target Case ID
   *
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * Max items per page
   *
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * Cursor for pagination
   *
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.reports.v2.ListReportsByCaseIdRequest.
 * Use `create(ListReportsByCaseIdRequestSchema)` to create a new message.
 */
export const ListReportsByCaseIdRequestSchema: GenMessage<ListReportsByCaseIdRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 59);

/**
 * -----------------------------------------------------------------------------
 * Section RPC Payloads
 * -----------------------------------------------------------------------------
 *
 * @generated from message hero.reports.v2.CreateReportSectionRequest
 */
export type CreateReportSectionRequest = Message<"hero.reports.v2.CreateReportSectionRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: hero.reports.v2.ReportSection section = 2;
   */
  section?: ReportSection;
};

/**
 * Describes the message hero.reports.v2.CreateReportSectionRequest.
 * Use `create(CreateReportSectionRequestSchema)` to create a new message.
 */
export const CreateReportSectionRequestSchema: GenMessage<CreateReportSectionRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 60);

/**
 * @generated from message hero.reports.v2.GetReportSectionRequest
 */
export type GetReportSectionRequest = Message<"hero.reports.v2.GetReportSectionRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: string section_id = 2;
   */
  sectionId: string;
};

/**
 * Describes the message hero.reports.v2.GetReportSectionRequest.
 * Use `create(GetReportSectionRequestSchema)` to create a new message.
 */
export const GetReportSectionRequestSchema: GenMessage<GetReportSectionRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 61);

/**
 * @generated from message hero.reports.v2.UpdateReportSectionRequest
 */
export type UpdateReportSectionRequest = Message<"hero.reports.v2.UpdateReportSectionRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: hero.reports.v2.ReportSection section = 2;
   */
  section?: ReportSection;
};

/**
 * Describes the message hero.reports.v2.UpdateReportSectionRequest.
 * Use `create(UpdateReportSectionRequestSchema)` to create a new message.
 */
export const UpdateReportSectionRequestSchema: GenMessage<UpdateReportSectionRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 62);

/**
 * @generated from message hero.reports.v2.DeleteReportSectionRequest
 */
export type DeleteReportSectionRequest = Message<"hero.reports.v2.DeleteReportSectionRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: string section_id = 2;
   */
  sectionId: string;
};

/**
 * Describes the message hero.reports.v2.DeleteReportSectionRequest.
 * Use `create(DeleteReportSectionRequestSchema)` to create a new message.
 */
export const DeleteReportSectionRequestSchema: GenMessage<DeleteReportSectionRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 63);

/**
 * @generated from message hero.reports.v2.ListReportSectionsRequest
 */
export type ListReportSectionsRequest = Message<"hero.reports.v2.ListReportSectionsRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;
};

/**
 * Describes the message hero.reports.v2.ListReportSectionsRequest.
 * Use `create(ListReportSectionsRequestSchema)` to create a new message.
 */
export const ListReportSectionsRequestSchema: GenMessage<ListReportSectionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 64);

/**
 * @generated from message hero.reports.v2.ListReportSectionsResponse
 */
export type ListReportSectionsResponse = Message<"hero.reports.v2.ListReportSectionsResponse"> & {
  /**
   * @generated from field: repeated hero.reports.v2.ReportSection sections = 1;
   */
  sections: ReportSection[];
};

/**
 * Describes the message hero.reports.v2.ListReportSectionsResponse.
 * Use `create(ListReportSectionsResponseSchema)` to create a new message.
 */
export const ListReportSectionsResponseSchema: GenMessage<ListReportSectionsResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 65);

/**
 * / A half-open timestamp range (inclusive).
 *
 * @generated from message hero.reports.v2.DateRange
 */
export type DateRange = Message<"hero.reports.v2.DateRange"> & {
  /**
   * RFC3339 timestamp, inclusive
   *
   * @generated from field: string from = 1;
   */
  from: string;

  /**
   * RFC3339 timestamp, inclusive
   *
   * @generated from field: string to = 2;
   */
  to: string;
};

/**
 * Describes the message hero.reports.v2.DateRange.
 * Use `create(DateRangeSchema)` to create a new message.
 */
export const DateRangeSchema: GenMessage<DateRange> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 66);

/**
 * / Field-specific query (limits a search term to one field)
 *
 * @generated from message hero.reports.v2.FieldQuery
 */
export type FieldQuery = Message<"hero.reports.v2.FieldQuery"> & {
  /**
   * e.g. "title", "entity_list_title", "reporting_person_first_name"
   *
   * @generated from field: string field = 1;
   */
  field: string;

  /**
   * the term to match in that field
   *
   * @generated from field: string query = 2;
   */
  query: string;
};

/**
 * Describes the message hero.reports.v2.FieldQuery.
 * Use `create(FieldQuerySchema)` to create a new message.
 */
export const FieldQuerySchema: GenMessage<FieldQuery> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 67);

/**
 * / Highlighted fragments for a given field in each report
 *
 * @generated from message hero.reports.v2.HighlightResult
 */
export type HighlightResult = Message<"hero.reports.v2.HighlightResult"> & {
  /**
   * the field name where matches occurred
   *
   * @generated from field: string field = 1;
   */
  field: string;

  /**
   * snippets with matches, e.g. ["…urgent…", "…critical…"]
   *
   * @generated from field: repeated string fragments = 2;
   */
  fragments: string[];
};

/**
 * Describes the message hero.reports.v2.HighlightResult.
 * Use `create(HighlightResultSchema)` to create a new message.
 */
export const HighlightResultSchema: GenMessage<HighlightResult> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 68);

/**
 * / Request for searching reports, flat and non-nested.
 * /
 * / All partial/fuzzy matching (ILIKE, substring, full-text) must be done via the `query`, `search_fields`, or `field_queries` parameters.
 * /
 * / PERFORMANCE NOTE: For optimal performance, combine exact filters (status, date ranges) with full-text search.
 * / Avoid using only full-text search on large datasets without additional filters.
 * /
 * / Supported field names for `search_fields` and `field_queries.field`:
 * /   - "title"                  (report title)
 * /   - "id"                     (report id)
 * /   - "narrative"              (narrative section rich text)
 * /   - "entity_list_title"      (entity list section title)
 * /   - "incident_location"      (incident details location)
 * /   - "reporting_person_name"  (reporting person first/middle/last name)
 * /   - "reporting_person_phone_number" (reporting person phone number)
 * /   - "reporting_person_role"        (reporting person role)
 * /   - "reference_display_name" (entity reference display name)
 * /   - "responder_display_name" (incident responder display name)
 * /   - "responder_role"         (incident responder role)
 * /   - "offense_type"           (offense type/classification - searches across all offenses in offense sections)
 * /   - "relation_description"   (relation description text)
 * /   - "relation_object_name"   (relation object display names - searches both object_a and object_b)
 * /   - "agency_name"            (involved agency name)
 * /   - "agency_reference"       (involved agency incident reference number)
 * /
 * / For exact-value and range filters, use the dedicated fields below. These include DateRange, enums, and array fields for exact/range matching.
 *
 * @generated from message hero.reports.v2.SearchReportsRequest
 */
export type SearchReportsRequest = Message<"hero.reports.v2.SearchReportsRequest"> & {
  /**
   * ────── Free-text & scoped field queries ──────
   *
   * full-text / fuzzy across all indexed fields
   *
   * @generated from field: string query = 1;
   */
  query: string;

  /**
   * limit `query` to these fields; empty = all
   *
   * @generated from field: repeated string search_fields = 2;
   */
  searchFields: string[];

  /**
   * individual term→field queries
   *
   * @generated from field: repeated hero.reports.v2.FieldQuery field_queries = 3;
   */
  fieldQueries: FieldQuery[];

  /**
   * ────── Top-level report filters ──────
   *
   * @generated from field: repeated hero.reports.v2.ReportStatus status = 4;
   */
  status: ReportStatus[];

  /**
   * @generated from field: repeated string situation_ids = 5;
   */
  situationIds: string[];

  /**
   * @generated from field: repeated string case_ids = 6;
   */
  caseIds: string[];

  /**
   * reports.created_at BETWEEN …
   *
   * @generated from field: hero.reports.v2.DateRange created_at = 7;
   */
  createdAt?: DateRange;

  /**
   * reports.updated_at BETWEEN …
   *
   * @generated from field: hero.reports.v2.DateRange updated_at = 8;
   */
  updatedAt?: DateRange;

  /**
   * reports.assigned_at BETWEEN …
   *
   * @generated from field: hero.reports.v2.DateRange assigned_at = 9;
   */
  assignedAt?: DateRange;

  /**
   * reports.completed_at BETWEEN …
   *
   * @generated from field: hero.reports.v2.DateRange completed_at = 10;
   */
  completedAt?: DateRange;

  /**
   * reports.created_by_asset_id IN (…)
   *
   * @generated from field: repeated string created_by_asset_ids = 11;
   */
  createdByAssetIds: string[];

  /**
   * reports.report_type IN (…)
   *
   * @generated from field: repeated hero.reports.v2.ReportType report_types = 12;
   */
  reportTypes: ReportType[];

  /**
   * ────── Entity-list sections (exact/range) ──────
   *
   * generated ref_ids @> ARRAY[…]
   *
   * @generated from field: repeated string entity_list_ref_ids = 13;
   */
  entityListRefIds: string[];

  /**
   * generated entity_refs[].type = …
   *
   * @generated from field: string reference_type = 14;
   */
  referenceType: string;

  /**
   * ────── Incident-details sections (exact/range) ──────
   *
   * generated incident_start_t BETWEEN …
   *
   * @generated from field: hero.reports.v2.DateRange incident_start_time = 15;
   */
  incidentStartTime?: DateRange;

  /**
   * generated incident_end_t BETWEEN …
   *
   * @generated from field: hero.reports.v2.DateRange incident_end_time = 16;
   */
  incidentEndTime?: DateRange;

  /**
   * generated initial_type IN (…)
   *
   * @generated from field: repeated hero.situations.v2.SituationType initial_types = 17;
   */
  initialTypes: SituationType[];

  /**
   * generated final_type   IN (…)
   *
   * @generated from field: repeated hero.situations.v2.SituationType final_types = 18;
   */
  finalTypes: SituationType[];

  /**
   * generated responders[].asset_id IN (…)
   *
   * @generated from field: repeated string responder_asset_ids = 19;
   */
  responderAssetIds: string[];

  /**
   * generated responders[].role IN (…)
   *
   * @generated from field: repeated string responder_roles = 20;
   */
  responderRoles: string[];

  /**
   * ────── Offense sections (exact/range) ──────
   *
   * offenses[].offense_type IN (…)
   *
   * @generated from field: repeated string offense_types = 21;
   */
  offenseTypes: string[];

  /**
   * offenses[].id IN (…)
   *
   * @generated from field: repeated string offense_ids = 22;
   */
  offenseIds: string[];

  /**
   * ────── Relation filters (exact/range) ──────
   *
   * relations[].relation_type IN (…)
   *
   * @generated from field: repeated string relation_types = 23;
   */
  relationTypes: string[];

  /**
   * relations[].created_by_asset_id IN (…)
   *
   * @generated from field: repeated string relation_created_by_asset_ids = 24;
   */
  relationCreatedByAssetIds: string[];

  /**
   * relations where either object_a OR object_b has type IN (…)
   *
   * @generated from field: repeated string relation_involves_object_types = 25;
   */
  relationInvolvesObjectTypes: string[];

  /**
   * relations where either object_a OR object_b has report_scoped_id IN (…)
   *
   * @generated from field: repeated string relation_involves_report_scoped_ids = 26;
   */
  relationInvolvesReportScopedIds: string[];

  /**
   * relations where either object_a OR object_b has global_id IN (…)
   *
   * @generated from field: repeated string relation_involves_global_ids = 27;
   */
  relationInvolvesGlobalIds: string[];

  /**
   * relations where either object_a OR object_b has external_id IN (…)
   *
   * @generated from field: repeated string relation_involves_external_ids = 28;
   */
  relationInvolvesExternalIds: string[];

  /**
   * ────── Pagination & sorting ──────
   *
   * @generated from field: int32 page_size = 29;
   */
  pageSize: number;

  /**
   * cursor
   *
   * @generated from field: string page_token = 30;
   */
  pageToken: string;

  /**
   * default = RELEVANCE
   *
   * @generated from field: hero.reports.v2.SearchOrderBy order_by = 31;
   */
  orderBy: SearchOrderBy;

  /**
   * default = false (DESC)
   *
   * @generated from field: bool ascending = 32;
   */
  ascending: boolean;
};

/**
 * Describes the message hero.reports.v2.SearchReportsRequest.
 * Use `create(SearchReportsRequestSchema)` to create a new message.
 */
export const SearchReportsRequestSchema: GenMessage<SearchReportsRequest> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 69);

/**
 * @generated from message hero.reports.v2.SearchReportsResponse
 */
export type SearchReportsResponse = Message<"hero.reports.v2.SearchReportsResponse"> & {
  /**
   * The page of reports that matched the query (already ordered & trimmed).
   *
   * @generated from field: repeated hero.reports.v2.Report reports = 1;
   */
  reports: Report[];

  /**
   * Cursor for fetching the next page; empty when you're on the last page.
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;

  /**
   * Per-report highlight information keyed by report ID.
   * Each HighlightResult lists the field name and one-or-more matched fragments
   * (e.g.  "…suspect vehicle…", "…dark-blue sedan…").
   *
   * @generated from field: map<string, hero.reports.v2.HighlightResult> highlights = 3;
   */
  highlights: { [key: string]: HighlightResult };

  /**
   * Total number of hits *before* pagination—useful for UI counters.
   *
   * @generated from field: int32 total_results = 4;
   */
  totalResults: number;
};

/**
 * Describes the message hero.reports.v2.SearchReportsResponse.
 * Use `create(SearchReportsResponseSchema)` to create a new message.
 */
export const SearchReportsResponseSchema: GenMessage<SearchReportsResponse> = /*@__PURE__*/
  messageDesc(file_hero_reports_v2_reports, 70);

/**
 * -----------------------------------------------------------------------------
 * Report Status Lifecycle
 * -----------------------------------------------------------------------------
 * Defines the high-level lifecycle states of a report.
 *
 * @generated from enum hero.reports.v2.ReportStatus
 */
export enum ReportStatus {
  /**
   * Default unset state
   *
   * @generated from enum value: REPORT_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Task has been assigned to an author
   *
   * @generated from enum value: REPORT_STATUS_ASSIGNED = 1;
   */
  ASSIGNED = 1,

  /**
   * Author is actively writing the report
   *
   * @generated from enum value: REPORT_STATUS_IN_PROGRESS = 2;
   */
  IN_PROGRESS = 2,

  /**
   * Report submitted and awaiting review
   *
   * @generated from enum value: REPORT_STATUS_SUBMITTED_FOR_REVIEW = 3;
   */
  SUBMITTED_FOR_REVIEW = 3,

  /**
   * Reviewer is actively reviewing
   *
   * @generated from enum value: REPORT_STATUS_UNDER_REVIEW = 4;
   */
  UNDER_REVIEW = 4,

  /**
   * Reviewer requested changes
   *
   * @generated from enum value: REPORT_STATUS_CHANGES_REQUESTED = 5;
   */
  CHANGES_REQUESTED = 5,

  /**
   * Author is reworking requested changes
   *
   * @generated from enum value: REPORT_STATUS_IN_REWORK = 6;
   */
  IN_REWORK = 6,

  /**
   * Report approved and finalized
   *
   * @generated from enum value: REPORT_STATUS_APPROVED = 7;
   */
  APPROVED = 7,

  /**
   * Report permanently rejected
   *
   * @generated from enum value: REPORT_STATUS_REJECTED = 8;
   */
  REJECTED = 8,

  /**
   * Report process cancelled
   *
   * @generated from enum value: REPORT_STATUS_CANCELLED = 9;
   */
  CANCELLED = 9,
}

/**
 * Describes the enum hero.reports.v2.ReportStatus.
 */
export const ReportStatusSchema: GenEnum<ReportStatus> = /*@__PURE__*/
  enumDesc(file_hero_reports_v2_reports, 0);

/**
 * -----------------------------------------------------------------------------
 * Report Type
 * -----------------------------------------------------------------------------
 * Defines the different types of reports that can be created.
 *
 * @generated from enum hero.reports.v2.ReportType
 */
export enum ReportType {
  /**
   * Default unset type
   *
   * @generated from enum value: REPORT_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Primary incident report for an incident 
   *
   * @generated from enum value: REPORT_TYPE_INCIDENT_PRIMARY = 1;
   */
  INCIDENT_PRIMARY = 1,

  /**
   * Supplemental incident report for an incident
   *
   * @generated from enum value: REPORT_TYPE_INCIDENT_SUPPLEMENTAL = 2;
   */
  INCIDENT_SUPPLEMENTAL = 2,

  /**
   * Supplemental investigative report for an incident
   *
   * @generated from enum value: REPORT_TYPE_INCIDENT_SUPPLEMENTAL_INVESTIGATIVE = 3;
   */
  INCIDENT_SUPPLEMENTAL_INVESTIGATIVE = 3,
}

/**
 * Describes the enum hero.reports.v2.ReportType.
 */
export const ReportTypeSchema: GenEnum<ReportType> = /*@__PURE__*/
  enumDesc(file_hero_reports_v2_reports, 1);

/**
 * -----------------------------------------------------------------------------
 * Section Types
 * -----------------------------------------------------------------------------
 * Enumerates the different types of sections within a report.
 *
 * @generated from enum hero.reports.v2.SectionType
 */
export enum SectionType {
  /**
   * Default unset type
   *
   * @generated from enum value: SECTION_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Free-form rich text (e.g., HTML/Markdown)
   *
   * @generated from enum value: SECTION_TYPE_NARRATIVE = 1;
   */
  NARRATIVE = 1,

  /**
   * Structured incident summary
   *
   * @generated from enum value: SECTION_TYPE_INCIDENT_DETAILS = 3;
   */
  INCIDENT_DETAILS = 3,

  /**
   * List of references to people entities
   *
   * @generated from enum value: SECTION_TYPE_ENTITY_LIST_PEOPLE = 4;
   */
  ENTITY_LIST_PEOPLE = 4,

  /**
   * List of references to vehicle entities
   *
   * @generated from enum value: SECTION_TYPE_ENTITY_LIST_VEHICLE = 5;
   */
  ENTITY_LIST_VEHICLE = 5,

  /**
   * List of references to property entities
   *
   * @generated from enum value: SECTION_TYPE_ENTITY_LIST_PROPERTIES = 6;
   */
  ENTITY_LIST_PROPERTIES = 6,

  /**
   * Free-form offense information
   *
   * @generated from enum value: SECTION_TYPE_OFFENSE = 7;
   */
  OFFENSE = 7,

  /**
   * List of references to organization entities
   *
   * @generated from enum value: SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS = 8;
   */
  ENTITY_LIST_ORGANIZATIONS = 8,

  /**
   * Structured arrest details
   *
   * @generated from enum value: SECTION_TYPE_ARREST = 9;
   */
  ARREST = 9,

  /**
   * Media attachments and files
   *
   * @generated from enum value: SECTION_TYPE_MEDIA = 10;
   */
  MEDIA = 10,
}

/**
 * Describes the enum hero.reports.v2.SectionType.
 */
export const SectionTypeSchema: GenEnum<SectionType> = /*@__PURE__*/
  enumDesc(file_hero_reports_v2_reports, 2);

/**
 * -----------------------------------------------------------------------------
 * Review Status
 * -----------------------------------------------------------------------------
 * State machine for individual review rounds.
 *
 * @generated from enum hero.reports.v2.ReviewStatus
 */
export enum ReviewStatus {
  /**
   * @generated from enum value: REVIEW_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Waiting on reviewer or author
   *
   * @generated from enum value: REVIEW_STATUS_AWAITING_ACTION = 1;
   */
  AWAITING_ACTION = 1,

  /**
   * Round approved
   *
   * @generated from enum value: REVIEW_STATUS_APPROVED = 2;
   */
  APPROVED = 2,

  /**
   * Reviewer asked for changes
   *
   * @generated from enum value: REVIEW_STATUS_CHANGES_REQUESTED = 3;
   */
  CHANGES_REQUESTED = 3,
}

/**
 * Describes the enum hero.reports.v2.ReviewStatus.
 */
export const ReviewStatusSchema: GenEnum<ReviewStatus> = /*@__PURE__*/
  enumDesc(file_hero_reports_v2_reports, 3);

/**
 * / How to sort the search results
 *
 * @generated from enum hero.reports.v2.SearchOrderBy
 */
export enum SearchOrderBy {
  /**
   * @generated from enum value: SEARCH_ORDER_BY_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_RELEVANCE = 1;
   */
  RELEVANCE = 1,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_CREATED_AT = 2;
   */
  CREATED_AT = 2,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_UPDATED_AT = 3;
   */
  UPDATED_AT = 3,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_STATUS = 4;
   */
  STATUS = 4,
}

/**
 * Describes the enum hero.reports.v2.SearchOrderBy.
 */
export const SearchOrderBySchema: GenEnum<SearchOrderBy> = /*@__PURE__*/
  enumDesc(file_hero_reports_v2_reports, 4);

/**
 * -----------------------------------------------------------------------------
 * ReportService
 * -----------------------------------------------------------------------------
 *
 * @generated from service hero.reports.v2.ReportService
 */
export const ReportService: GenService<{
  /**
   * Section CRUD
   *
   * @generated from rpc hero.reports.v2.ReportService.CreateReportSection
   */
  createReportSection: {
    methodKind: "unary";
    input: typeof CreateReportSectionRequestSchema;
    output: typeof ReportSectionSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.GetReportSection
   */
  getReportSection: {
    methodKind: "unary";
    input: typeof GetReportSectionRequestSchema;
    output: typeof ReportSectionSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.UpdateReportSection
   */
  updateReportSection: {
    methodKind: "unary";
    input: typeof UpdateReportSectionRequestSchema;
    output: typeof ReportSectionSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.DeleteReportSection
   */
  deleteReportSection: {
    methodKind: "unary";
    input: typeof DeleteReportSectionRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.ListReportSections
   */
  listReportSections: {
    methodKind: "unary";
    input: typeof ListReportSectionsRequestSchema;
    output: typeof ListReportSectionsResponseSchema;
  },
  /**
   * Report CRUD & metadata
   *
   * @generated from rpc hero.reports.v2.ReportService.CreateReport
   */
  createReport: {
    methodKind: "unary";
    input: typeof CreateReportRequestSchema;
    output: typeof CreateReportResponseSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.GetReport
   */
  getReport: {
    methodKind: "unary";
    input: typeof GetReportRequestSchema;
    output: typeof ReportSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.UpdateReport
   */
  updateReport: {
    methodKind: "unary";
    input: typeof UpdateReportRequestSchema;
    output: typeof ReportSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.UpdateReportStatus
   */
  updateReportStatus: {
    methodKind: "unary";
    input: typeof UpdateReportStatusRequestSchema;
    output: typeof UpdateReportStatusResponseSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.ListReports
   */
  listReports: {
    methodKind: "unary";
    input: typeof ListReportsRequestSchema;
    output: typeof ListReportsResponseSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.BatchGetReports
   */
  batchGetReports: {
    methodKind: "unary";
    input: typeof BatchGetReportsRequestSchema;
    output: typeof BatchGetReportsResponseSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.DeleteReport
   */
  deleteReport: {
    methodKind: "unary";
    input: typeof DeleteReportRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * Filtered list RPCs
   *
   * @generated from rpc hero.reports.v2.ReportService.ListReportsBySituationId
   */
  listReportsBySituationId: {
    methodKind: "unary";
    input: typeof ListReportsBySituationIdRequestSchema;
    output: typeof ListReportsResponseSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.ListReportsByCaseId
   */
  listReportsByCaseId: {
    methodKind: "unary";
    input: typeof ListReportsByCaseIdRequestSchema;
    output: typeof ListReportsResponseSchema;
  },
  /**
   * Comments
   *
   * @generated from rpc hero.reports.v2.ReportService.AddComment
   */
  addComment: {
    methodKind: "unary";
    input: typeof AddCommentRequestSchema;
    output: typeof CommentSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.GetComments
   */
  getComments: {
    methodKind: "unary";
    input: typeof GetCommentsRequestSchema;
    output: typeof GetCommentsResponseSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.UpdateComment
   */
  updateComment: {
    methodKind: "unary";
    input: typeof UpdateCommentRequestSchema;
    output: typeof CommentSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.DeleteComment
   */
  deleteComment: {
    methodKind: "unary";
    input: typeof DeleteCommentRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.ResolveComment
   */
  resolveComment: {
    methodKind: "unary";
    input: typeof ResolveCommentRequestSchema;
    output: typeof CommentSchema;
  },
  /**
   * Review workflow
   *
   * @generated from rpc hero.reports.v2.ReportService.SubmitForReview
   */
  submitForReview: {
    methodKind: "unary";
    input: typeof SubmitForReviewRequestSchema;
    output: typeof ReportSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.AddReviewRound
   */
  addReviewRound: {
    methodKind: "unary";
    input: typeof AddReviewRoundRequestSchema;
    output: typeof ReviewRoundSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.GetReviewRound
   */
  getReviewRound: {
    methodKind: "unary";
    input: typeof GetReviewRoundRequestSchema;
    output: typeof ReviewRoundSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.UpdateReviewRound
   */
  updateReviewRound: {
    methodKind: "unary";
    input: typeof UpdateReviewRoundRequestSchema;
    output: typeof ReviewRoundSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.DeleteReviewRound
   */
  deleteReviewRound: {
    methodKind: "unary";
    input: typeof DeleteReviewRoundRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.ApproveReviewRound
   */
  approveReviewRound: {
    methodKind: "unary";
    input: typeof ApproveReviewRoundRequestSchema;
    output: typeof ReviewRoundSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.RequestChanges
   */
  requestChanges: {
    methodKind: "unary";
    input: typeof RequestChangesRequestSchema;
    output: typeof ReviewRoundSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.ListReviewRoundsForReport
   */
  listReviewRoundsForReport: {
    methodKind: "unary";
    input: typeof ListReviewRoundsForReportRequestSchema;
    output: typeof ListReviewRoundsForReportResponseSchema;
  },
  /**
   * JSON metadata
   *
   * @generated from rpc hero.reports.v2.ReportService.UpdateAdditionalInfoJson
   */
  updateAdditionalInfoJson: {
    methodKind: "unary";
    input: typeof UpdateAdditionalInfoJsonRequestSchema;
    output: typeof UpdateAdditionalInfoJsonResponseSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.GetAdditionalInfo
   */
  getAdditionalInfo: {
    methodKind: "unary";
    input: typeof GetAdditionalInfoRequestSchema;
    output: typeof GetAdditionalInfoResponseSchema;
  },
  /**
   * Versioning
   *
   * @generated from rpc hero.reports.v2.ReportService.GetReportVersion
   */
  getReportVersion: {
    methodKind: "unary";
    input: typeof GetReportVersionRequestSchema;
    output: typeof ReportSnapshotSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.ListReportVersions
   */
  listReportVersions: {
    methodKind: "unary";
    input: typeof ListReportVersionsRequestSchema;
    output: typeof ListReportVersionsResponseSchema;
  },
  /**
   * Add the new search RPC
   *
   * @generated from rpc hero.reports.v2.ReportService.SearchReports
   */
  searchReports: {
    methodKind: "unary";
    input: typeof SearchReportsRequestSchema;
    output: typeof SearchReportsResponseSchema;
  },
  /**
   * Relation CRUD
   *
   * @generated from rpc hero.reports.v2.ReportService.CreateRelation
   */
  createRelation: {
    methodKind: "unary";
    input: typeof CreateRelationRequestSchema;
    output: typeof RelationSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.GetRelation
   */
  getRelation: {
    methodKind: "unary";
    input: typeof GetRelationRequestSchema;
    output: typeof RelationSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.UpdateRelation
   */
  updateRelation: {
    methodKind: "unary";
    input: typeof UpdateRelationRequestSchema;
    output: typeof RelationSchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.DeleteRelation
   */
  deleteRelation: {
    methodKind: "unary";
    input: typeof DeleteRelationRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc hero.reports.v2.ReportService.ListRelations
   */
  listRelations: {
    methodKind: "unary";
    input: typeof ListRelationsRequestSchema;
    output: typeof ListRelationsResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_reports_v2_reports, 0);

