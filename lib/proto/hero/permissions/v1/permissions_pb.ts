// @generated by protoc-gen-es v2.6.1 with parameter "target=ts"
// @generated from file hero/permissions/v1/permissions.proto (package hero.permissions.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenExtension, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, extDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { Empty, FieldOptions, MethodOptions } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_descriptor, file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import type { Asset } from "../../assets/v2/assets_pb";
import { file_hero_assets_v2_assets } from "../../assets/v2/assets_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/permissions/v1/permissions.proto.
 */
export const file_hero_permissions_v1_permissions: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_descriptor, file_google_protobuf_empty, file_hero_assets_v2_assets]);

/**
 * Define a structured message for access control
 *
 * @generated from message hero.permissions.v1.AccessControl
 */
export type AccessControl = Message<"hero.permissions.v1.AccessControl"> & {
  /**
   * @generated from field: hero.permissions.v1.ProtectionLevel required_protection_level = 1;
   */
  requiredProtectionLevel: ProtectionLevel;
};

/**
 * Describes the message hero.permissions.v1.AccessControl.
 * Use `create(AccessControlSchema)` to create a new message.
 */
export const AccessControlSchema: GenMessage<AccessControl> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 0);

/**
 * Role definition
 *
 * @generated from message hero.permissions.v1.Role
 */
export type Role = Message<"hero.permissions.v1.Role"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: int32 org_id = 3;
   */
  orgId: number;

  /**
   * @generated from field: repeated hero.permissions.v1.PermissionCategory categories = 4;
   */
  categories: PermissionCategory[];

  /**
   * @generated from field: repeated hero.permissions.v1.PermissionSet permission_sets = 5;
   */
  permissionSets: PermissionSet[];
};

/**
 * Describes the message hero.permissions.v1.Role.
 * Use `create(RoleSchema)` to create a new message.
 */
export const RoleSchema: GenMessage<Role> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 1);

/**
 * @generated from message hero.permissions.v1.Category
 */
export type Category = Message<"hero.permissions.v1.Category"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: repeated string actions = 2;
   */
  actions: string[];
};

/**
 * Describes the message hero.permissions.v1.Category.
 * Use `create(CategorySchema)` to create a new message.
 */
export const CategorySchema: GenMessage<Category> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 2);

/**
 * @generated from message hero.permissions.v1.PermissionCategory
 */
export type PermissionCategory = Message<"hero.permissions.v1.PermissionCategory"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: bool can_do_all = 2;
   */
  canDoAll: boolean;

  /**
   * @generated from field: repeated hero.permissions.v1.Action actions = 3;
   */
  actions: Action[];
};

/**
 * Describes the message hero.permissions.v1.PermissionCategory.
 * Use `create(PermissionCategorySchema)` to create a new message.
 */
export const PermissionCategorySchema: GenMessage<PermissionCategory> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 3);

/**
 * @generated from message hero.permissions.v1.Action
 */
export type Action = Message<"hero.permissions.v1.Action"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: bool can_do_action = 2;
   */
  canDoAction: boolean;
};

/**
 * Describes the message hero.permissions.v1.Action.
 * Use `create(ActionSchema)` to create a new message.
 */
export const ActionSchema: GenMessage<Action> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 4);

/**
 * @generated from message hero.permissions.v1.DispatchRoleOnRequest
 */
export type DispatchRoleOnRequest = Message<"hero.permissions.v1.DispatchRoleOnRequest"> & {
  /**
   * @generated from field: string role_id = 1;
   */
  roleId: string;
};

/**
 * Describes the message hero.permissions.v1.DispatchRoleOnRequest.
 * Use `create(DispatchRoleOnRequestSchema)` to create a new message.
 */
export const DispatchRoleOnRequestSchema: GenMessage<DispatchRoleOnRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 5);

/**
 * @generated from message hero.permissions.v1.DispatchRoleOnResponse
 */
export type DispatchRoleOnResponse = Message<"hero.permissions.v1.DispatchRoleOnResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.DispatchRoleOnResponse.
 * Use `create(DispatchRoleOnResponseSchema)` to create a new message.
 */
export const DispatchRoleOnResponseSchema: GenMessage<DispatchRoleOnResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 6);

/**
 * @generated from message hero.permissions.v1.DispatchRoleOffRequest
 */
export type DispatchRoleOffRequest = Message<"hero.permissions.v1.DispatchRoleOffRequest"> & {
  /**
   * @generated from field: string role_id = 1;
   */
  roleId: string;
};

/**
 * Describes the message hero.permissions.v1.DispatchRoleOffRequest.
 * Use `create(DispatchRoleOffRequestSchema)` to create a new message.
 */
export const DispatchRoleOffRequestSchema: GenMessage<DispatchRoleOffRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 7);

/**
 * @generated from message hero.permissions.v1.DispatchRoleOffResponse
 */
export type DispatchRoleOffResponse = Message<"hero.permissions.v1.DispatchRoleOffResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.DispatchRoleOffResponse.
 * Use `create(DispatchRoleOffResponseSchema)` to create a new message.
 */
export const DispatchRoleOffResponseSchema: GenMessage<DispatchRoleOffResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 8);

/**
 * @generated from message hero.permissions.v1.ListSituationRolePermissionsRequest
 */
export type ListSituationRolePermissionsRequest = Message<"hero.permissions.v1.ListSituationRolePermissionsRequest"> & {
  /**
   * @generated from field: string situation_id = 1;
   */
  situationId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListSituationRolePermissionsRequest.
 * Use `create(ListSituationRolePermissionsRequestSchema)` to create a new message.
 */
export const ListSituationRolePermissionsRequestSchema: GenMessage<ListSituationRolePermissionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 9);

/**
 * @generated from message hero.permissions.v1.ListSituationRolePermissionsResponse
 */
export type ListSituationRolePermissionsResponse = Message<"hero.permissions.v1.ListSituationRolePermissionsResponse"> & {
  /**
   * @generated from field: repeated hero.permissions.v1.ObjectViewer object_viewers = 1;
   */
  objectViewers: ObjectViewer[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListSituationRolePermissionsResponse.
 * Use `create(ListSituationRolePermissionsResponseSchema)` to create a new message.
 */
export const ListSituationRolePermissionsResponseSchema: GenMessage<ListSituationRolePermissionsResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 10);

/**
 * @generated from message hero.permissions.v1.GetSituationRolePermissionRequest
 */
export type GetSituationRolePermissionRequest = Message<"hero.permissions.v1.GetSituationRolePermissionRequest"> & {
  /**
   * @generated from field: string situation_id = 1;
   */
  situationId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;
};

/**
 * Describes the message hero.permissions.v1.GetSituationRolePermissionRequest.
 * Use `create(GetSituationRolePermissionRequestSchema)` to create a new message.
 */
export const GetSituationRolePermissionRequestSchema: GenMessage<GetSituationRolePermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 11);

/**
 * @generated from message hero.permissions.v1.GetSituationRolePermissionResponse
 */
export type GetSituationRolePermissionResponse = Message<"hero.permissions.v1.GetSituationRolePermissionResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 1;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.GetSituationRolePermissionResponse.
 * Use `create(GetSituationRolePermissionResponseSchema)` to create a new message.
 */
export const GetSituationRolePermissionResponseSchema: GenMessage<GetSituationRolePermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 12);

/**
 * @generated from message hero.permissions.v1.UpdateSituationRolePermissionRequest
 */
export type UpdateSituationRolePermissionRequest = Message<"hero.permissions.v1.UpdateSituationRolePermissionRequest"> & {
  /**
   * @generated from field: string situation_id = 1;
   */
  situationId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 3;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.UpdateSituationRolePermissionRequest.
 * Use `create(UpdateSituationRolePermissionRequestSchema)` to create a new message.
 */
export const UpdateSituationRolePermissionRequestSchema: GenMessage<UpdateSituationRolePermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 13);

/**
 * @generated from message hero.permissions.v1.UpdateSituationRolePermissionResponse
 */
export type UpdateSituationRolePermissionResponse = Message<"hero.permissions.v1.UpdateSituationRolePermissionResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.UpdateSituationRolePermissionResponse.
 * Use `create(UpdateSituationRolePermissionResponseSchema)` to create a new message.
 */
export const UpdateSituationRolePermissionResponseSchema: GenMessage<UpdateSituationRolePermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 14);

/**
 * *******************************************************
 * Cases
 * *******************************************************
 *
 * @generated from message hero.permissions.v1.ListCaseRolePermissionsRequest
 */
export type ListCaseRolePermissionsRequest = Message<"hero.permissions.v1.ListCaseRolePermissionsRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListCaseRolePermissionsRequest.
 * Use `create(ListCaseRolePermissionsRequestSchema)` to create a new message.
 */
export const ListCaseRolePermissionsRequestSchema: GenMessage<ListCaseRolePermissionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 15);

/**
 * @generated from message hero.permissions.v1.ListCaseRolePermissionsResponse
 */
export type ListCaseRolePermissionsResponse = Message<"hero.permissions.v1.ListCaseRolePermissionsResponse"> & {
  /**
   * @generated from field: repeated hero.permissions.v1.ObjectViewer object_viewers = 1;
   */
  objectViewers: ObjectViewer[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListCaseRolePermissionsResponse.
 * Use `create(ListCaseRolePermissionsResponseSchema)` to create a new message.
 */
export const ListCaseRolePermissionsResponseSchema: GenMessage<ListCaseRolePermissionsResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 16);

/**
 * @generated from message hero.permissions.v1.GetCaseRolePermissionRequest
 */
export type GetCaseRolePermissionRequest = Message<"hero.permissions.v1.GetCaseRolePermissionRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;
};

/**
 * Describes the message hero.permissions.v1.GetCaseRolePermissionRequest.
 * Use `create(GetCaseRolePermissionRequestSchema)` to create a new message.
 */
export const GetCaseRolePermissionRequestSchema: GenMessage<GetCaseRolePermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 17);

/**
 * @generated from message hero.permissions.v1.GetCaseRolePermissionResponse
 */
export type GetCaseRolePermissionResponse = Message<"hero.permissions.v1.GetCaseRolePermissionResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 1;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.GetCaseRolePermissionResponse.
 * Use `create(GetCaseRolePermissionResponseSchema)` to create a new message.
 */
export const GetCaseRolePermissionResponseSchema: GenMessage<GetCaseRolePermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 18);

/**
 * @generated from message hero.permissions.v1.UpdateCaseRolePermissionRequest
 */
export type UpdateCaseRolePermissionRequest = Message<"hero.permissions.v1.UpdateCaseRolePermissionRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 3;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.UpdateCaseRolePermissionRequest.
 * Use `create(UpdateCaseRolePermissionRequestSchema)` to create a new message.
 */
export const UpdateCaseRolePermissionRequestSchema: GenMessage<UpdateCaseRolePermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 19);

/**
 * @generated from message hero.permissions.v1.UpdateCaseRolePermissionResponse
 */
export type UpdateCaseRolePermissionResponse = Message<"hero.permissions.v1.UpdateCaseRolePermissionResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.UpdateCaseRolePermissionResponse.
 * Use `create(UpdateCaseRolePermissionResponseSchema)` to create a new message.
 */
export const UpdateCaseRolePermissionResponseSchema: GenMessage<UpdateCaseRolePermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 20);

/**
 * @generated from message hero.permissions.v1.ListReportRolePermissionsRequest
 */
export type ListReportRolePermissionsRequest = Message<"hero.permissions.v1.ListReportRolePermissionsRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListReportRolePermissionsRequest.
 * Use `create(ListReportRolePermissionsRequestSchema)` to create a new message.
 */
export const ListReportRolePermissionsRequestSchema: GenMessage<ListReportRolePermissionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 21);

/**
 * @generated from message hero.permissions.v1.ListReportRolePermissionsResponse
 */
export type ListReportRolePermissionsResponse = Message<"hero.permissions.v1.ListReportRolePermissionsResponse"> & {
  /**
   * @generated from field: repeated hero.permissions.v1.ObjectViewer object_viewers = 1;
   */
  objectViewers: ObjectViewer[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListReportRolePermissionsResponse.
 * Use `create(ListReportRolePermissionsResponseSchema)` to create a new message.
 */
export const ListReportRolePermissionsResponseSchema: GenMessage<ListReportRolePermissionsResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 22);

/**
 * @generated from message hero.permissions.v1.GetReportRolePermissionRequest
 */
export type GetReportRolePermissionRequest = Message<"hero.permissions.v1.GetReportRolePermissionRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;
};

/**
 * Describes the message hero.permissions.v1.GetReportRolePermissionRequest.
 * Use `create(GetReportRolePermissionRequestSchema)` to create a new message.
 */
export const GetReportRolePermissionRequestSchema: GenMessage<GetReportRolePermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 23);

/**
 * @generated from message hero.permissions.v1.GetReportRolePermissionResponse
 */
export type GetReportRolePermissionResponse = Message<"hero.permissions.v1.GetReportRolePermissionResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 1;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.GetReportRolePermissionResponse.
 * Use `create(GetReportRolePermissionResponseSchema)` to create a new message.
 */
export const GetReportRolePermissionResponseSchema: GenMessage<GetReportRolePermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 24);

/**
 * @generated from message hero.permissions.v1.UpdateReportRolePermissionRequest
 */
export type UpdateReportRolePermissionRequest = Message<"hero.permissions.v1.UpdateReportRolePermissionRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 3;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.UpdateReportRolePermissionRequest.
 * Use `create(UpdateReportRolePermissionRequestSchema)` to create a new message.
 */
export const UpdateReportRolePermissionRequestSchema: GenMessage<UpdateReportRolePermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 25);

/**
 * @generated from message hero.permissions.v1.UpdateReportRolePermissionResponse
 */
export type UpdateReportRolePermissionResponse = Message<"hero.permissions.v1.UpdateReportRolePermissionResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.UpdateReportRolePermissionResponse.
 * Use `create(UpdateReportRolePermissionResponseSchema)` to create a new message.
 */
export const UpdateReportRolePermissionResponseSchema: GenMessage<UpdateReportRolePermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 26);

/**
 * @generated from message hero.permissions.v1.ObjectViewer
 */
export type ObjectViewer = Message<"hero.permissions.v1.ObjectViewer"> & {
  /**
   * @generated from field: string role_id = 1;
   */
  roleId: string;

  /**
   * @generated from field: string role_name = 2;
   */
  roleName: string;

  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 3;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.ObjectViewer.
 * Use `create(ObjectViewerSchema)` to create a new message.
 */
export const ObjectViewerSchema: GenMessage<ObjectViewer> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 27);

/**
 * @generated from message hero.permissions.v1.PermissionSet
 */
export type PermissionSet = Message<"hero.permissions.v1.PermissionSet"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: int32 org_id = 3;
   */
  orgId: number;

  /**
   * @generated from field: string description = 4;
   */
  description: string;

  /**
   * @generated from field: repeated hero.permissions.v1.PermissionCategory categories = 5;
   */
  categories: PermissionCategory[];
};

/**
 * Describes the message hero.permissions.v1.PermissionSet.
 * Use `create(PermissionSetSchema)` to create a new message.
 */
export const PermissionSetSchema: GenMessage<PermissionSet> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 28);

/**
 * @generated from message hero.permissions.v1.CreatePermissionSetRequest
 */
export type CreatePermissionSetRequest = Message<"hero.permissions.v1.CreatePermissionSetRequest"> & {
  /**
   * @generated from field: hero.permissions.v1.PermissionSet permission_set = 1;
   */
  permissionSet?: PermissionSet;
};

/**
 * Describes the message hero.permissions.v1.CreatePermissionSetRequest.
 * Use `create(CreatePermissionSetRequestSchema)` to create a new message.
 */
export const CreatePermissionSetRequestSchema: GenMessage<CreatePermissionSetRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 29);

/**
 * @generated from message hero.permissions.v1.CreatePermissionSetResponse
 */
export type CreatePermissionSetResponse = Message<"hero.permissions.v1.CreatePermissionSetResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.PermissionSet permission_set = 1;
   */
  permissionSet?: PermissionSet;
};

/**
 * Describes the message hero.permissions.v1.CreatePermissionSetResponse.
 * Use `create(CreatePermissionSetResponseSchema)` to create a new message.
 */
export const CreatePermissionSetResponseSchema: GenMessage<CreatePermissionSetResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 30);

/**
 * @generated from message hero.permissions.v1.GetPermissionSetRequest
 */
export type GetPermissionSetRequest = Message<"hero.permissions.v1.GetPermissionSetRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.permissions.v1.GetPermissionSetRequest.
 * Use `create(GetPermissionSetRequestSchema)` to create a new message.
 */
export const GetPermissionSetRequestSchema: GenMessage<GetPermissionSetRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 31);

/**
 * @generated from message hero.permissions.v1.GetPermissionSetResponse
 */
export type GetPermissionSetResponse = Message<"hero.permissions.v1.GetPermissionSetResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.PermissionSet permission_set = 1;
   */
  permissionSet?: PermissionSet;
};

/**
 * Describes the message hero.permissions.v1.GetPermissionSetResponse.
 * Use `create(GetPermissionSetResponseSchema)` to create a new message.
 */
export const GetPermissionSetResponseSchema: GenMessage<GetPermissionSetResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 32);

/**
 * @generated from message hero.permissions.v1.ListPermissionSetsRequest
 */
export type ListPermissionSetsRequest = Message<"hero.permissions.v1.ListPermissionSetsRequest"> & {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListPermissionSetsRequest.
 * Use `create(ListPermissionSetsRequestSchema)` to create a new message.
 */
export const ListPermissionSetsRequestSchema: GenMessage<ListPermissionSetsRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 33);

/**
 * @generated from message hero.permissions.v1.ListPermissionSetsResponse
 */
export type ListPermissionSetsResponse = Message<"hero.permissions.v1.ListPermissionSetsResponse"> & {
  /**
   * @generated from field: repeated hero.permissions.v1.PermissionSet permission_sets = 1;
   */
  permissionSets: PermissionSet[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListPermissionSetsResponse.
 * Use `create(ListPermissionSetsResponseSchema)` to create a new message.
 */
export const ListPermissionSetsResponseSchema: GenMessage<ListPermissionSetsResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 34);

/**
 * @generated from message hero.permissions.v1.UpdatePermissionSetRequest
 */
export type UpdatePermissionSetRequest = Message<"hero.permissions.v1.UpdatePermissionSetRequest"> & {
  /**
   * @generated from field: hero.permissions.v1.PermissionSet permission_set = 1;
   */
  permissionSet?: PermissionSet;
};

/**
 * Describes the message hero.permissions.v1.UpdatePermissionSetRequest.
 * Use `create(UpdatePermissionSetRequestSchema)` to create a new message.
 */
export const UpdatePermissionSetRequestSchema: GenMessage<UpdatePermissionSetRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 35);

/**
 * @generated from message hero.permissions.v1.UpdatePermissionSetResponse
 */
export type UpdatePermissionSetResponse = Message<"hero.permissions.v1.UpdatePermissionSetResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.PermissionSet permission_set = 1;
   */
  permissionSet?: PermissionSet;
};

/**
 * Describes the message hero.permissions.v1.UpdatePermissionSetResponse.
 * Use `create(UpdatePermissionSetResponseSchema)` to create a new message.
 */
export const UpdatePermissionSetResponseSchema: GenMessage<UpdatePermissionSetResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 36);

/**
 * @generated from message hero.permissions.v1.DeletePermissionSetRequest
 */
export type DeletePermissionSetRequest = Message<"hero.permissions.v1.DeletePermissionSetRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.permissions.v1.DeletePermissionSetRequest.
 * Use `create(DeletePermissionSetRequestSchema)` to create a new message.
 */
export const DeletePermissionSetRequestSchema: GenMessage<DeletePermissionSetRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 37);

/**
 * @generated from message hero.permissions.v1.DeletePermissionSetResponse
 */
export type DeletePermissionSetResponse = Message<"hero.permissions.v1.DeletePermissionSetResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.DeletePermissionSetResponse.
 * Use `create(DeletePermissionSetResponseSchema)` to create a new message.
 */
export const DeletePermissionSetResponseSchema: GenMessage<DeletePermissionSetResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 38);

/**
 * @generated from message hero.permissions.v1.ListActionsByCategoryRequest
 */
export type ListActionsByCategoryRequest = Message<"hero.permissions.v1.ListActionsByCategoryRequest"> & {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListActionsByCategoryRequest.
 * Use `create(ListActionsByCategoryRequestSchema)` to create a new message.
 */
export const ListActionsByCategoryRequestSchema: GenMessage<ListActionsByCategoryRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 39);

/**
 * @generated from message hero.permissions.v1.ListActionsByCategoryResponse
 */
export type ListActionsByCategoryResponse = Message<"hero.permissions.v1.ListActionsByCategoryResponse"> & {
  /**
   * @generated from field: repeated hero.permissions.v1.Category categories = 1;
   */
  categories: Category[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListActionsByCategoryResponse.
 * Use `create(ListActionsByCategoryResponseSchema)` to create a new message.
 */
export const ListActionsByCategoryResponseSchema: GenMessage<ListActionsByCategoryResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 40);

/**
 * @generated from message hero.permissions.v1.ListRoleAssetsRequest
 */
export type ListRoleAssetsRequest = Message<"hero.permissions.v1.ListRoleAssetsRequest"> & {
  /**
   * @generated from field: string role_id = 1;
   */
  roleId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListRoleAssetsRequest.
 * Use `create(ListRoleAssetsRequestSchema)` to create a new message.
 */
export const ListRoleAssetsRequestSchema: GenMessage<ListRoleAssetsRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 41);

/**
 * @generated from message hero.permissions.v1.ListRoleAssetsResponse
 */
export type ListRoleAssetsResponse = Message<"hero.permissions.v1.ListRoleAssetsResponse"> & {
  /**
   * @generated from field: repeated hero.assets.v2.Asset assets = 1;
   */
  assets: Asset[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListRoleAssetsResponse.
 * Use `create(ListRoleAssetsResponseSchema)` to create a new message.
 */
export const ListRoleAssetsResponseSchema: GenMessage<ListRoleAssetsResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 42);

/**
 * @generated from message hero.permissions.v1.GetAssetRolesRequest
 */
export type GetAssetRolesRequest = Message<"hero.permissions.v1.GetAssetRolesRequest"> & {
  /**
   * @generated from field: string asset_id = 1;
   */
  assetId: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;
};

/**
 * Describes the message hero.permissions.v1.GetAssetRolesRequest.
 * Use `create(GetAssetRolesRequestSchema)` to create a new message.
 */
export const GetAssetRolesRequestSchema: GenMessage<GetAssetRolesRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 43);

/**
 * @generated from message hero.permissions.v1.GetAssetRolesResponse
 */
export type GetAssetRolesResponse = Message<"hero.permissions.v1.GetAssetRolesResponse"> & {
  /**
   * @generated from field: repeated string roles = 1;
   */
  roles: string[];
};

/**
 * Describes the message hero.permissions.v1.GetAssetRolesResponse.
 * Use `create(GetAssetRolesResponseSchema)` to create a new message.
 */
export const GetAssetRolesResponseSchema: GenMessage<GetAssetRolesResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 44);

/**
 * @generated from message hero.permissions.v1.ListRolesRequest
 */
export type ListRolesRequest = Message<"hero.permissions.v1.ListRolesRequest"> & {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListRolesRequest.
 * Use `create(ListRolesRequestSchema)` to create a new message.
 */
export const ListRolesRequestSchema: GenMessage<ListRolesRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 45);

/**
 * @generated from message hero.permissions.v1.ListRolesResponse
 */
export type ListRolesResponse = Message<"hero.permissions.v1.ListRolesResponse"> & {
  /**
   * @generated from field: repeated hero.permissions.v1.Role roles = 1;
   */
  roles: Role[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListRolesResponse.
 * Use `create(ListRolesResponseSchema)` to create a new message.
 */
export const ListRolesResponseSchema: GenMessage<ListRolesResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 46);

/**
 * @generated from message hero.permissions.v1.CreateRoleRequest
 */
export type CreateRoleRequest = Message<"hero.permissions.v1.CreateRoleRequest"> & {
  /**
   * @generated from field: hero.permissions.v1.Role role = 1;
   */
  role?: Role;
};

/**
 * Describes the message hero.permissions.v1.CreateRoleRequest.
 * Use `create(CreateRoleRequestSchema)` to create a new message.
 */
export const CreateRoleRequestSchema: GenMessage<CreateRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 47);

/**
 * @generated from message hero.permissions.v1.CreateRoleResponse
 */
export type CreateRoleResponse = Message<"hero.permissions.v1.CreateRoleResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.Role role = 1;
   */
  role?: Role;
};

/**
 * Describes the message hero.permissions.v1.CreateRoleResponse.
 * Use `create(CreateRoleResponseSchema)` to create a new message.
 */
export const CreateRoleResponseSchema: GenMessage<CreateRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 48);

/**
 * @generated from message hero.permissions.v1.GetRoleRequest
 */
export type GetRoleRequest = Message<"hero.permissions.v1.GetRoleRequest"> & {
  /**
   * @generated from field: string role_id = 1;
   */
  roleId: string;
};

/**
 * Describes the message hero.permissions.v1.GetRoleRequest.
 * Use `create(GetRoleRequestSchema)` to create a new message.
 */
export const GetRoleRequestSchema: GenMessage<GetRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 49);

/**
 * @generated from message hero.permissions.v1.GetRoleResponse
 */
export type GetRoleResponse = Message<"hero.permissions.v1.GetRoleResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.Role role = 1;
   */
  role?: Role;
};

/**
 * Describes the message hero.permissions.v1.GetRoleResponse.
 * Use `create(GetRoleResponseSchema)` to create a new message.
 */
export const GetRoleResponseSchema: GenMessage<GetRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 50);

/**
 * @generated from message hero.permissions.v1.UpdateRoleRequest
 */
export type UpdateRoleRequest = Message<"hero.permissions.v1.UpdateRoleRequest"> & {
  /**
   * @generated from field: hero.permissions.v1.Role role = 1;
   */
  role?: Role;
};

/**
 * Describes the message hero.permissions.v1.UpdateRoleRequest.
 * Use `create(UpdateRoleRequestSchema)` to create a new message.
 */
export const UpdateRoleRequestSchema: GenMessage<UpdateRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 51);

/**
 * @generated from message hero.permissions.v1.UpdateRoleResponse
 */
export type UpdateRoleResponse = Message<"hero.permissions.v1.UpdateRoleResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.Role role = 1;
   */
  role?: Role;
};

/**
 * Describes the message hero.permissions.v1.UpdateRoleResponse.
 * Use `create(UpdateRoleResponseSchema)` to create a new message.
 */
export const UpdateRoleResponseSchema: GenMessage<UpdateRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 52);

/**
 * @generated from message hero.permissions.v1.DeleteRoleRequest
 */
export type DeleteRoleRequest = Message<"hero.permissions.v1.DeleteRoleRequest"> & {
  /**
   * @generated from field: string role_id = 1;
   */
  roleId: string;
};

/**
 * Describes the message hero.permissions.v1.DeleteRoleRequest.
 * Use `create(DeleteRoleRequestSchema)` to create a new message.
 */
export const DeleteRoleRequestSchema: GenMessage<DeleteRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 53);

/**
 * @generated from message hero.permissions.v1.DeleteRoleResponse
 */
export type DeleteRoleResponse = Message<"hero.permissions.v1.DeleteRoleResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.DeleteRoleResponse.
 * Use `create(DeleteRoleResponseSchema)` to create a new message.
 */
export const DeleteRoleResponseSchema: GenMessage<DeleteRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 54);

/**
 * @generated from message hero.permissions.v1.GetUserRolesRequest
 */
export type GetUserRolesRequest = Message<"hero.permissions.v1.GetUserRolesRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;
};

/**
 * Describes the message hero.permissions.v1.GetUserRolesRequest.
 * Use `create(GetUserRolesRequestSchema)` to create a new message.
 */
export const GetUserRolesRequestSchema: GenMessage<GetUserRolesRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 55);

/**
 * @generated from message hero.permissions.v1.GetUserRolesResponse
 */
export type GetUserRolesResponse = Message<"hero.permissions.v1.GetUserRolesResponse"> & {
  /**
   * @generated from field: repeated string roles = 1;
   */
  roles: string[];
};

/**
 * Describes the message hero.permissions.v1.GetUserRolesResponse.
 * Use `create(GetUserRolesResponseSchema)` to create a new message.
 */
export const GetUserRolesResponseSchema: GenMessage<GetUserRolesResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 56);

/**
 * @generated from message hero.permissions.v1.AddAssetToRoleRequest
 */
export type AddAssetToRoleRequest = Message<"hero.permissions.v1.AddAssetToRoleRequest"> & {
  /**
   * @generated from field: string asset_id = 1;
   */
  assetId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * @generated from field: string role_name = 3;
   */
  roleName: string;

  /**
   * @generated from field: int32 org_id_override = 4;
   */
  orgIdOverride: number;
};

/**
 * Describes the message hero.permissions.v1.AddAssetToRoleRequest.
 * Use `create(AddAssetToRoleRequestSchema)` to create a new message.
 */
export const AddAssetToRoleRequestSchema: GenMessage<AddAssetToRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 57);

/**
 * @generated from message hero.permissions.v1.AddAssetToRoleResponse
 */
export type AddAssetToRoleResponse = Message<"hero.permissions.v1.AddAssetToRoleResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.AddAssetToRoleResponse.
 * Use `create(AddAssetToRoleResponseSchema)` to create a new message.
 */
export const AddAssetToRoleResponseSchema: GenMessage<AddAssetToRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 58);

/**
 * @generated from message hero.permissions.v1.AddCognitoUserToRoleRequest
 */
export type AddCognitoUserToRoleRequest = Message<"hero.permissions.v1.AddCognitoUserToRoleRequest"> & {
  /**
   * @generated from field: string cognito_sub_id = 1;
   */
  cognitoSubId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * @generated from field: string role_name = 3;
   */
  roleName: string;

  /**
   * @generated from field: int32 org_id_override = 4;
   */
  orgIdOverride: number;
};

/**
 * Describes the message hero.permissions.v1.AddCognitoUserToRoleRequest.
 * Use `create(AddCognitoUserToRoleRequestSchema)` to create a new message.
 */
export const AddCognitoUserToRoleRequestSchema: GenMessage<AddCognitoUserToRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 59);

/**
 * @generated from message hero.permissions.v1.AddCognitoUserToRoleResponse
 */
export type AddCognitoUserToRoleResponse = Message<"hero.permissions.v1.AddCognitoUserToRoleResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.AddCognitoUserToRoleResponse.
 * Use `create(AddCognitoUserToRoleResponseSchema)` to create a new message.
 */
export const AddCognitoUserToRoleResponseSchema: GenMessage<AddCognitoUserToRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 60);

/**
 * @generated from message hero.permissions.v1.AddUserToRoleRequest
 */
export type AddUserToRoleRequest = Message<"hero.permissions.v1.AddUserToRoleRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * @generated from field: string role_name = 3;
   */
  roleName: string;

  /**
   * @generated from field: int32 org_id_override = 4;
   */
  orgIdOverride: number;
};

/**
 * Describes the message hero.permissions.v1.AddUserToRoleRequest.
 * Use `create(AddUserToRoleRequestSchema)` to create a new message.
 */
export const AddUserToRoleRequestSchema: GenMessage<AddUserToRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 61);

/**
 * @generated from message hero.permissions.v1.RemoveUserFromRoleRequest
 */
export type RemoveUserFromRoleRequest = Message<"hero.permissions.v1.RemoveUserFromRoleRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * @generated from field: string role_name = 3;
   */
  roleName: string;
};

/**
 * Describes the message hero.permissions.v1.RemoveUserFromRoleRequest.
 * Use `create(RemoveUserFromRoleRequestSchema)` to create a new message.
 */
export const RemoveUserFromRoleRequestSchema: GenMessage<RemoveUserFromRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 62);

/**
 * @generated from message hero.permissions.v1.AddUserToRoleResponse
 */
export type AddUserToRoleResponse = Message<"hero.permissions.v1.AddUserToRoleResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.AddUserToRoleResponse.
 * Use `create(AddUserToRoleResponseSchema)` to create a new message.
 */
export const AddUserToRoleResponseSchema: GenMessage<AddUserToRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 63);

/**
 * @generated from message hero.permissions.v1.RemoveUserFromRoleResponse
 */
export type RemoveUserFromRoleResponse = Message<"hero.permissions.v1.RemoveUserFromRoleResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.RemoveUserFromRoleResponse.
 * Use `create(RemoveUserFromRoleResponseSchema)` to create a new message.
 */
export const RemoveUserFromRoleResponseSchema: GenMessage<RemoveUserFromRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 64);

/**
 * @generated from message hero.permissions.v1.RemoveAssetFromRoleRequest
 */
export type RemoveAssetFromRoleRequest = Message<"hero.permissions.v1.RemoveAssetFromRoleRequest"> & {
  /**
   * @generated from field: string asset_id = 1;
   */
  assetId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;
};

/**
 * Describes the message hero.permissions.v1.RemoveAssetFromRoleRequest.
 * Use `create(RemoveAssetFromRoleRequestSchema)` to create a new message.
 */
export const RemoveAssetFromRoleRequestSchema: GenMessage<RemoveAssetFromRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 65);

/**
 * @generated from message hero.permissions.v1.RemoveAssetFromRoleResponse
 */
export type RemoveAssetFromRoleResponse = Message<"hero.permissions.v1.RemoveAssetFromRoleResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.RemoveAssetFromRoleResponse.
 * Use `create(RemoveAssetFromRoleResponseSchema)` to create a new message.
 */
export const RemoveAssetFromRoleResponseSchema: GenMessage<RemoveAssetFromRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 66);

/**
 * @generated from message hero.permissions.v1.DeleteAssetRequest
 */
export type DeleteAssetRequest = Message<"hero.permissions.v1.DeleteAssetRequest"> & {
  /**
   * @generated from field: string asset_id = 1;
   */
  assetId: string;
};

/**
 * Describes the message hero.permissions.v1.DeleteAssetRequest.
 * Use `create(DeleteAssetRequestSchema)` to create a new message.
 */
export const DeleteAssetRequestSchema: GenMessage<DeleteAssetRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 67);

/**
 * @generated from message hero.permissions.v1.DeleteAssetResponse
 */
export type DeleteAssetResponse = Message<"hero.permissions.v1.DeleteAssetResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.DeleteAssetResponse.
 * Use `create(DeleteAssetResponseSchema)` to create a new message.
 */
export const DeleteAssetResponseSchema: GenMessage<DeleteAssetResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 68);

/**
 * @generated from message hero.permissions.v1.CheckPermissionRequest
 */
export type CheckPermissionRequest = Message<"hero.permissions.v1.CheckPermissionRequest"> & {
  /**
   * @generated from field: string category = 1;
   */
  category: string;

  /**
   * @generated from field: string action = 2;
   */
  action: string;

  /**
   * @generated from field: string object_id = 3;
   */
  objectId: string;
};

/**
 * Describes the message hero.permissions.v1.CheckPermissionRequest.
 * Use `create(CheckPermissionRequestSchema)` to create a new message.
 */
export const CheckPermissionRequestSchema: GenMessage<CheckPermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 69);

/**
 * @generated from message hero.permissions.v1.CheckPermissionResponse
 */
export type CheckPermissionResponse = Message<"hero.permissions.v1.CheckPermissionResponse"> & {
  /**
   * @generated from field: bool allowed = 1;
   */
  allowed: boolean;
};

/**
 * Describes the message hero.permissions.v1.CheckPermissionResponse.
 * Use `create(CheckPermissionResponseSchema)` to create a new message.
 */
export const CheckPermissionResponseSchema: GenMessage<CheckPermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 70);

/**
 * @generated from message hero.permissions.v1.BatchCheckPermissionRequest
 */
export type BatchCheckPermissionRequest = Message<"hero.permissions.v1.BatchCheckPermissionRequest"> & {
  /**
   * @generated from field: string category = 1;
   */
  category: string;

  /**
   * @generated from field: string action = 2;
   */
  action: string;

  /**
   * @generated from field: repeated string object_ids = 3;
   */
  objectIds: string[];
};

/**
 * Describes the message hero.permissions.v1.BatchCheckPermissionRequest.
 * Use `create(BatchCheckPermissionRequestSchema)` to create a new message.
 */
export const BatchCheckPermissionRequestSchema: GenMessage<BatchCheckPermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 71);

/**
 * @generated from message hero.permissions.v1.BatchCheckPermissionResponse
 */
export type BatchCheckPermissionResponse = Message<"hero.permissions.v1.BatchCheckPermissionResponse"> & {
  /**
   * @generated from field: map<string, bool> results = 1;
   */
  results: { [key: string]: boolean };
};

/**
 * Describes the message hero.permissions.v1.BatchCheckPermissionResponse.
 * Use `create(BatchCheckPermissionResponseSchema)` to create a new message.
 */
export const BatchCheckPermissionResponseSchema: GenMessage<BatchCheckPermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 72);

/**
 * Define an enum for roles
 *
 * @generated from enum hero.permissions.v1.ProtectionLevel
 */
export enum ProtectionLevel {
  /**
   * @generated from enum value: PROTECTION_LEVEL_UNSPECIFIED = 0;
   */
  PROTECTION_LEVEL_UNSPECIFIED = 0,

  /**
   * Open to all authenticated users. Currently only used for meta permission endpoints (e.g. CheckPermission)
   *
   * @generated from enum value: OPEN = 1;
   */
  OPEN = 1,

  /**
   * Only authenticated users with the necessary permissions can access (e.g. PermissionService decides)
   *
   * @generated from enum value: PERMISSIONED = 2;
   */
  PERMISSIONED = 2,
}

/**
 * Describes the enum hero.permissions.v1.ProtectionLevel.
 */
export const ProtectionLevelSchema: GenEnum<ProtectionLevel> = /*@__PURE__*/
  enumDesc(file_hero_permissions_v1_permissions, 0);

/**
 * @generated from enum hero.permissions.v1.ObjectPermission
 */
export enum ObjectPermission {
  /**
   * @generated from enum value: OBJECT_PERMISSION_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: OBJECT_PERMISSION_CAN_MANAGE = 1;
   */
  CAN_MANAGE = 1,

  /**
   * @generated from enum value: OBJECT_PERMISSION_CAN_EDIT = 2;
   */
  CAN_EDIT = 2,

  /**
   * @generated from enum value: OBJECT_PERMISSION_CAN_VIEW = 3;
   */
  CAN_VIEW = 3,

  /**
   * @generated from enum value: OBJECT_PERMISSION_CAN_FIND = 4;
   */
  CAN_FIND = 4,

  /**
   * @generated from enum value: OBJECT_PERMISSION_BLOCKED = 5;
   */
  BLOCKED = 5,
}

/**
 * Describes the enum hero.permissions.v1.ObjectPermission.
 */
export const ObjectPermissionSchema: GenEnum<ObjectPermission> = /*@__PURE__*/
  enumDesc(file_hero_permissions_v1_permissions, 1);

/**
 * @generated from enum hero.permissions.v1.ObjectType
 */
export enum ObjectType {
  /**
   * @generated from enum value: OBJECT_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: OBJECT_TYPE_REPORT = 1;
   */
  REPORT = 1,

  /**
   * @generated from enum value: OBJECT_TYPE_SITUATION = 2;
   */
  SITUATION = 2,

  /**
   * @generated from enum value: OBJECT_TYPE_CASE = 3;
   */
  CASE = 3,

  /**
   * @generated from enum value: OBJECT_TYPE_FILE = 4;
   */
  FILE = 4,
}

/**
 * Describes the enum hero.permissions.v1.ObjectType.
 */
export const ObjectTypeSchema: GenEnum<ObjectType> = /*@__PURE__*/
  enumDesc(file_hero_permissions_v1_permissions, 2);

/**
 * === Service for managing user roles and checking permissions ===
 *
 * @generated from service hero.permissions.v1.PermissionService
 */
export const PermissionService: GenService<{
  /**
   * # Permission Checking
   *
   * @generated from rpc hero.permissions.v1.PermissionService.CheckPermission
   */
  checkPermission: {
    methodKind: "unary";
    input: typeof CheckPermissionRequestSchema;
    output: typeof CheckPermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.BatchCheckPermission
   */
  batchCheckPermission: {
    methodKind: "unary";
    input: typeof BatchCheckPermissionRequestSchema;
    output: typeof BatchCheckPermissionResponseSchema;
  },
  /**
   * List out available actions, organized by category
   * Used for role creation and permission set creation
   *
   * @generated from rpc hero.permissions.v1.PermissionService.ListActionsByCategory
   */
  listActionsByCategory: {
    methodKind: "unary";
    input: typeof ListActionsByCategoryRequestSchema;
    output: typeof ListActionsByCategoryResponseSchema;
  },
  /**
   * # Role Management
   *
   * @generated from rpc hero.permissions.v1.PermissionService.CreateRole
   */
  createRole: {
    methodKind: "unary";
    input: typeof CreateRoleRequestSchema;
    output: typeof CreateRoleResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.GetRole
   */
  getRole: {
    methodKind: "unary";
    input: typeof GetRoleRequestSchema;
    output: typeof GetRoleResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.ListRoles
   */
  listRoles: {
    methodKind: "unary";
    input: typeof ListRolesRequestSchema;
    output: typeof ListRolesResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.UpdateRole
   */
  updateRole: {
    methodKind: "unary";
    input: typeof UpdateRoleRequestSchema;
    output: typeof UpdateRoleResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.DeleteRole
   */
  deleteRole: {
    methodKind: "unary";
    input: typeof DeleteRoleRequestSchema;
    output: typeof DeleteRoleResponseSchema;
  },
  /**
   * # Permission Set Management
   *
   * @generated from rpc hero.permissions.v1.PermissionService.CreatePermissionSet
   */
  createPermissionSet: {
    methodKind: "unary";
    input: typeof CreatePermissionSetRequestSchema;
    output: typeof CreatePermissionSetResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.GetPermissionSet
   */
  getPermissionSet: {
    methodKind: "unary";
    input: typeof GetPermissionSetRequestSchema;
    output: typeof GetPermissionSetResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.ListPermissionSets
   */
  listPermissionSets: {
    methodKind: "unary";
    input: typeof ListPermissionSetsRequestSchema;
    output: typeof ListPermissionSetsResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.UpdatePermissionSet
   */
  updatePermissionSet: {
    methodKind: "unary";
    input: typeof UpdatePermissionSetRequestSchema;
    output: typeof UpdatePermissionSetResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.DeletePermissionSet
   */
  deletePermissionSet: {
    methodKind: "unary";
    input: typeof DeletePermissionSetRequestSchema;
    output: typeof DeletePermissionSetResponseSchema;
  },
  /**
   * # User Role Management
   * Get all assets for a role
   *
   * @generated from rpc hero.permissions.v1.PermissionService.ListRoleAssets
   */
  listRoleAssets: {
    methodKind: "unary";
    input: typeof ListRoleAssetsRequestSchema;
    output: typeof ListRoleAssetsResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.GetAssetRoles
   */
  getAssetRoles: {
    methodKind: "unary";
    input: typeof GetAssetRolesRequestSchema;
    output: typeof GetAssetRolesResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.AddAssetToRole
   */
  addAssetToRole: {
    methodKind: "unary";
    input: typeof AddAssetToRoleRequestSchema;
    output: typeof AddAssetToRoleResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.RemoveAssetFromRole
   */
  removeAssetFromRole: {
    methodKind: "unary";
    input: typeof RemoveAssetFromRoleRequestSchema;
    output: typeof RemoveAssetFromRoleResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.DeleteAsset
   */
  deleteAsset: {
    methodKind: "unary";
    input: typeof DeleteAssetRequestSchema;
    output: typeof DeleteAssetResponseSchema;
  },
  /**
   * // slightly lower level than AddAssetToRole, currently used for twilio webhook and other bots
   *
   * @generated from rpc hero.permissions.v1.PermissionService.AddUserToRole
   */
  addUserToRole: {
    methodKind: "unary";
    input: typeof AddUserToRoleRequestSchema;
    output: typeof AddUserToRoleResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.RemoveUserFromRole
   */
  removeUserFromRole: {
    methodKind: "unary";
    input: typeof RemoveUserFromRoleRequestSchema;
    output: typeof RemoveUserFromRoleResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.GetUserRoles
   */
  getUserRoles: {
    methodKind: "unary";
    input: typeof GetUserRolesRequestSchema;
    output: typeof GetUserRolesResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.AddCognitoUserToRole
   */
  addCognitoUserToRole: {
    methodKind: "unary";
    input: typeof AddCognitoUserToRoleRequestSchema;
    output: typeof AddCognitoUserToRoleResponseSchema;
  },
  /**
   * # Object Override Management
   * REPORTS
   *
   * @generated from rpc hero.permissions.v1.PermissionService.GetReportRolePermission
   */
  getReportRolePermission: {
    methodKind: "unary";
    input: typeof GetReportRolePermissionRequestSchema;
    output: typeof GetReportRolePermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.UpdateReportRolePermission
   */
  updateReportRolePermission: {
    methodKind: "unary";
    input: typeof UpdateReportRolePermissionRequestSchema;
    output: typeof UpdateReportRolePermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.ListReportRolePermissions
   */
  listReportRolePermissions: {
    methodKind: "unary";
    input: typeof ListReportRolePermissionsRequestSchema;
    output: typeof ListReportRolePermissionsResponseSchema;
  },
  /**
   * SITUATIONS
   *
   * @generated from rpc hero.permissions.v1.PermissionService.GetSituationRolePermission
   */
  getSituationRolePermission: {
    methodKind: "unary";
    input: typeof GetSituationRolePermissionRequestSchema;
    output: typeof GetSituationRolePermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.UpdateSituationRolePermission
   */
  updateSituationRolePermission: {
    methodKind: "unary";
    input: typeof UpdateSituationRolePermissionRequestSchema;
    output: typeof UpdateSituationRolePermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.ListSituationRolePermissions
   */
  listSituationRolePermissions: {
    methodKind: "unary";
    input: typeof ListSituationRolePermissionsRequestSchema;
    output: typeof ListSituationRolePermissionsResponseSchema;
  },
  /**
   * CASES
   *
   * @generated from rpc hero.permissions.v1.PermissionService.GetCaseRolePermission
   */
  getCaseRolePermission: {
    methodKind: "unary";
    input: typeof GetCaseRolePermissionRequestSchema;
    output: typeof GetCaseRolePermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.UpdateCaseRolePermission
   */
  updateCaseRolePermission: {
    methodKind: "unary";
    input: typeof UpdateCaseRolePermissionRequestSchema;
    output: typeof UpdateCaseRolePermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.ListCaseRolePermissions
   */
  listCaseRolePermissions: {
    methodKind: "unary";
    input: typeof ListCaseRolePermissionsRequestSchema;
    output: typeof ListCaseRolePermissionsResponseSchema;
  },
  /**
   * # Dispatch Role Management
   *
   * @generated from rpc hero.permissions.v1.PermissionService.DispatchRoleOn
   */
  dispatchRoleOn: {
    methodKind: "unary";
    input: typeof DispatchRoleOnRequestSchema;
    output: typeof DispatchRoleOnResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.DispatchRoleOff
   */
  dispatchRoleOff: {
    methodKind: "unary";
    input: typeof DispatchRoleOffRequestSchema;
    output: typeof DispatchRoleOffResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_permissions_v1_permissions, 0);

/**
 * @generated from extension: bool permission_id = 50000;
 */
export const permission_id: GenExtension<FieldOptions, boolean> = /*@__PURE__*/
  extDesc(file_hero_permissions_v1_permissions, 0);

/**
 * @generated from extension: hero.permissions.v1.AccessControl access_control = 50003;
 */
export const access_control: GenExtension<MethodOptions, AccessControl> = /*@__PURE__*/
  extDesc(file_hero_permissions_v1_permissions, 1);

