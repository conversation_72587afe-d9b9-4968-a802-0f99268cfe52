// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.3
// 	protoc        (unknown)
// source: hero/assets/v2/assets.proto

package assets

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AssetType int32

const (
	AssetType_ASSET_TYPE_UNSPECIFIED AssetType = 0
	AssetType_ASSET_TYPE_MEMBER      AssetType = 1
	AssetType_ASSET_TYPE_DISPATCHER  AssetType = 2
	AssetType_ASSET_TYPE_RESPONDER   AssetType = 3
	AssetType_ASSET_TYPE_CAMERA      AssetType = 4
	AssetType_ASSET_TYPE_BOT         AssetType = 5
	AssetType_ASSET_TYPE_SUPERVISOR  AssetType = 6
	AssetType_ASSET_TYPE_TEST        AssetType = 7
)

// Enum value maps for AssetType.
var (
	AssetType_name = map[int32]string{
		0: "ASSET_TYPE_UNSPECIFIED",
		1: "ASSET_TYPE_MEMBER",
		2: "ASSET_TYPE_DISPATCHER",
		3: "ASSET_TYPE_RESPONDER",
		4: "ASSET_TYPE_CAMERA",
		5: "ASSET_TYPE_BOT",
		6: "ASSET_TYPE_SUPERVISOR",
		7: "ASSET_TYPE_TEST",
	}
	AssetType_value = map[string]int32{
		"ASSET_TYPE_UNSPECIFIED": 0,
		"ASSET_TYPE_MEMBER":      1,
		"ASSET_TYPE_DISPATCHER":  2,
		"ASSET_TYPE_RESPONDER":   3,
		"ASSET_TYPE_CAMERA":      4,
		"ASSET_TYPE_BOT":         5,
		"ASSET_TYPE_SUPERVISOR":  6,
		"ASSET_TYPE_TEST":        7,
	}
)

func (x AssetType) Enum() *AssetType {
	p := new(AssetType)
	*p = x
	return p
}

func (x AssetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetType) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_assets_v2_assets_proto_enumTypes[0].Descriptor()
}

func (AssetType) Type() protoreflect.EnumType {
	return &file_hero_assets_v2_assets_proto_enumTypes[0]
}

func (x AssetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetType.Descriptor instead.
func (AssetType) EnumDescriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{0}
}

type AssetStatus int32

const (
	AssetStatus_ASSET_STATUS_UNSPECIFIED AssetStatus = 0
	AssetStatus_ASSET_STATUS_AVAILABLE   AssetStatus = 1
	AssetStatus_ASSET_STATUS_OFFLINE     AssetStatus = 2
	AssetStatus_ASSET_STATUS_BUSY        AssetStatus = 3
	AssetStatus_ASSET_STATUS_DEACTIVATED AssetStatus = 4
	AssetStatus_ASSET_STATUS_RESERVED    AssetStatus = 5
	AssetStatus_ASSET_STATUS_MAINTENANCE AssetStatus = 6
	AssetStatus_ASSET_STATUS_ON_BREAK    AssetStatus = 7
)

// Enum value maps for AssetStatus.
var (
	AssetStatus_name = map[int32]string{
		0: "ASSET_STATUS_UNSPECIFIED",
		1: "ASSET_STATUS_AVAILABLE",
		2: "ASSET_STATUS_OFFLINE",
		3: "ASSET_STATUS_BUSY",
		4: "ASSET_STATUS_DEACTIVATED",
		5: "ASSET_STATUS_RESERVED",
		6: "ASSET_STATUS_MAINTENANCE",
		7: "ASSET_STATUS_ON_BREAK",
	}
	AssetStatus_value = map[string]int32{
		"ASSET_STATUS_UNSPECIFIED": 0,
		"ASSET_STATUS_AVAILABLE":   1,
		"ASSET_STATUS_OFFLINE":     2,
		"ASSET_STATUS_BUSY":        3,
		"ASSET_STATUS_DEACTIVATED": 4,
		"ASSET_STATUS_RESERVED":    5,
		"ASSET_STATUS_MAINTENANCE": 6,
		"ASSET_STATUS_ON_BREAK":    7,
	}
)

func (x AssetStatus) Enum() *AssetStatus {
	p := new(AssetStatus)
	*p = x
	return p
}

func (x AssetStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_assets_v2_assets_proto_enumTypes[1].Descriptor()
}

func (AssetStatus) Type() protoreflect.EnumType {
	return &file_hero_assets_v2_assets_proto_enumTypes[1]
}

func (x AssetStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetStatus.Descriptor instead.
func (AssetStatus) EnumDescriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{1}
}

// Enum for the order by field in the search assets request.
type SearchOrderBy int32

const (
	SearchOrderBy_SEARCH_ORDER_BY_UNSPECIFIED          SearchOrderBy = 0
	SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE            SearchOrderBy = 1
	SearchOrderBy_SEARCH_ORDER_BY_NAME                 SearchOrderBy = 2
	SearchOrderBy_SEARCH_ORDER_BY_CREATE_TIME          SearchOrderBy = 3
	SearchOrderBy_SEARCH_ORDER_BY_UPDATE_TIME          SearchOrderBy = 4
	SearchOrderBy_SEARCH_ORDER_BY_STATUS_CHANGED_TIME  SearchOrderBy = 5
	SearchOrderBy_SEARCH_ORDER_BY_LOCATION_UPDATE_TIME SearchOrderBy = 6
)

// Enum value maps for SearchOrderBy.
var (
	SearchOrderBy_name = map[int32]string{
		0: "SEARCH_ORDER_BY_UNSPECIFIED",
		1: "SEARCH_ORDER_BY_RELEVANCE",
		2: "SEARCH_ORDER_BY_NAME",
		3: "SEARCH_ORDER_BY_CREATE_TIME",
		4: "SEARCH_ORDER_BY_UPDATE_TIME",
		5: "SEARCH_ORDER_BY_STATUS_CHANGED_TIME",
		6: "SEARCH_ORDER_BY_LOCATION_UPDATE_TIME",
	}
	SearchOrderBy_value = map[string]int32{
		"SEARCH_ORDER_BY_UNSPECIFIED":          0,
		"SEARCH_ORDER_BY_RELEVANCE":            1,
		"SEARCH_ORDER_BY_NAME":                 2,
		"SEARCH_ORDER_BY_CREATE_TIME":          3,
		"SEARCH_ORDER_BY_UPDATE_TIME":          4,
		"SEARCH_ORDER_BY_STATUS_CHANGED_TIME":  5,
		"SEARCH_ORDER_BY_LOCATION_UPDATE_TIME": 6,
	}
)

func (x SearchOrderBy) Enum() *SearchOrderBy {
	p := new(SearchOrderBy)
	*p = x
	return p
}

func (x SearchOrderBy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchOrderBy) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_assets_v2_assets_proto_enumTypes[2].Descriptor()
}

func (SearchOrderBy) Type() protoreflect.EnumType {
	return &file_hero_assets_v2_assets_proto_enumTypes[2]
}

func (x SearchOrderBy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchOrderBy.Descriptor instead.
func (SearchOrderBy) EnumDescriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{2}
}

type GetZelloChannelsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetZelloChannelsRequest) Reset() {
	*x = GetZelloChannelsRequest{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetZelloChannelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetZelloChannelsRequest) ProtoMessage() {}

func (x *GetZelloChannelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetZelloChannelsRequest.ProtoReflect.Descriptor instead.
func (*GetZelloChannelsRequest) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{0}
}

type GetZelloChannelsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZelloChannels []*ZelloChannel        `protobuf:"bytes,1,rep,name=zello_channels,json=zelloChannels,proto3" json:"zello_channels,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetZelloChannelsResponse) Reset() {
	*x = GetZelloChannelsResponse{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetZelloChannelsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetZelloChannelsResponse) ProtoMessage() {}

func (x *GetZelloChannelsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetZelloChannelsResponse.ProtoReflect.Descriptor instead.
func (*GetZelloChannelsResponse) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{1}
}

func (x *GetZelloChannelsResponse) GetZelloChannels() []*ZelloChannel {
	if x != nil {
		return x.ZelloChannels
	}
	return nil
}

type ZelloChannel struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	OrgId          int32                  `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	ZelloChannelId string                 `protobuf:"bytes,3,opt,name=zello_channel_id,json=zelloChannelId,proto3" json:"zello_channel_id,omitempty"`
	DisplayName    string                 `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ZelloChannel) Reset() {
	*x = ZelloChannel{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ZelloChannel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZelloChannel) ProtoMessage() {}

func (x *ZelloChannel) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZelloChannel.ProtoReflect.Descriptor instead.
func (*ZelloChannel) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{2}
}

func (x *ZelloChannel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ZelloChannel) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *ZelloChannel) GetZelloChannelId() string {
	if x != nil {
		return x.ZelloChannelId
	}
	return ""
}

func (x *ZelloChannel) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

type Asset struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	OrgId         int32                  `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	CognitoJwtSub string                 `protobuf:"bytes,3,opt,name=cognito_jwt_sub,json=cognitoJwtSub,proto3" json:"cognito_jwt_sub,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Type          AssetType              `protobuf:"varint,5,opt,name=type,proto3,enum=hero.assets.v2.AssetType" json:"type,omitempty"`
	Status        AssetStatus            `protobuf:"varint,6,opt,name=status,proto3,enum=hero.assets.v2.AssetStatus" json:"status,omitempty"`
	// Last known location
	Latitude           float64 `protobuf:"fixed64,7,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude          float64 `protobuf:"fixed64,8,opt,name=longitude,proto3" json:"longitude,omitempty"`
	LocationUpdateTime string  `protobuf:"bytes,9,opt,name=location_update_time,json=locationUpdateTime,proto3" json:"location_update_time,omitempty"` // ISO8601 timestamp for last location update
	ContactNo          string  `protobuf:"bytes,10,opt,name=contact_no,json=contactNo,proto3" json:"contact_no,omitempty"`
	ContactEmail       string  `protobuf:"bytes,11,opt,name=contact_email,json=contactEmail,proto3" json:"contact_email,omitempty"`
	CreateTime         string  `protobuf:"bytes,12,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"` // ISO8601 timestamp when created
	UpdateTime         string  `protobuf:"bytes,13,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"` // ISO8601 timestamp for last update
	// For asset this will be fixed "ASSET"
	ResourceType string `protobuf:"bytes,14,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// This needs to be a valid JSON string
	// For cameras, this will contain camera-specific details like:
	//
	//	{
	//	  "camera": {
	//	    "ip_address": "*************",
	//	    "rtsp_url": "rtsp://*************/stream",
	//	    "credentials": {
	//	      "username": "admin",
	//	      "password": "encrypted_password",
	//	      "use_tls": true,
	//	      "certificate_path": "/path/to/cert"
	//	    },
	//	    "manufacturer": "Hikvision",
	//	    "model": "DS-2CD2185FWD-I",
	//	    "firmware_version": "V5.5.53",
	//	    "capabilities": ["PTZ", "HD", "NightVision"],
	//	    "stream_type": "RTSP",
	//	    "metadata": {
	//	      "resolution": "1080p",
	//	      "fps": "30",
	//	      "storage": "SD"
	//	    }
	//	  }
	//	}
	AdditionalInfoJson string `protobuf:"bytes,16,opt,name=additional_info_json,json=additionalInfoJson,proto3" json:"additional_info_json,omitempty"`
	// Last timestamp when status got changed
	StatusChangedTime string `protobuf:"bytes,17,opt,name=status_changed_time,json=statusChangedTime,proto3" json:"status_changed_time,omitempty"` // ISO8601 timestamp when status changed
	// Flag to indicate if this is an internal asset
	IsInternal    bool `protobuf:"varint,18,opt,name=is_internal,json=isInternal,proto3" json:"is_internal,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Asset) Reset() {
	*x = Asset{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Asset) ProtoMessage() {}

func (x *Asset) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Asset.ProtoReflect.Descriptor instead.
func (*Asset) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{3}
}

func (x *Asset) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Asset) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *Asset) GetCognitoJwtSub() string {
	if x != nil {
		return x.CognitoJwtSub
	}
	return ""
}

func (x *Asset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Asset) GetType() AssetType {
	if x != nil {
		return x.Type
	}
	return AssetType_ASSET_TYPE_UNSPECIFIED
}

func (x *Asset) GetStatus() AssetStatus {
	if x != nil {
		return x.Status
	}
	return AssetStatus_ASSET_STATUS_UNSPECIFIED
}

func (x *Asset) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *Asset) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *Asset) GetLocationUpdateTime() string {
	if x != nil {
		return x.LocationUpdateTime
	}
	return ""
}

func (x *Asset) GetContactNo() string {
	if x != nil {
		return x.ContactNo
	}
	return ""
}

func (x *Asset) GetContactEmail() string {
	if x != nil {
		return x.ContactEmail
	}
	return ""
}

func (x *Asset) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Asset) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *Asset) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *Asset) GetAdditionalInfoJson() string {
	if x != nil {
		return x.AdditionalInfoJson
	}
	return ""
}

func (x *Asset) GetStatusChangedTime() string {
	if x != nil {
		return x.StatusChangedTime
	}
	return ""
}

func (x *Asset) GetIsInternal() bool {
	if x != nil {
		return x.IsInternal
	}
	return false
}

type GetAssetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAssetRequest) Reset() {
	*x = GetAssetRequest{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetRequest) ProtoMessage() {}

func (x *GetAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetRequest.ProtoReflect.Descriptor instead.
func (*GetAssetRequest) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{4}
}

func (x *GetAssetRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetAssetResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Asset         *Asset                 `protobuf:"bytes,1,opt,name=asset,proto3" json:"asset,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAssetResponse) Reset() {
	*x = GetAssetResponse{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAssetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetResponse) ProtoMessage() {}

func (x *GetAssetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetResponse.ProtoReflect.Descriptor instead.
func (*GetAssetResponse) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{5}
}

func (x *GetAssetResponse) GetAsset() *Asset {
	if x != nil {
		return x.Asset
	}
	return nil
}

type GetAssetByCognitoSubRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CognitoJwtSub string                 `protobuf:"bytes,1,opt,name=cognito_jwt_sub,json=cognitoJwtSub,proto3" json:"cognito_jwt_sub,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAssetByCognitoSubRequest) Reset() {
	*x = GetAssetByCognitoSubRequest{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAssetByCognitoSubRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetByCognitoSubRequest) ProtoMessage() {}

func (x *GetAssetByCognitoSubRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetByCognitoSubRequest.ProtoReflect.Descriptor instead.
func (*GetAssetByCognitoSubRequest) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{6}
}

func (x *GetAssetByCognitoSubRequest) GetCognitoJwtSub() string {
	if x != nil {
		return x.CognitoJwtSub
	}
	return ""
}

type GetAssetByCognitoSubResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Asset         *Asset                 `protobuf:"bytes,1,opt,name=asset,proto3" json:"asset,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAssetByCognitoSubResponse) Reset() {
	*x = GetAssetByCognitoSubResponse{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAssetByCognitoSubResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetByCognitoSubResponse) ProtoMessage() {}

func (x *GetAssetByCognitoSubResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetByCognitoSubResponse.ProtoReflect.Descriptor instead.
func (*GetAssetByCognitoSubResponse) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{7}
}

func (x *GetAssetByCognitoSubResponse) GetAsset() *Asset {
	if x != nil {
		return x.Asset
	}
	return nil
}

type CreateAssetRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The Asset provided here should not include an id, which will be generated
	Asset         *Asset `protobuf:"bytes,1,opt,name=asset,proto3" json:"asset,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAssetRequest) Reset() {
	*x = CreateAssetRequest{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAssetRequest) ProtoMessage() {}

func (x *CreateAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAssetRequest.ProtoReflect.Descriptor instead.
func (*CreateAssetRequest) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{8}
}

func (x *CreateAssetRequest) GetAsset() *Asset {
	if x != nil {
		return x.Asset
	}
	return nil
}

type CreateAssetResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Asset         *Asset                 `protobuf:"bytes,1,opt,name=asset,proto3" json:"asset,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAssetResponse) Reset() {
	*x = CreateAssetResponse{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAssetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAssetResponse) ProtoMessage() {}

func (x *CreateAssetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAssetResponse.ProtoReflect.Descriptor instead.
func (*CreateAssetResponse) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{9}
}

func (x *CreateAssetResponse) GetAsset() *Asset {
	if x != nil {
		return x.Asset
	}
	return nil
}

// Request message to list assets with pagination, filtering, and ordering options.
type ListAssetsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Maximum number of assets to return in the response.
	PageSize int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// A token identifying a specific page of results to retrieve.
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// Optional filter: Returns only assets of the specified type.
	Type AssetType `protobuf:"varint,3,opt,name=type,proto3,enum=hero.assets.v2.AssetType" json:"type,omitempty"`
	// Optional filter: Returns only assets matching the specified status.
	Status AssetStatus `protobuf:"varint,4,opt,name=status,proto3,enum=hero.assets.v2.AssetStatus" json:"status,omitempty"`
	// Optional: Specifies the ordering of returned assets (e.g., "name asc" or "create_time desc").
	OrderBy       string `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAssetsRequest) Reset() {
	*x = ListAssetsRequest{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAssetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssetsRequest) ProtoMessage() {}

func (x *ListAssetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssetsRequest.ProtoReflect.Descriptor instead.
func (*ListAssetsRequest) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{10}
}

func (x *ListAssetsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListAssetsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListAssetsRequest) GetType() AssetType {
	if x != nil {
		return x.Type
	}
	return AssetType_ASSET_TYPE_UNSPECIFIED
}

func (x *ListAssetsRequest) GetStatus() AssetStatus {
	if x != nil {
		return x.Status
	}
	return AssetStatus_ASSET_STATUS_UNSPECIFIED
}

func (x *ListAssetsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

type ListAssetsResponse struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	Assets []*Asset               `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"`
	// Token to retrieve the next page of results, if any
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAssetsResponse) Reset() {
	*x = ListAssetsResponse{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAssetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssetsResponse) ProtoMessage() {}

func (x *ListAssetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssetsResponse.ProtoReflect.Descriptor instead.
func (*ListAssetsResponse) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{11}
}

func (x *ListAssetsResponse) GetAssets() []*Asset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *ListAssetsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type DeleteAssetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAssetRequest) Reset() {
	*x = DeleteAssetRequest{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAssetRequest) ProtoMessage() {}

func (x *DeleteAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAssetRequest.ProtoReflect.Descriptor instead.
func (*DeleteAssetRequest) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteAssetRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeleteAssetResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAssetResponse) Reset() {
	*x = DeleteAssetResponse{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAssetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAssetResponse) ProtoMessage() {}

func (x *DeleteAssetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAssetResponse.ProtoReflect.Descriptor instead.
func (*DeleteAssetResponse) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{13}
}

type UpdateAssetRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The asset field should include the id of the asset to update along with new field values
	Asset         *Asset `protobuf:"bytes,1,opt,name=asset,proto3" json:"asset,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAssetRequest) Reset() {
	*x = UpdateAssetRequest{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAssetRequest) ProtoMessage() {}

func (x *UpdateAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAssetRequest.ProtoReflect.Descriptor instead.
func (*UpdateAssetRequest) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateAssetRequest) GetAsset() *Asset {
	if x != nil {
		return x.Asset
	}
	return nil
}

type UpdateAssetResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Asset         *Asset                 `protobuf:"bytes,1,opt,name=asset,proto3" json:"asset,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAssetResponse) Reset() {
	*x = UpdateAssetResponse{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAssetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAssetResponse) ProtoMessage() {}

func (x *UpdateAssetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAssetResponse.ProtoReflect.Descriptor instead.
func (*UpdateAssetResponse) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateAssetResponse) GetAsset() *Asset {
	if x != nil {
		return x.Asset
	}
	return nil
}

type AddAdditionalInfoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the asset to update.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// A JSON string containing the additional info to be merged.
	AdditionalInfoJson string `protobuf:"bytes,2,opt,name=additional_info_json,json=additionalInfoJson,proto3" json:"additional_info_json,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *AddAdditionalInfoRequest) Reset() {
	*x = AddAdditionalInfoRequest{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddAdditionalInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAdditionalInfoRequest) ProtoMessage() {}

func (x *AddAdditionalInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAdditionalInfoRequest.ProtoReflect.Descriptor instead.
func (*AddAdditionalInfoRequest) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{16}
}

func (x *AddAdditionalInfoRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AddAdditionalInfoRequest) GetAdditionalInfoJson() string {
	if x != nil {
		return x.AdditionalInfoJson
	}
	return ""
}

type AddAdditionalInfoResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The id of the updated asset
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The updated additional_info_json object.
	AdditionalInfoJson string `protobuf:"bytes,2,opt,name=additional_info_json,json=additionalInfoJson,proto3" json:"additional_info_json,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *AddAdditionalInfoResponse) Reset() {
	*x = AddAdditionalInfoResponse{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddAdditionalInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAdditionalInfoResponse) ProtoMessage() {}

func (x *AddAdditionalInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAdditionalInfoResponse.ProtoReflect.Descriptor instead.
func (*AddAdditionalInfoResponse) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{17}
}

func (x *AddAdditionalInfoResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AddAdditionalInfoResponse) GetAdditionalInfoJson() string {
	if x != nil {
		return x.AdditionalInfoJson
	}
	return ""
}

type ZelloCreds struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	OrgId             int32                  `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	AssetId           string                 `protobuf:"bytes,3,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	Username          string                 `protobuf:"bytes,4,opt,name=username,proto3" json:"username,omitempty"`
	Password          string                 `protobuf:"bytes,5,opt,name=password,proto3" json:"password,omitempty"`
	EncryptedPassword string                 `protobuf:"bytes,6,opt,name=encrypted_password,json=encryptedPassword,proto3" json:"encrypted_password,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ZelloCreds) Reset() {
	*x = ZelloCreds{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ZelloCreds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZelloCreds) ProtoMessage() {}

func (x *ZelloCreds) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZelloCreds.ProtoReflect.Descriptor instead.
func (*ZelloCreds) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{18}
}

func (x *ZelloCreds) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ZelloCreds) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *ZelloCreds) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *ZelloCreds) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ZelloCreds) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *ZelloCreds) GetEncryptedPassword() string {
	if x != nil {
		return x.EncryptedPassword
	}
	return ""
}

type GetAssetPrivateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AssetId       string                 `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAssetPrivateRequest) Reset() {
	*x = GetAssetPrivateRequest{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAssetPrivateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetPrivateRequest) ProtoMessage() {}

func (x *GetAssetPrivateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetPrivateRequest.ProtoReflect.Descriptor instead.
func (*GetAssetPrivateRequest) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{19}
}

func (x *GetAssetPrivateRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

type GetAssetPrivateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Asset         *Asset                 `protobuf:"bytes,1,opt,name=asset,proto3" json:"asset,omitempty"`
	ZelloCreds    *ZelloCreds            `protobuf:"bytes,2,opt,name=zello_creds,json=zelloCreds,proto3" json:"zello_creds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAssetPrivateResponse) Reset() {
	*x = GetAssetPrivateResponse{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAssetPrivateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetPrivateResponse) ProtoMessage() {}

func (x *GetAssetPrivateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetPrivateResponse.ProtoReflect.Descriptor instead.
func (*GetAssetPrivateResponse) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{20}
}

func (x *GetAssetPrivateResponse) GetAsset() *Asset {
	if x != nil {
		return x.Asset
	}
	return nil
}

func (x *GetAssetPrivateResponse) GetZelloCreds() *ZelloCreds {
	if x != nil {
		return x.ZelloCreds
	}
	return nil
}

// Request to get all assets associated with a phone number
type ListAssetsByPhoneNumberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PhoneNumber   string                 `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAssetsByPhoneNumberRequest) Reset() {
	*x = ListAssetsByPhoneNumberRequest{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAssetsByPhoneNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssetsByPhoneNumberRequest) ProtoMessage() {}

func (x *ListAssetsByPhoneNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssetsByPhoneNumberRequest.ProtoReflect.Descriptor instead.
func (*ListAssetsByPhoneNumberRequest) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{21}
}

func (x *ListAssetsByPhoneNumberRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

// Response containing all assets that have the requested phone number
type ListAssetsByPhoneNumberResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Assets        []*Asset               `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAssetsByPhoneNumberResponse) Reset() {
	*x = ListAssetsByPhoneNumberResponse{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAssetsByPhoneNumberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssetsByPhoneNumberResponse) ProtoMessage() {}

func (x *ListAssetsByPhoneNumberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssetsByPhoneNumberResponse.ProtoReflect.Descriptor instead.
func (*ListAssetsByPhoneNumberResponse) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{22}
}

func (x *ListAssetsByPhoneNumberResponse) GetAssets() []*Asset {
	if x != nil {
		return x.Assets
	}
	return nil
}

// ---------------------------------------------------------------------------
// SEARCH MESSAGES
// ---------------------------------------------------------------------------
type DateRange struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	From          string                 `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	To            string                 `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DateRange) Reset() {
	*x = DateRange{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DateRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateRange) ProtoMessage() {}

func (x *DateRange) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateRange.ProtoReflect.Descriptor instead.
func (*DateRange) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{23}
}

func (x *DateRange) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *DateRange) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

type FieldQuery struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"` // The field to search in (id, name, contact_no, contact_email)
	Query         string                 `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"` // The query text for this specific field
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FieldQuery) Reset() {
	*x = FieldQuery{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FieldQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldQuery) ProtoMessage() {}

func (x *FieldQuery) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldQuery.ProtoReflect.Descriptor instead.
func (*FieldQuery) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{24}
}

func (x *FieldQuery) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *FieldQuery) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

type HighlightResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`         // Field name that had a match
	Fragments     []string               `protobuf:"bytes,2,rep,name=fragments,proto3" json:"fragments,omitempty"` // Highlighted fragments with matched terms
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HighlightResult) Reset() {
	*x = HighlightResult{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HighlightResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HighlightResult) ProtoMessage() {}

func (x *HighlightResult) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HighlightResult.ProtoReflect.Descriptor instead.
func (*HighlightResult) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{25}
}

func (x *HighlightResult) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *HighlightResult) GetFragments() []string {
	if x != nil {
		return x.Fragments
	}
	return nil
}

type SearchAssetsRequest struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	Query        string                 `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`                                   // Free-text / fuzzy query (id, name, contact_no, contact_email)
	SearchFields []string               `protobuf:"bytes,2,rep,name=search_fields,json=searchFields,proto3" json:"search_fields,omitempty"` // Limit search scope (leave empty = all)
	FieldQueries []*FieldQuery          `protobuf:"bytes,3,rep,name=field_queries,json=fieldQueries,proto3" json:"field_queries,omitempty"` // Field-specific query terms
	// ─────────────── Fixed-value filters ───────────────
	Type   []AssetType   `protobuf:"varint,4,rep,packed,name=type,proto3,enum=hero.assets.v2.AssetType" json:"type,omitempty"`
	Status []AssetStatus `protobuf:"varint,5,rep,packed,name=status,proto3,enum=hero.assets.v2.AssetStatus" json:"status,omitempty"`
	// ─────────────── Date-range filters (inclusive RFC3339) ───────────────
	CreateTime         *DateRange `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime         *DateRange `protobuf:"bytes,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	StatusChangedTime  *DateRange `protobuf:"bytes,8,opt,name=status_changed_time,json=statusChangedTime,proto3" json:"status_changed_time,omitempty"`
	LocationUpdateTime *DateRange `protobuf:"bytes,9,opt,name=location_update_time,json=locationUpdateTime,proto3" json:"location_update_time,omitempty"`
	// ─────────────── Geo bounding box ───────────────
	MinLatitude  float64 `protobuf:"fixed64,10,opt,name=min_latitude,json=minLatitude,proto3" json:"min_latitude,omitempty"`
	MaxLatitude  float64 `protobuf:"fixed64,11,opt,name=max_latitude,json=maxLatitude,proto3" json:"max_latitude,omitempty"`
	MinLongitude float64 `protobuf:"fixed64,12,opt,name=min_longitude,json=minLongitude,proto3" json:"min_longitude,omitempty"`
	MaxLongitude float64 `protobuf:"fixed64,13,opt,name=max_longitude,json=maxLongitude,proto3" json:"max_longitude,omitempty"`
	// ─────────────── Pagination & sorting ───────────────
	PageSize  int32  `protobuf:"varint,14,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken string `protobuf:"bytes,15,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// ─────────────── Enum based ordering ───────────────
	OrderBy       SearchOrderBy `protobuf:"varint,16,opt,name=order_by,json=orderBy,proto3,enum=hero.assets.v2.SearchOrderBy" json:"order_by,omitempty"` // defaults to RELEVANCE
	Ascending     bool          `protobuf:"varint,17,opt,name=ascending,proto3" json:"ascending,omitempty"`                                              // true = ASC, false = DESC
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchAssetsRequest) Reset() {
	*x = SearchAssetsRequest{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchAssetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAssetsRequest) ProtoMessage() {}

func (x *SearchAssetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAssetsRequest.ProtoReflect.Descriptor instead.
func (*SearchAssetsRequest) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{26}
}

func (x *SearchAssetsRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *SearchAssetsRequest) GetSearchFields() []string {
	if x != nil {
		return x.SearchFields
	}
	return nil
}

func (x *SearchAssetsRequest) GetFieldQueries() []*FieldQuery {
	if x != nil {
		return x.FieldQueries
	}
	return nil
}

func (x *SearchAssetsRequest) GetType() []AssetType {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *SearchAssetsRequest) GetStatus() []AssetStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *SearchAssetsRequest) GetCreateTime() *DateRange {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *SearchAssetsRequest) GetUpdateTime() *DateRange {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *SearchAssetsRequest) GetStatusChangedTime() *DateRange {
	if x != nil {
		return x.StatusChangedTime
	}
	return nil
}

func (x *SearchAssetsRequest) GetLocationUpdateTime() *DateRange {
	if x != nil {
		return x.LocationUpdateTime
	}
	return nil
}

func (x *SearchAssetsRequest) GetMinLatitude() float64 {
	if x != nil {
		return x.MinLatitude
	}
	return 0
}

func (x *SearchAssetsRequest) GetMaxLatitude() float64 {
	if x != nil {
		return x.MaxLatitude
	}
	return 0
}

func (x *SearchAssetsRequest) GetMinLongitude() float64 {
	if x != nil {
		return x.MinLongitude
	}
	return 0
}

func (x *SearchAssetsRequest) GetMaxLongitude() float64 {
	if x != nil {
		return x.MaxLongitude
	}
	return 0
}

func (x *SearchAssetsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SearchAssetsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *SearchAssetsRequest) GetOrderBy() SearchOrderBy {
	if x != nil {
		return x.OrderBy
	}
	return SearchOrderBy_SEARCH_ORDER_BY_UNSPECIFIED
}

func (x *SearchAssetsRequest) GetAscending() bool {
	if x != nil {
		return x.Ascending
	}
	return false
}

type SearchAssetsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Assets        []*Asset               `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"`
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// New field for highlight information
	Highlights    map[string]*HighlightResult `protobuf:"bytes,3,rep,name=highlights,proto3" json:"highlights,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // Key is asset ID
	TotalResults  int32                       `protobuf:"varint,4,opt,name=total_results,json=totalResults,proto3" json:"total_results,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchAssetsResponse) Reset() {
	*x = SearchAssetsResponse{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchAssetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAssetsResponse) ProtoMessage() {}

func (x *SearchAssetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAssetsResponse.ProtoReflect.Descriptor instead.
func (*SearchAssetsResponse) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{27}
}

func (x *SearchAssetsResponse) GetAssets() []*Asset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *SearchAssetsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *SearchAssetsResponse) GetHighlights() map[string]*HighlightResult {
	if x != nil {
		return x.Highlights
	}
	return nil
}

func (x *SearchAssetsResponse) GetTotalResults() int32 {
	if x != nil {
		return x.TotalResults
	}
	return 0
}

type SetAssetInternalStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	IsInternal    bool                   `protobuf:"varint,2,opt,name=is_internal,json=isInternal,proto3" json:"is_internal,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetAssetInternalStatusRequest) Reset() {
	*x = SetAssetInternalStatusRequest{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetAssetInternalStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAssetInternalStatusRequest) ProtoMessage() {}

func (x *SetAssetInternalStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAssetInternalStatusRequest.ProtoReflect.Descriptor instead.
func (*SetAssetInternalStatusRequest) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{28}
}

func (x *SetAssetInternalStatusRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SetAssetInternalStatusRequest) GetIsInternal() bool {
	if x != nil {
		return x.IsInternal
	}
	return false
}

type SetAssetInternalStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Asset         *Asset                 `protobuf:"bytes,1,opt,name=asset,proto3" json:"asset,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetAssetInternalStatusResponse) Reset() {
	*x = SetAssetInternalStatusResponse{}
	mi := &file_hero_assets_v2_assets_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetAssetInternalStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAssetInternalStatusResponse) ProtoMessage() {}

func (x *SetAssetInternalStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_assets_v2_assets_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAssetInternalStatusResponse.ProtoReflect.Descriptor instead.
func (*SetAssetInternalStatusResponse) Descriptor() ([]byte, []int) {
	return file_hero_assets_v2_assets_proto_rawDescGZIP(), []int{29}
}

func (x *SetAssetInternalStatusResponse) GetAsset() *Asset {
	if x != nil {
		return x.Asset
	}
	return nil
}

var File_hero_assets_v2_assets_proto protoreflect.FileDescriptor

var file_hero_assets_v2_assets_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x76, 0x32,
	0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x22, 0x19, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x5a, 0x65, 0x6c, 0x6c, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x5f, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x5a,
	0x65, 0x6c, 0x6c, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x7a, 0x65, 0x6c, 0x6c, 0x6f, 0x5f, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x5a, 0x65,
	0x6c, 0x6c, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x0d, 0x7a, 0x65, 0x6c, 0x6c,
	0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x22, 0x82, 0x01, 0x0a, 0x0c, 0x5a, 0x65,
	0x6c, 0x6c, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x10, 0x7a, 0x65, 0x6c, 0x6c, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x7a, 0x65, 0x6c,
	0x6c, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xe8,
	0x04, 0x0a, 0x05, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0f, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x6f, 0x5f, 0x6a, 0x77, 0x74, 0x5f, 0x73,
	0x75, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74,
	0x6f, 0x4a, 0x77, 0x74, 0x53, 0x75, 0x62, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x33, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c,
	0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09,
	0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4e, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69,
	0x73, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x22, 0x21, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x3f, 0x0a, 0x10,
	0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x2b, 0x0a, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x22, 0x45, 0x0a,
	0x1b, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x67, 0x6e, 0x69,
	0x74, 0x6f, 0x53, 0x75, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f,
	0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x6f, 0x5f, 0x6a, 0x77, 0x74, 0x5f, 0x73, 0x75, 0x62, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x6f, 0x4a, 0x77,
	0x74, 0x53, 0x75, 0x62, 0x22, 0x4b, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x42, 0x79, 0x43, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x6f, 0x53, 0x75, 0x62, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x05, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x22, 0x41, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x05, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x22, 0x42, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x52, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x22, 0xce, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2d, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x33, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x22, 0x6b, 0x0a, 0x12, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2d, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x26,
	0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x24, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x15, 0x0a, 0x13,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x41, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x05, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52,
	0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x22, 0x42, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a,
	0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x52, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x22, 0x5c, 0x0a, 0x18, 0x41, 0x64,
	0x64, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0x5d, 0x0a, 0x19, 0x41, 0x64, 0x64, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0xb5, 0x01, 0x0a, 0x0a, 0x5a, 0x65, 0x6c, 0x6c,
	0x6f, 0x43, 0x72, 0x65, 0x64, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x12, 0x2d, 0x0a, 0x12, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x65, 0x6e,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22,
	0x33, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x22, 0x83, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x2b, 0x0a, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x12, 0x3b, 0x0a,
	0x0b, 0x7a, 0x65, 0x6c, 0x6c, 0x6f, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x5a, 0x65, 0x6c, 0x6c, 0x6f, 0x43, 0x72, 0x65, 0x64, 0x73, 0x52, 0x0a,
	0x7a, 0x65, 0x6c, 0x6c, 0x6f, 0x43, 0x72, 0x65, 0x64, 0x73, 0x22, 0x43, 0x0a, 0x1e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22,
	0x50, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x42, 0x79, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x22, 0x2f, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72,
	0x6f, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x74, 0x6f, 0x22, 0x38, 0x0a, 0x0a, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0x45, 0x0a, 0x0f,
	0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x66, 0x72, 0x61, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x22, 0xa9, 0x06, 0x0a, 0x13, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x3f, 0x0a, 0x0d, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x71, 0x75, 0x65, 0x72, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x0c, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x51, 0x75, 0x65, 0x72, 0x69, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x33, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3a, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x49, 0x0a, 0x13, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x11, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4b,
	0x0a, 0x14, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x12, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d,
	0x69, 0x6e, 0x5f, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0b, 0x6d, 0x69, 0x6e, 0x4c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x4c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x69, 0x6e, 0x5f, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x6d, 0x69, 0x6e, 0x4c, 0x6f, 0x6e,
	0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x78, 0x5f, 0x6c, 0x6f,
	0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x6d,
	0x61, 0x78, 0x4c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x38, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x62, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42,
	0x79, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x61, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x22,
	0xc8, 0x02, 0x0a, 0x14, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52,
	0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x54, 0x0a, 0x0a, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x48, 0x69, 0x67, 0x68, 0x6c, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x68, 0x69, 0x67, 0x68, 0x6c,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x5e, 0x0a, 0x0f, 0x48, 0x69,
	0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x35, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x50, 0x0a, 0x1d, 0x53, 0x65,
	0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x69,
	0x73, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x69, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x22, 0x4d, 0x0a, 0x1e,
	0x53, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b,
	0x0a, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2a, 0xce, 0x01, 0x0a, 0x09,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x53, 0x53,
	0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15,
	0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x41,
	0x54, 0x43, 0x48, 0x45, 0x52, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x53, 0x53, 0x45, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x44, 0x45, 0x52, 0x10,
	0x03, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x43, 0x41, 0x4d, 0x45, 0x52, 0x41, 0x10, 0x04, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x53, 0x53, 0x45,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x4f, 0x54, 0x10, 0x05, 0x12, 0x19, 0x0a, 0x15,
	0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x55, 0x50, 0x45, 0x52,
	0x56, 0x49, 0x53, 0x4f, 0x52, 0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x53, 0x53, 0x45, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x53, 0x54, 0x10, 0x07, 0x2a, 0xea, 0x01, 0x0a,
	0x0b, 0x41, 0x73, 0x73, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x18,
	0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c,
	0x41, 0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x02,
	0x12, 0x15, 0x0a, 0x11, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x42, 0x55, 0x53, 0x59, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x53, 0x53, 0x45, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41,
	0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x52, 0x56, 0x45, 0x44, 0x10, 0x05,
	0x12, 0x1c, 0x0a, 0x18, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x06, 0x12, 0x19,
	0x0a, 0x15, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f,
	0x4e, 0x5f, 0x42, 0x52, 0x45, 0x41, 0x4b, 0x10, 0x07, 0x2a, 0xfe, 0x01, 0x0a, 0x0d, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1f, 0x0a, 0x1b, 0x53,
	0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19,
	0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f,
	0x52, 0x45, 0x4c, 0x45, 0x56, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x53,
	0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f,
	0x54, 0x49, 0x4d, 0x45, 0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48,
	0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x04, 0x12, 0x27, 0x0a, 0x23, 0x53, 0x45, 0x41, 0x52, 0x43,
	0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x44, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x05,
	0x12, 0x28, 0x0a, 0x24, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52,
	0x5f, 0x42, 0x59, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x06, 0x32, 0xb3, 0x0a, 0x0a, 0x14, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x4f, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12,
	0x1f, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x73, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x42, 0x79, 0x43, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x6f, 0x53, 0x75, 0x62, 0x12, 0x2b, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x6f, 0x53,
	0x75, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x6f, 0x53, 0x75, 0x62, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x58, 0x0a, 0x0b, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x22, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x61, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x64, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x22, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x55, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x12, 0x21, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x58, 0x0a,
	0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x22, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x58, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x22, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x68, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x29, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x12, 0x26,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x67, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x5a, 0x65, 0x6c, 0x6c, 0x6f, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x12, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x5a, 0x65, 0x6c, 0x6c, 0x6f, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x47, 0x65, 0x74, 0x5a, 0x65, 0x6c, 0x6c, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7c, 0x0a, 0x17, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x0c, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x79, 0x0a, 0x16, 0x53, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x2d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x53, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x53, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x42, 0x1d, 0x5a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x76, 0x32, 0x3b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_hero_assets_v2_assets_proto_rawDescOnce sync.Once
	file_hero_assets_v2_assets_proto_rawDescData = file_hero_assets_v2_assets_proto_rawDesc
)

func file_hero_assets_v2_assets_proto_rawDescGZIP() []byte {
	file_hero_assets_v2_assets_proto_rawDescOnce.Do(func() {
		file_hero_assets_v2_assets_proto_rawDescData = protoimpl.X.CompressGZIP(file_hero_assets_v2_assets_proto_rawDescData)
	})
	return file_hero_assets_v2_assets_proto_rawDescData
}

var file_hero_assets_v2_assets_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_hero_assets_v2_assets_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_hero_assets_v2_assets_proto_goTypes = []any{
	(AssetType)(0),                          // 0: hero.assets.v2.AssetType
	(AssetStatus)(0),                        // 1: hero.assets.v2.AssetStatus
	(SearchOrderBy)(0),                      // 2: hero.assets.v2.SearchOrderBy
	(*GetZelloChannelsRequest)(nil),         // 3: hero.assets.v2.GetZelloChannelsRequest
	(*GetZelloChannelsResponse)(nil),        // 4: hero.assets.v2.GetZelloChannelsResponse
	(*ZelloChannel)(nil),                    // 5: hero.assets.v2.ZelloChannel
	(*Asset)(nil),                           // 6: hero.assets.v2.Asset
	(*GetAssetRequest)(nil),                 // 7: hero.assets.v2.GetAssetRequest
	(*GetAssetResponse)(nil),                // 8: hero.assets.v2.GetAssetResponse
	(*GetAssetByCognitoSubRequest)(nil),     // 9: hero.assets.v2.GetAssetByCognitoSubRequest
	(*GetAssetByCognitoSubResponse)(nil),    // 10: hero.assets.v2.GetAssetByCognitoSubResponse
	(*CreateAssetRequest)(nil),              // 11: hero.assets.v2.CreateAssetRequest
	(*CreateAssetResponse)(nil),             // 12: hero.assets.v2.CreateAssetResponse
	(*ListAssetsRequest)(nil),               // 13: hero.assets.v2.ListAssetsRequest
	(*ListAssetsResponse)(nil),              // 14: hero.assets.v2.ListAssetsResponse
	(*DeleteAssetRequest)(nil),              // 15: hero.assets.v2.DeleteAssetRequest
	(*DeleteAssetResponse)(nil),             // 16: hero.assets.v2.DeleteAssetResponse
	(*UpdateAssetRequest)(nil),              // 17: hero.assets.v2.UpdateAssetRequest
	(*UpdateAssetResponse)(nil),             // 18: hero.assets.v2.UpdateAssetResponse
	(*AddAdditionalInfoRequest)(nil),        // 19: hero.assets.v2.AddAdditionalInfoRequest
	(*AddAdditionalInfoResponse)(nil),       // 20: hero.assets.v2.AddAdditionalInfoResponse
	(*ZelloCreds)(nil),                      // 21: hero.assets.v2.ZelloCreds
	(*GetAssetPrivateRequest)(nil),          // 22: hero.assets.v2.GetAssetPrivateRequest
	(*GetAssetPrivateResponse)(nil),         // 23: hero.assets.v2.GetAssetPrivateResponse
	(*ListAssetsByPhoneNumberRequest)(nil),  // 24: hero.assets.v2.ListAssetsByPhoneNumberRequest
	(*ListAssetsByPhoneNumberResponse)(nil), // 25: hero.assets.v2.ListAssetsByPhoneNumberResponse
	(*DateRange)(nil),                       // 26: hero.assets.v2.DateRange
	(*FieldQuery)(nil),                      // 27: hero.assets.v2.FieldQuery
	(*HighlightResult)(nil),                 // 28: hero.assets.v2.HighlightResult
	(*SearchAssetsRequest)(nil),             // 29: hero.assets.v2.SearchAssetsRequest
	(*SearchAssetsResponse)(nil),            // 30: hero.assets.v2.SearchAssetsResponse
	(*SetAssetInternalStatusRequest)(nil),   // 31: hero.assets.v2.SetAssetInternalStatusRequest
	(*SetAssetInternalStatusResponse)(nil),  // 32: hero.assets.v2.SetAssetInternalStatusResponse
	nil,                                     // 33: hero.assets.v2.SearchAssetsResponse.HighlightsEntry
}
var file_hero_assets_v2_assets_proto_depIdxs = []int32{
	5,  // 0: hero.assets.v2.GetZelloChannelsResponse.zello_channels:type_name -> hero.assets.v2.ZelloChannel
	0,  // 1: hero.assets.v2.Asset.type:type_name -> hero.assets.v2.AssetType
	1,  // 2: hero.assets.v2.Asset.status:type_name -> hero.assets.v2.AssetStatus
	6,  // 3: hero.assets.v2.GetAssetResponse.asset:type_name -> hero.assets.v2.Asset
	6,  // 4: hero.assets.v2.GetAssetByCognitoSubResponse.asset:type_name -> hero.assets.v2.Asset
	6,  // 5: hero.assets.v2.CreateAssetRequest.asset:type_name -> hero.assets.v2.Asset
	6,  // 6: hero.assets.v2.CreateAssetResponse.asset:type_name -> hero.assets.v2.Asset
	0,  // 7: hero.assets.v2.ListAssetsRequest.type:type_name -> hero.assets.v2.AssetType
	1,  // 8: hero.assets.v2.ListAssetsRequest.status:type_name -> hero.assets.v2.AssetStatus
	6,  // 9: hero.assets.v2.ListAssetsResponse.assets:type_name -> hero.assets.v2.Asset
	6,  // 10: hero.assets.v2.UpdateAssetRequest.asset:type_name -> hero.assets.v2.Asset
	6,  // 11: hero.assets.v2.UpdateAssetResponse.asset:type_name -> hero.assets.v2.Asset
	6,  // 12: hero.assets.v2.GetAssetPrivateResponse.asset:type_name -> hero.assets.v2.Asset
	21, // 13: hero.assets.v2.GetAssetPrivateResponse.zello_creds:type_name -> hero.assets.v2.ZelloCreds
	6,  // 14: hero.assets.v2.ListAssetsByPhoneNumberResponse.assets:type_name -> hero.assets.v2.Asset
	27, // 15: hero.assets.v2.SearchAssetsRequest.field_queries:type_name -> hero.assets.v2.FieldQuery
	0,  // 16: hero.assets.v2.SearchAssetsRequest.type:type_name -> hero.assets.v2.AssetType
	1,  // 17: hero.assets.v2.SearchAssetsRequest.status:type_name -> hero.assets.v2.AssetStatus
	26, // 18: hero.assets.v2.SearchAssetsRequest.create_time:type_name -> hero.assets.v2.DateRange
	26, // 19: hero.assets.v2.SearchAssetsRequest.update_time:type_name -> hero.assets.v2.DateRange
	26, // 20: hero.assets.v2.SearchAssetsRequest.status_changed_time:type_name -> hero.assets.v2.DateRange
	26, // 21: hero.assets.v2.SearchAssetsRequest.location_update_time:type_name -> hero.assets.v2.DateRange
	2,  // 22: hero.assets.v2.SearchAssetsRequest.order_by:type_name -> hero.assets.v2.SearchOrderBy
	6,  // 23: hero.assets.v2.SearchAssetsResponse.assets:type_name -> hero.assets.v2.Asset
	33, // 24: hero.assets.v2.SearchAssetsResponse.highlights:type_name -> hero.assets.v2.SearchAssetsResponse.HighlightsEntry
	6,  // 25: hero.assets.v2.SetAssetInternalStatusResponse.asset:type_name -> hero.assets.v2.Asset
	28, // 26: hero.assets.v2.SearchAssetsResponse.HighlightsEntry.value:type_name -> hero.assets.v2.HighlightResult
	7,  // 27: hero.assets.v2.AssetRegistryService.GetAsset:input_type -> hero.assets.v2.GetAssetRequest
	9,  // 28: hero.assets.v2.AssetRegistryService.GetAssetByCognitoSub:input_type -> hero.assets.v2.GetAssetByCognitoSubRequest
	11, // 29: hero.assets.v2.AssetRegistryService.CreateAsset:input_type -> hero.assets.v2.CreateAssetRequest
	11, // 30: hero.assets.v2.AssetRegistryService.CreateResponderAsset:input_type -> hero.assets.v2.CreateAssetRequest
	13, // 31: hero.assets.v2.AssetRegistryService.ListAssets:input_type -> hero.assets.v2.ListAssetsRequest
	15, // 32: hero.assets.v2.AssetRegistryService.DeleteAsset:input_type -> hero.assets.v2.DeleteAssetRequest
	17, // 33: hero.assets.v2.AssetRegistryService.UpdateAsset:input_type -> hero.assets.v2.UpdateAssetRequest
	19, // 34: hero.assets.v2.AssetRegistryService.AddAdditionalInfo:input_type -> hero.assets.v2.AddAdditionalInfoRequest
	22, // 35: hero.assets.v2.AssetRegistryService.GetAssetPrivate:input_type -> hero.assets.v2.GetAssetPrivateRequest
	3,  // 36: hero.assets.v2.AssetRegistryService.GetZelloChannels:input_type -> hero.assets.v2.GetZelloChannelsRequest
	24, // 37: hero.assets.v2.AssetRegistryService.ListAssetsByPhoneNumber:input_type -> hero.assets.v2.ListAssetsByPhoneNumberRequest
	29, // 38: hero.assets.v2.AssetRegistryService.SearchAssets:input_type -> hero.assets.v2.SearchAssetsRequest
	31, // 39: hero.assets.v2.AssetRegistryService.SetAssetInternalStatus:input_type -> hero.assets.v2.SetAssetInternalStatusRequest
	8,  // 40: hero.assets.v2.AssetRegistryService.GetAsset:output_type -> hero.assets.v2.GetAssetResponse
	10, // 41: hero.assets.v2.AssetRegistryService.GetAssetByCognitoSub:output_type -> hero.assets.v2.GetAssetByCognitoSubResponse
	12, // 42: hero.assets.v2.AssetRegistryService.CreateAsset:output_type -> hero.assets.v2.CreateAssetResponse
	12, // 43: hero.assets.v2.AssetRegistryService.CreateResponderAsset:output_type -> hero.assets.v2.CreateAssetResponse
	14, // 44: hero.assets.v2.AssetRegistryService.ListAssets:output_type -> hero.assets.v2.ListAssetsResponse
	16, // 45: hero.assets.v2.AssetRegistryService.DeleteAsset:output_type -> hero.assets.v2.DeleteAssetResponse
	18, // 46: hero.assets.v2.AssetRegistryService.UpdateAsset:output_type -> hero.assets.v2.UpdateAssetResponse
	20, // 47: hero.assets.v2.AssetRegistryService.AddAdditionalInfo:output_type -> hero.assets.v2.AddAdditionalInfoResponse
	23, // 48: hero.assets.v2.AssetRegistryService.GetAssetPrivate:output_type -> hero.assets.v2.GetAssetPrivateResponse
	4,  // 49: hero.assets.v2.AssetRegistryService.GetZelloChannels:output_type -> hero.assets.v2.GetZelloChannelsResponse
	25, // 50: hero.assets.v2.AssetRegistryService.ListAssetsByPhoneNumber:output_type -> hero.assets.v2.ListAssetsByPhoneNumberResponse
	30, // 51: hero.assets.v2.AssetRegistryService.SearchAssets:output_type -> hero.assets.v2.SearchAssetsResponse
	32, // 52: hero.assets.v2.AssetRegistryService.SetAssetInternalStatus:output_type -> hero.assets.v2.SetAssetInternalStatusResponse
	40, // [40:53] is the sub-list for method output_type
	27, // [27:40] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_hero_assets_v2_assets_proto_init() }
func file_hero_assets_v2_assets_proto_init() {
	if File_hero_assets_v2_assets_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hero_assets_v2_assets_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_hero_assets_v2_assets_proto_goTypes,
		DependencyIndexes: file_hero_assets_v2_assets_proto_depIdxs,
		EnumInfos:         file_hero_assets_v2_assets_proto_enumTypes,
		MessageInfos:      file_hero_assets_v2_assets_proto_msgTypes,
	}.Build()
	File_hero_assets_v2_assets_proto = out.File
	file_hero_assets_v2_assets_proto_rawDesc = nil
	file_hero_assets_v2_assets_proto_goTypes = nil
	file_hero_assets_v2_assets_proto_depIdxs = nil
}
