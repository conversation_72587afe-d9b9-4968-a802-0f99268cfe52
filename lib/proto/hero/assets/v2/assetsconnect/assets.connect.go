// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: hero/assets/v2/assets.proto

package assetsconnect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	http "net/http"
	v2 "proto/hero/assets/v2"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// AssetRegistryServiceName is the fully-qualified name of the AssetRegistryService service.
	AssetRegistryServiceName = "hero.assets.v2.AssetRegistryService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// AssetRegistryServiceGetAssetProcedure is the fully-qualified name of the AssetRegistryService's
	// GetAsset RPC.
	AssetRegistryServiceGetAssetProcedure = "/hero.assets.v2.AssetRegistryService/GetAsset"
	// AssetRegistryServiceGetAssetByCognitoSubProcedure is the fully-qualified name of the
	// AssetRegistryService's GetAssetByCognitoSub RPC.
	AssetRegistryServiceGetAssetByCognitoSubProcedure = "/hero.assets.v2.AssetRegistryService/GetAssetByCognitoSub"
	// AssetRegistryServiceCreateAssetProcedure is the fully-qualified name of the
	// AssetRegistryService's CreateAsset RPC.
	AssetRegistryServiceCreateAssetProcedure = "/hero.assets.v2.AssetRegistryService/CreateAsset"
	// AssetRegistryServiceCreateResponderAssetProcedure is the fully-qualified name of the
	// AssetRegistryService's CreateResponderAsset RPC.
	AssetRegistryServiceCreateResponderAssetProcedure = "/hero.assets.v2.AssetRegistryService/CreateResponderAsset"
	// AssetRegistryServiceListAssetsProcedure is the fully-qualified name of the AssetRegistryService's
	// ListAssets RPC.
	AssetRegistryServiceListAssetsProcedure = "/hero.assets.v2.AssetRegistryService/ListAssets"
	// AssetRegistryServiceDeleteAssetProcedure is the fully-qualified name of the
	// AssetRegistryService's DeleteAsset RPC.
	AssetRegistryServiceDeleteAssetProcedure = "/hero.assets.v2.AssetRegistryService/DeleteAsset"
	// AssetRegistryServiceUpdateAssetProcedure is the fully-qualified name of the
	// AssetRegistryService's UpdateAsset RPC.
	AssetRegistryServiceUpdateAssetProcedure = "/hero.assets.v2.AssetRegistryService/UpdateAsset"
	// AssetRegistryServiceAddAdditionalInfoProcedure is the fully-qualified name of the
	// AssetRegistryService's AddAdditionalInfo RPC.
	AssetRegistryServiceAddAdditionalInfoProcedure = "/hero.assets.v2.AssetRegistryService/AddAdditionalInfo"
	// AssetRegistryServiceGetAssetPrivateProcedure is the fully-qualified name of the
	// AssetRegistryService's GetAssetPrivate RPC.
	AssetRegistryServiceGetAssetPrivateProcedure = "/hero.assets.v2.AssetRegistryService/GetAssetPrivate"
	// AssetRegistryServiceGetZelloChannelsProcedure is the fully-qualified name of the
	// AssetRegistryService's GetZelloChannels RPC.
	AssetRegistryServiceGetZelloChannelsProcedure = "/hero.assets.v2.AssetRegistryService/GetZelloChannels"
	// AssetRegistryServiceListAssetsByPhoneNumberProcedure is the fully-qualified name of the
	// AssetRegistryService's ListAssetsByPhoneNumber RPC.
	AssetRegistryServiceListAssetsByPhoneNumberProcedure = "/hero.assets.v2.AssetRegistryService/ListAssetsByPhoneNumber"
	// AssetRegistryServiceSearchAssetsProcedure is the fully-qualified name of the
	// AssetRegistryService's SearchAssets RPC.
	AssetRegistryServiceSearchAssetsProcedure = "/hero.assets.v2.AssetRegistryService/SearchAssets"
	// AssetRegistryServiceSetAssetInternalStatusProcedure is the fully-qualified name of the
	// AssetRegistryService's SetAssetInternalStatus RPC.
	AssetRegistryServiceSetAssetInternalStatusProcedure = "/hero.assets.v2.AssetRegistryService/SetAssetInternalStatus"
)

// AssetRegistryServiceClient is a client for the hero.assets.v2.AssetRegistryService service.
type AssetRegistryServiceClient interface {
	GetAsset(context.Context, *connect.Request[v2.GetAssetRequest]) (*connect.Response[v2.GetAssetResponse], error)
	// Fetch an asset by its Cognito JWT `sub` claim
	GetAssetByCognitoSub(context.Context, *connect.Request[v2.GetAssetByCognitoSubRequest]) (*connect.Response[v2.GetAssetByCognitoSubResponse], error)
	// Register a new Asset to the Registry
	CreateAsset(context.Context, *connect.Request[v2.CreateAssetRequest]) (*connect.Response[v2.CreateAssetResponse], error)
	CreateResponderAsset(context.Context, *connect.Request[v2.CreateAssetRequest]) (*connect.Response[v2.CreateAssetResponse], error)
	// List all Assets, with their current Orders
	ListAssets(context.Context, *connect.Request[v2.ListAssetsRequest]) (*connect.Response[v2.ListAssetsResponse], error)
	// Delete an Asset
	DeleteAsset(context.Context, *connect.Request[v2.DeleteAssetRequest]) (*connect.Response[v2.DeleteAssetResponse], error)
	// Update an Asset
	UpdateAsset(context.Context, *connect.Request[v2.UpdateAssetRequest]) (*connect.Response[v2.UpdateAssetResponse], error)
	// Add additional info to an asset by merging provided JSON into the existing additional_info_json.
	AddAdditionalInfo(context.Context, *connect.Request[v2.AddAdditionalInfoRequest]) (*connect.Response[v2.AddAdditionalInfoResponse], error)
	// GetAssetPrivate is a special RPC to fetch an Asset and its private details
	// (e.g., Zello credentials) for use in secure communications
	// It is intended to only be accessed by the Asset itself, or by Admins
	GetAssetPrivate(context.Context, *connect.Request[v2.GetAssetPrivateRequest]) (*connect.Response[v2.GetAssetPrivateResponse], error)
	GetZelloChannels(context.Context, *connect.Request[v2.GetZelloChannelsRequest]) (*connect.Response[v2.GetZelloChannelsResponse], error)
	// Get all assets associated with a phone number
	ListAssetsByPhoneNumber(context.Context, *connect.Request[v2.ListAssetsByPhoneNumberRequest]) (*connect.Response[v2.ListAssetsByPhoneNumberResponse], error)
	// Search assets with advanced filtering and text search capabilities
	SearchAssets(context.Context, *connect.Request[v2.SearchAssetsRequest]) (*connect.Response[v2.SearchAssetsResponse], error)
	// Set whether an asset is internal or external
	SetAssetInternalStatus(context.Context, *connect.Request[v2.SetAssetInternalStatusRequest]) (*connect.Response[v2.SetAssetInternalStatusResponse], error)
}

// NewAssetRegistryServiceClient constructs a client for the hero.assets.v2.AssetRegistryService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewAssetRegistryServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) AssetRegistryServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	assetRegistryServiceMethods := v2.File_hero_assets_v2_assets_proto.Services().ByName("AssetRegistryService").Methods()
	return &assetRegistryServiceClient{
		getAsset: connect.NewClient[v2.GetAssetRequest, v2.GetAssetResponse](
			httpClient,
			baseURL+AssetRegistryServiceGetAssetProcedure,
			connect.WithSchema(assetRegistryServiceMethods.ByName("GetAsset")),
			connect.WithClientOptions(opts...),
		),
		getAssetByCognitoSub: connect.NewClient[v2.GetAssetByCognitoSubRequest, v2.GetAssetByCognitoSubResponse](
			httpClient,
			baseURL+AssetRegistryServiceGetAssetByCognitoSubProcedure,
			connect.WithSchema(assetRegistryServiceMethods.ByName("GetAssetByCognitoSub")),
			connect.WithClientOptions(opts...),
		),
		createAsset: connect.NewClient[v2.CreateAssetRequest, v2.CreateAssetResponse](
			httpClient,
			baseURL+AssetRegistryServiceCreateAssetProcedure,
			connect.WithSchema(assetRegistryServiceMethods.ByName("CreateAsset")),
			connect.WithClientOptions(opts...),
		),
		createResponderAsset: connect.NewClient[v2.CreateAssetRequest, v2.CreateAssetResponse](
			httpClient,
			baseURL+AssetRegistryServiceCreateResponderAssetProcedure,
			connect.WithSchema(assetRegistryServiceMethods.ByName("CreateResponderAsset")),
			connect.WithClientOptions(opts...),
		),
		listAssets: connect.NewClient[v2.ListAssetsRequest, v2.ListAssetsResponse](
			httpClient,
			baseURL+AssetRegistryServiceListAssetsProcedure,
			connect.WithSchema(assetRegistryServiceMethods.ByName("ListAssets")),
			connect.WithClientOptions(opts...),
		),
		deleteAsset: connect.NewClient[v2.DeleteAssetRequest, v2.DeleteAssetResponse](
			httpClient,
			baseURL+AssetRegistryServiceDeleteAssetProcedure,
			connect.WithSchema(assetRegistryServiceMethods.ByName("DeleteAsset")),
			connect.WithClientOptions(opts...),
		),
		updateAsset: connect.NewClient[v2.UpdateAssetRequest, v2.UpdateAssetResponse](
			httpClient,
			baseURL+AssetRegistryServiceUpdateAssetProcedure,
			connect.WithSchema(assetRegistryServiceMethods.ByName("UpdateAsset")),
			connect.WithClientOptions(opts...),
		),
		addAdditionalInfo: connect.NewClient[v2.AddAdditionalInfoRequest, v2.AddAdditionalInfoResponse](
			httpClient,
			baseURL+AssetRegistryServiceAddAdditionalInfoProcedure,
			connect.WithSchema(assetRegistryServiceMethods.ByName("AddAdditionalInfo")),
			connect.WithClientOptions(opts...),
		),
		getAssetPrivate: connect.NewClient[v2.GetAssetPrivateRequest, v2.GetAssetPrivateResponse](
			httpClient,
			baseURL+AssetRegistryServiceGetAssetPrivateProcedure,
			connect.WithSchema(assetRegistryServiceMethods.ByName("GetAssetPrivate")),
			connect.WithClientOptions(opts...),
		),
		getZelloChannels: connect.NewClient[v2.GetZelloChannelsRequest, v2.GetZelloChannelsResponse](
			httpClient,
			baseURL+AssetRegistryServiceGetZelloChannelsProcedure,
			connect.WithSchema(assetRegistryServiceMethods.ByName("GetZelloChannels")),
			connect.WithClientOptions(opts...),
		),
		listAssetsByPhoneNumber: connect.NewClient[v2.ListAssetsByPhoneNumberRequest, v2.ListAssetsByPhoneNumberResponse](
			httpClient,
			baseURL+AssetRegistryServiceListAssetsByPhoneNumberProcedure,
			connect.WithSchema(assetRegistryServiceMethods.ByName("ListAssetsByPhoneNumber")),
			connect.WithClientOptions(opts...),
		),
		searchAssets: connect.NewClient[v2.SearchAssetsRequest, v2.SearchAssetsResponse](
			httpClient,
			baseURL+AssetRegistryServiceSearchAssetsProcedure,
			connect.WithSchema(assetRegistryServiceMethods.ByName("SearchAssets")),
			connect.WithClientOptions(opts...),
		),
		setAssetInternalStatus: connect.NewClient[v2.SetAssetInternalStatusRequest, v2.SetAssetInternalStatusResponse](
			httpClient,
			baseURL+AssetRegistryServiceSetAssetInternalStatusProcedure,
			connect.WithSchema(assetRegistryServiceMethods.ByName("SetAssetInternalStatus")),
			connect.WithClientOptions(opts...),
		),
	}
}

// assetRegistryServiceClient implements AssetRegistryServiceClient.
type assetRegistryServiceClient struct {
	getAsset                *connect.Client[v2.GetAssetRequest, v2.GetAssetResponse]
	getAssetByCognitoSub    *connect.Client[v2.GetAssetByCognitoSubRequest, v2.GetAssetByCognitoSubResponse]
	createAsset             *connect.Client[v2.CreateAssetRequest, v2.CreateAssetResponse]
	createResponderAsset    *connect.Client[v2.CreateAssetRequest, v2.CreateAssetResponse]
	listAssets              *connect.Client[v2.ListAssetsRequest, v2.ListAssetsResponse]
	deleteAsset             *connect.Client[v2.DeleteAssetRequest, v2.DeleteAssetResponse]
	updateAsset             *connect.Client[v2.UpdateAssetRequest, v2.UpdateAssetResponse]
	addAdditionalInfo       *connect.Client[v2.AddAdditionalInfoRequest, v2.AddAdditionalInfoResponse]
	getAssetPrivate         *connect.Client[v2.GetAssetPrivateRequest, v2.GetAssetPrivateResponse]
	getZelloChannels        *connect.Client[v2.GetZelloChannelsRequest, v2.GetZelloChannelsResponse]
	listAssetsByPhoneNumber *connect.Client[v2.ListAssetsByPhoneNumberRequest, v2.ListAssetsByPhoneNumberResponse]
	searchAssets            *connect.Client[v2.SearchAssetsRequest, v2.SearchAssetsResponse]
	setAssetInternalStatus  *connect.Client[v2.SetAssetInternalStatusRequest, v2.SetAssetInternalStatusResponse]
}

// GetAsset calls hero.assets.v2.AssetRegistryService.GetAsset.
func (c *assetRegistryServiceClient) GetAsset(ctx context.Context, req *connect.Request[v2.GetAssetRequest]) (*connect.Response[v2.GetAssetResponse], error) {
	return c.getAsset.CallUnary(ctx, req)
}

// GetAssetByCognitoSub calls hero.assets.v2.AssetRegistryService.GetAssetByCognitoSub.
func (c *assetRegistryServiceClient) GetAssetByCognitoSub(ctx context.Context, req *connect.Request[v2.GetAssetByCognitoSubRequest]) (*connect.Response[v2.GetAssetByCognitoSubResponse], error) {
	return c.getAssetByCognitoSub.CallUnary(ctx, req)
}

// CreateAsset calls hero.assets.v2.AssetRegistryService.CreateAsset.
func (c *assetRegistryServiceClient) CreateAsset(ctx context.Context, req *connect.Request[v2.CreateAssetRequest]) (*connect.Response[v2.CreateAssetResponse], error) {
	return c.createAsset.CallUnary(ctx, req)
}

// CreateResponderAsset calls hero.assets.v2.AssetRegistryService.CreateResponderAsset.
func (c *assetRegistryServiceClient) CreateResponderAsset(ctx context.Context, req *connect.Request[v2.CreateAssetRequest]) (*connect.Response[v2.CreateAssetResponse], error) {
	return c.createResponderAsset.CallUnary(ctx, req)
}

// ListAssets calls hero.assets.v2.AssetRegistryService.ListAssets.
func (c *assetRegistryServiceClient) ListAssets(ctx context.Context, req *connect.Request[v2.ListAssetsRequest]) (*connect.Response[v2.ListAssetsResponse], error) {
	return c.listAssets.CallUnary(ctx, req)
}

// DeleteAsset calls hero.assets.v2.AssetRegistryService.DeleteAsset.
func (c *assetRegistryServiceClient) DeleteAsset(ctx context.Context, req *connect.Request[v2.DeleteAssetRequest]) (*connect.Response[v2.DeleteAssetResponse], error) {
	return c.deleteAsset.CallUnary(ctx, req)
}

// UpdateAsset calls hero.assets.v2.AssetRegistryService.UpdateAsset.
func (c *assetRegistryServiceClient) UpdateAsset(ctx context.Context, req *connect.Request[v2.UpdateAssetRequest]) (*connect.Response[v2.UpdateAssetResponse], error) {
	return c.updateAsset.CallUnary(ctx, req)
}

// AddAdditionalInfo calls hero.assets.v2.AssetRegistryService.AddAdditionalInfo.
func (c *assetRegistryServiceClient) AddAdditionalInfo(ctx context.Context, req *connect.Request[v2.AddAdditionalInfoRequest]) (*connect.Response[v2.AddAdditionalInfoResponse], error) {
	return c.addAdditionalInfo.CallUnary(ctx, req)
}

// GetAssetPrivate calls hero.assets.v2.AssetRegistryService.GetAssetPrivate.
func (c *assetRegistryServiceClient) GetAssetPrivate(ctx context.Context, req *connect.Request[v2.GetAssetPrivateRequest]) (*connect.Response[v2.GetAssetPrivateResponse], error) {
	return c.getAssetPrivate.CallUnary(ctx, req)
}

// GetZelloChannels calls hero.assets.v2.AssetRegistryService.GetZelloChannels.
func (c *assetRegistryServiceClient) GetZelloChannels(ctx context.Context, req *connect.Request[v2.GetZelloChannelsRequest]) (*connect.Response[v2.GetZelloChannelsResponse], error) {
	return c.getZelloChannels.CallUnary(ctx, req)
}

// ListAssetsByPhoneNumber calls hero.assets.v2.AssetRegistryService.ListAssetsByPhoneNumber.
func (c *assetRegistryServiceClient) ListAssetsByPhoneNumber(ctx context.Context, req *connect.Request[v2.ListAssetsByPhoneNumberRequest]) (*connect.Response[v2.ListAssetsByPhoneNumberResponse], error) {
	return c.listAssetsByPhoneNumber.CallUnary(ctx, req)
}

// SearchAssets calls hero.assets.v2.AssetRegistryService.SearchAssets.
func (c *assetRegistryServiceClient) SearchAssets(ctx context.Context, req *connect.Request[v2.SearchAssetsRequest]) (*connect.Response[v2.SearchAssetsResponse], error) {
	return c.searchAssets.CallUnary(ctx, req)
}

// SetAssetInternalStatus calls hero.assets.v2.AssetRegistryService.SetAssetInternalStatus.
func (c *assetRegistryServiceClient) SetAssetInternalStatus(ctx context.Context, req *connect.Request[v2.SetAssetInternalStatusRequest]) (*connect.Response[v2.SetAssetInternalStatusResponse], error) {
	return c.setAssetInternalStatus.CallUnary(ctx, req)
}

// AssetRegistryServiceHandler is an implementation of the hero.assets.v2.AssetRegistryService
// service.
type AssetRegistryServiceHandler interface {
	GetAsset(context.Context, *connect.Request[v2.GetAssetRequest]) (*connect.Response[v2.GetAssetResponse], error)
	// Fetch an asset by its Cognito JWT `sub` claim
	GetAssetByCognitoSub(context.Context, *connect.Request[v2.GetAssetByCognitoSubRequest]) (*connect.Response[v2.GetAssetByCognitoSubResponse], error)
	// Register a new Asset to the Registry
	CreateAsset(context.Context, *connect.Request[v2.CreateAssetRequest]) (*connect.Response[v2.CreateAssetResponse], error)
	CreateResponderAsset(context.Context, *connect.Request[v2.CreateAssetRequest]) (*connect.Response[v2.CreateAssetResponse], error)
	// List all Assets, with their current Orders
	ListAssets(context.Context, *connect.Request[v2.ListAssetsRequest]) (*connect.Response[v2.ListAssetsResponse], error)
	// Delete an Asset
	DeleteAsset(context.Context, *connect.Request[v2.DeleteAssetRequest]) (*connect.Response[v2.DeleteAssetResponse], error)
	// Update an Asset
	UpdateAsset(context.Context, *connect.Request[v2.UpdateAssetRequest]) (*connect.Response[v2.UpdateAssetResponse], error)
	// Add additional info to an asset by merging provided JSON into the existing additional_info_json.
	AddAdditionalInfo(context.Context, *connect.Request[v2.AddAdditionalInfoRequest]) (*connect.Response[v2.AddAdditionalInfoResponse], error)
	// GetAssetPrivate is a special RPC to fetch an Asset and its private details
	// (e.g., Zello credentials) for use in secure communications
	// It is intended to only be accessed by the Asset itself, or by Admins
	GetAssetPrivate(context.Context, *connect.Request[v2.GetAssetPrivateRequest]) (*connect.Response[v2.GetAssetPrivateResponse], error)
	GetZelloChannels(context.Context, *connect.Request[v2.GetZelloChannelsRequest]) (*connect.Response[v2.GetZelloChannelsResponse], error)
	// Get all assets associated with a phone number
	ListAssetsByPhoneNumber(context.Context, *connect.Request[v2.ListAssetsByPhoneNumberRequest]) (*connect.Response[v2.ListAssetsByPhoneNumberResponse], error)
	// Search assets with advanced filtering and text search capabilities
	SearchAssets(context.Context, *connect.Request[v2.SearchAssetsRequest]) (*connect.Response[v2.SearchAssetsResponse], error)
	// Set whether an asset is internal or external
	SetAssetInternalStatus(context.Context, *connect.Request[v2.SetAssetInternalStatusRequest]) (*connect.Response[v2.SetAssetInternalStatusResponse], error)
}

// NewAssetRegistryServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewAssetRegistryServiceHandler(svc AssetRegistryServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	assetRegistryServiceMethods := v2.File_hero_assets_v2_assets_proto.Services().ByName("AssetRegistryService").Methods()
	assetRegistryServiceGetAssetHandler := connect.NewUnaryHandler(
		AssetRegistryServiceGetAssetProcedure,
		svc.GetAsset,
		connect.WithSchema(assetRegistryServiceMethods.ByName("GetAsset")),
		connect.WithHandlerOptions(opts...),
	)
	assetRegistryServiceGetAssetByCognitoSubHandler := connect.NewUnaryHandler(
		AssetRegistryServiceGetAssetByCognitoSubProcedure,
		svc.GetAssetByCognitoSub,
		connect.WithSchema(assetRegistryServiceMethods.ByName("GetAssetByCognitoSub")),
		connect.WithHandlerOptions(opts...),
	)
	assetRegistryServiceCreateAssetHandler := connect.NewUnaryHandler(
		AssetRegistryServiceCreateAssetProcedure,
		svc.CreateAsset,
		connect.WithSchema(assetRegistryServiceMethods.ByName("CreateAsset")),
		connect.WithHandlerOptions(opts...),
	)
	assetRegistryServiceCreateResponderAssetHandler := connect.NewUnaryHandler(
		AssetRegistryServiceCreateResponderAssetProcedure,
		svc.CreateResponderAsset,
		connect.WithSchema(assetRegistryServiceMethods.ByName("CreateResponderAsset")),
		connect.WithHandlerOptions(opts...),
	)
	assetRegistryServiceListAssetsHandler := connect.NewUnaryHandler(
		AssetRegistryServiceListAssetsProcedure,
		svc.ListAssets,
		connect.WithSchema(assetRegistryServiceMethods.ByName("ListAssets")),
		connect.WithHandlerOptions(opts...),
	)
	assetRegistryServiceDeleteAssetHandler := connect.NewUnaryHandler(
		AssetRegistryServiceDeleteAssetProcedure,
		svc.DeleteAsset,
		connect.WithSchema(assetRegistryServiceMethods.ByName("DeleteAsset")),
		connect.WithHandlerOptions(opts...),
	)
	assetRegistryServiceUpdateAssetHandler := connect.NewUnaryHandler(
		AssetRegistryServiceUpdateAssetProcedure,
		svc.UpdateAsset,
		connect.WithSchema(assetRegistryServiceMethods.ByName("UpdateAsset")),
		connect.WithHandlerOptions(opts...),
	)
	assetRegistryServiceAddAdditionalInfoHandler := connect.NewUnaryHandler(
		AssetRegistryServiceAddAdditionalInfoProcedure,
		svc.AddAdditionalInfo,
		connect.WithSchema(assetRegistryServiceMethods.ByName("AddAdditionalInfo")),
		connect.WithHandlerOptions(opts...),
	)
	assetRegistryServiceGetAssetPrivateHandler := connect.NewUnaryHandler(
		AssetRegistryServiceGetAssetPrivateProcedure,
		svc.GetAssetPrivate,
		connect.WithSchema(assetRegistryServiceMethods.ByName("GetAssetPrivate")),
		connect.WithHandlerOptions(opts...),
	)
	assetRegistryServiceGetZelloChannelsHandler := connect.NewUnaryHandler(
		AssetRegistryServiceGetZelloChannelsProcedure,
		svc.GetZelloChannels,
		connect.WithSchema(assetRegistryServiceMethods.ByName("GetZelloChannels")),
		connect.WithHandlerOptions(opts...),
	)
	assetRegistryServiceListAssetsByPhoneNumberHandler := connect.NewUnaryHandler(
		AssetRegistryServiceListAssetsByPhoneNumberProcedure,
		svc.ListAssetsByPhoneNumber,
		connect.WithSchema(assetRegistryServiceMethods.ByName("ListAssetsByPhoneNumber")),
		connect.WithHandlerOptions(opts...),
	)
	assetRegistryServiceSearchAssetsHandler := connect.NewUnaryHandler(
		AssetRegistryServiceSearchAssetsProcedure,
		svc.SearchAssets,
		connect.WithSchema(assetRegistryServiceMethods.ByName("SearchAssets")),
		connect.WithHandlerOptions(opts...),
	)
	assetRegistryServiceSetAssetInternalStatusHandler := connect.NewUnaryHandler(
		AssetRegistryServiceSetAssetInternalStatusProcedure,
		svc.SetAssetInternalStatus,
		connect.WithSchema(assetRegistryServiceMethods.ByName("SetAssetInternalStatus")),
		connect.WithHandlerOptions(opts...),
	)
	return "/hero.assets.v2.AssetRegistryService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case AssetRegistryServiceGetAssetProcedure:
			assetRegistryServiceGetAssetHandler.ServeHTTP(w, r)
		case AssetRegistryServiceGetAssetByCognitoSubProcedure:
			assetRegistryServiceGetAssetByCognitoSubHandler.ServeHTTP(w, r)
		case AssetRegistryServiceCreateAssetProcedure:
			assetRegistryServiceCreateAssetHandler.ServeHTTP(w, r)
		case AssetRegistryServiceCreateResponderAssetProcedure:
			assetRegistryServiceCreateResponderAssetHandler.ServeHTTP(w, r)
		case AssetRegistryServiceListAssetsProcedure:
			assetRegistryServiceListAssetsHandler.ServeHTTP(w, r)
		case AssetRegistryServiceDeleteAssetProcedure:
			assetRegistryServiceDeleteAssetHandler.ServeHTTP(w, r)
		case AssetRegistryServiceUpdateAssetProcedure:
			assetRegistryServiceUpdateAssetHandler.ServeHTTP(w, r)
		case AssetRegistryServiceAddAdditionalInfoProcedure:
			assetRegistryServiceAddAdditionalInfoHandler.ServeHTTP(w, r)
		case AssetRegistryServiceGetAssetPrivateProcedure:
			assetRegistryServiceGetAssetPrivateHandler.ServeHTTP(w, r)
		case AssetRegistryServiceGetZelloChannelsProcedure:
			assetRegistryServiceGetZelloChannelsHandler.ServeHTTP(w, r)
		case AssetRegistryServiceListAssetsByPhoneNumberProcedure:
			assetRegistryServiceListAssetsByPhoneNumberHandler.ServeHTTP(w, r)
		case AssetRegistryServiceSearchAssetsProcedure:
			assetRegistryServiceSearchAssetsHandler.ServeHTTP(w, r)
		case AssetRegistryServiceSetAssetInternalStatusProcedure:
			assetRegistryServiceSetAssetInternalStatusHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedAssetRegistryServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedAssetRegistryServiceHandler struct{}

func (UnimplementedAssetRegistryServiceHandler) GetAsset(context.Context, *connect.Request[v2.GetAssetRequest]) (*connect.Response[v2.GetAssetResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.assets.v2.AssetRegistryService.GetAsset is not implemented"))
}

func (UnimplementedAssetRegistryServiceHandler) GetAssetByCognitoSub(context.Context, *connect.Request[v2.GetAssetByCognitoSubRequest]) (*connect.Response[v2.GetAssetByCognitoSubResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.assets.v2.AssetRegistryService.GetAssetByCognitoSub is not implemented"))
}

func (UnimplementedAssetRegistryServiceHandler) CreateAsset(context.Context, *connect.Request[v2.CreateAssetRequest]) (*connect.Response[v2.CreateAssetResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.assets.v2.AssetRegistryService.CreateAsset is not implemented"))
}

func (UnimplementedAssetRegistryServiceHandler) CreateResponderAsset(context.Context, *connect.Request[v2.CreateAssetRequest]) (*connect.Response[v2.CreateAssetResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.assets.v2.AssetRegistryService.CreateResponderAsset is not implemented"))
}

func (UnimplementedAssetRegistryServiceHandler) ListAssets(context.Context, *connect.Request[v2.ListAssetsRequest]) (*connect.Response[v2.ListAssetsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.assets.v2.AssetRegistryService.ListAssets is not implemented"))
}

func (UnimplementedAssetRegistryServiceHandler) DeleteAsset(context.Context, *connect.Request[v2.DeleteAssetRequest]) (*connect.Response[v2.DeleteAssetResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.assets.v2.AssetRegistryService.DeleteAsset is not implemented"))
}

func (UnimplementedAssetRegistryServiceHandler) UpdateAsset(context.Context, *connect.Request[v2.UpdateAssetRequest]) (*connect.Response[v2.UpdateAssetResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.assets.v2.AssetRegistryService.UpdateAsset is not implemented"))
}

func (UnimplementedAssetRegistryServiceHandler) AddAdditionalInfo(context.Context, *connect.Request[v2.AddAdditionalInfoRequest]) (*connect.Response[v2.AddAdditionalInfoResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.assets.v2.AssetRegistryService.AddAdditionalInfo is not implemented"))
}

func (UnimplementedAssetRegistryServiceHandler) GetAssetPrivate(context.Context, *connect.Request[v2.GetAssetPrivateRequest]) (*connect.Response[v2.GetAssetPrivateResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.assets.v2.AssetRegistryService.GetAssetPrivate is not implemented"))
}

func (UnimplementedAssetRegistryServiceHandler) GetZelloChannels(context.Context, *connect.Request[v2.GetZelloChannelsRequest]) (*connect.Response[v2.GetZelloChannelsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.assets.v2.AssetRegistryService.GetZelloChannels is not implemented"))
}

func (UnimplementedAssetRegistryServiceHandler) ListAssetsByPhoneNumber(context.Context, *connect.Request[v2.ListAssetsByPhoneNumberRequest]) (*connect.Response[v2.ListAssetsByPhoneNumberResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.assets.v2.AssetRegistryService.ListAssetsByPhoneNumber is not implemented"))
}

func (UnimplementedAssetRegistryServiceHandler) SearchAssets(context.Context, *connect.Request[v2.SearchAssetsRequest]) (*connect.Response[v2.SearchAssetsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.assets.v2.AssetRegistryService.SearchAssets is not implemented"))
}

func (UnimplementedAssetRegistryServiceHandler) SetAssetInternalStatus(context.Context, *connect.Request[v2.SetAssetInternalStatusRequest]) (*connect.Response[v2.SetAssetInternalStatusResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.assets.v2.AssetRegistryService.SetAssetInternalStatus is not implemented"))
}
