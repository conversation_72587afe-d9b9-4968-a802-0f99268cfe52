syntax = "proto3";

package hero.assets.v2;

option go_package = "proto/hero/assets/v2;assets";

service AssetRegistryService {
    rpc GetAsset(GetAssetRequest) returns (GetAssetResponse) {}
    // Fetch an asset by its Cognito JWT `sub` claim
    rpc GetAssetByCognitoSub(GetAssetByCognitoSubRequest) returns (GetAssetByCognitoSubResponse) {}
    
    // Register a new Asset to the Registry
    rpc CreateAsset(CreateAssetRequest) returns (CreateAssetResponse) {}
    
    rpc CreateResponderAsset(CreateAssetRequest) returns (CreateAssetResponse) {}
    // List all Assets, with their current Orders
    rpc ListAssets(ListAssetsRequest) returns (ListAssetsResponse) {
        // option (hero.auth.v1.access_control) = {
        //     required_roles: []
        // };
    }
    // Delete an Asset 
    rpc DeleteAsset(DeleteAssetRequest) returns (DeleteAssetResponse) {}
    // Update an Asset 
    rpc UpdateAsset(UpdateAssetRequest) returns (UpdateAssetResponse) {} 
    // Add additional info to an asset by merging provided JSON into the existing additional_info_json.
    rpc AddAdditionalInfo(AddAdditionalInfoRequest) returns (AddAdditionalInfoResponse);
    // GetAssetPrivate is a special RPC to fetch an Asset and its private details
    // (e.g., Zello credentials) for use in secure communications
    // It is intended to only be accessed by the Asset itself, or by Admins
    rpc GetAssetPrivate(GetAssetPrivateRequest) returns (GetAssetPrivateResponse) {
        // option (hero.auth.v1.access_control) = {
        //     required_roles: []
        // };
    }
    rpc GetZelloChannels(GetZelloChannelsRequest) returns (GetZelloChannelsResponse) {
        // option (hero.auth.v1.access_control) = {
        //     required_roles: []
        // };
    }
    // Get all assets associated with a phone number
    rpc ListAssetsByPhoneNumber(ListAssetsByPhoneNumberRequest) returns (ListAssetsByPhoneNumberResponse) {}
    
    // Search assets with advanced filtering and text search capabilities
    rpc SearchAssets(SearchAssetsRequest) returns (SearchAssetsResponse) {}
    
    // Set whether an asset is internal or external
    rpc SetAssetInternalStatus(SetAssetInternalStatusRequest) returns (SetAssetInternalStatusResponse) {}
}

message GetZelloChannelsRequest {
}

message GetZelloChannelsResponse {
    repeated ZelloChannel zello_channels = 1;
}

message ZelloChannel {
    string id = 1;
    int32 org_id = 2;
    string zello_channel_id = 3;
    string display_name = 4;
}

message Asset {
    string id = 1;
    int32 org_id = 2;
    string cognito_jwt_sub = 3;
    string name = 4;
    AssetType type = 5;
    AssetStatus status = 6;

    // Last known location
    double latitude = 7;
    double longitude = 8;
    string location_update_time = 9;  // ISO8601 timestamp for last location update

    string contact_no = 10;
    string contact_email = 11;

    string create_time = 12;  // ISO8601 timestamp when created
    string update_time = 13;  // ISO8601 timestamp for last update

    // For asset this will be fixed "ASSET"
    string resource_type = 14;

    // This needs to be a valid JSON string 
    // For cameras, this will contain camera-specific details like:
    // {
    //   "camera": {
    //     "ip_address": "*************",
    //     "rtsp_url": "rtsp://*************/stream",
    //     "credentials": {
    //       "username": "admin",
    //       "password": "encrypted_password",
    //       "use_tls": true,
    //       "certificate_path": "/path/to/cert"
    //     },
    //     "manufacturer": "Hikvision",
    //     "model": "DS-2CD2185FWD-I",
    //     "firmware_version": "V5.5.53",
    //     "capabilities": ["PTZ", "HD", "NightVision"],
    //     "stream_type": "RTSP",
    //     "metadata": {
    //       "resolution": "1080p",
    //       "fps": "30",
    //       "storage": "SD"
    //     }
    //   }
    // }
    string additional_info_json = 16;

    // Last timestamp when status got changed
    string status_changed_time = 17;  // ISO8601 timestamp when status changed

    // Flag to indicate if this is an internal asset
    bool is_internal = 18;
}

enum AssetType {
    ASSET_TYPE_UNSPECIFIED = 0;
    ASSET_TYPE_MEMBER = 1;
    ASSET_TYPE_DISPATCHER = 2;
    ASSET_TYPE_RESPONDER = 3;
    ASSET_TYPE_CAMERA = 4;
    ASSET_TYPE_BOT = 5;
    ASSET_TYPE_SUPERVISOR = 6;
    ASSET_TYPE_TEST = 7;
}

enum AssetStatus {
    ASSET_STATUS_UNSPECIFIED = 0;
    ASSET_STATUS_AVAILABLE = 1;
    ASSET_STATUS_OFFLINE = 2;
    ASSET_STATUS_BUSY = 3;
    ASSET_STATUS_DEACTIVATED = 4;
    ASSET_STATUS_RESERVED = 5;
    ASSET_STATUS_MAINTENANCE = 6;
    ASSET_STATUS_ON_BREAK = 7;
}

// Enum for the order by field in the search assets request.
enum SearchOrderBy {
    SEARCH_ORDER_BY_UNSPECIFIED = 0;
    SEARCH_ORDER_BY_RELEVANCE = 1;
    SEARCH_ORDER_BY_NAME = 2;
    SEARCH_ORDER_BY_CREATE_TIME = 3;
    SEARCH_ORDER_BY_UPDATE_TIME = 4;
    SEARCH_ORDER_BY_STATUS_CHANGED_TIME = 5;
    SEARCH_ORDER_BY_LOCATION_UPDATE_TIME = 6;
}

// Request and Response messages

message GetAssetRequest {
    string id = 1;
}

message GetAssetResponse {
    Asset asset = 1;
}

message GetAssetByCognitoSubRequest {
    string cognito_jwt_sub = 1;
}

message GetAssetByCognitoSubResponse {
    Asset asset = 1;
}

message CreateAssetRequest {
    // The Asset provided here should not include an id, which will be generated
    Asset asset = 1;
}

message CreateAssetResponse {
    Asset asset = 1;
}

// Request message to list assets with pagination, filtering, and ordering options.
message ListAssetsRequest {
  // Maximum number of assets to return in the response.
  int32 page_size = 1;

  // A token identifying a specific page of results to retrieve.
  string page_token = 2;

  // Optional filter: Returns only assets of the specified type.
  AssetType type = 3;

  // Optional filter: Returns only assets matching the specified status.
  AssetStatus status = 4;

  // Optional: Specifies the ordering of returned assets (e.g., "name asc" or "create_time desc").
  string order_by = 5;
}

message ListAssetsResponse {
    repeated Asset assets = 1;
    // Token to retrieve the next page of results, if any
    string next_page_token = 2;
}

message DeleteAssetRequest {
    string id = 1;
}

message DeleteAssetResponse {
}

message UpdateAssetRequest {
    // The asset field should include the id of the asset to update along with new field values
    Asset asset = 1;
}

message UpdateAssetResponse {
    Asset asset = 1;
}

message AddAdditionalInfoRequest {
  // The ID of the asset to update.
  string id = 1;
  // A JSON string containing the additional info to be merged.
  string additional_info_json = 2;
}

message AddAdditionalInfoResponse {
  // The id of the updated asset 
  string id = 1;
  // The updated additional_info_json object.
  string additional_info_json = 2;
}

message ZelloCreds {
    string id = 1;
    int32 org_id = 2;
    string asset_id = 3;
    string username = 4;
    string password = 5;
    string encrypted_password = 6;
}

message GetAssetPrivateRequest {
    string asset_id = 1;
}

message GetAssetPrivateResponse {
    Asset asset = 1;
    ZelloCreds zello_creds = 2;
}

// Request to get all assets associated with a phone number
message ListAssetsByPhoneNumberRequest {
    string phone_number = 1;
}

// Response containing all assets that have the requested phone number
message ListAssetsByPhoneNumberResponse {
    repeated Asset assets = 1;
}

/* ---------------------------------------------------------------------------
   SEARCH MESSAGES
---------------------------------------------------------------------------*/
message DateRange {
    string from = 1;
    string to = 2;
}

message FieldQuery {
    string field = 1;       // The field to search in (id, name, contact_no, contact_email)
    string query = 2;       // The query text for this specific field
}

message HighlightResult {
    string field = 1;       // Field name that had a match
    repeated string fragments = 2; // Highlighted fragments with matched terms
}

message SearchAssetsRequest {
    string query = 1;                       // Free-text / fuzzy query (id, name, contact_no, contact_email)
    repeated string search_fields = 2;      // Limit search scope (leave empty = all)
    repeated FieldQuery field_queries = 3;  // Field-specific query terms
    
    // ─────────────── Fixed-value filters ───────────────
    repeated AssetType type = 4;
    repeated AssetStatus status = 5;
    
    // ─────────────── Date-range filters (inclusive RFC3339) ───────────────
    DateRange create_time = 6;
    DateRange update_time = 7;
    DateRange status_changed_time = 8;
    DateRange location_update_time = 9;
    
    // ─────────────── Geo bounding box ───────────────
    double min_latitude = 10;
    double max_latitude = 11;
    double min_longitude = 12;
    double max_longitude = 13;
    
    // ─────────────── Pagination & sorting ───────────────
    int32 page_size = 14;
    string page_token = 15;
    
    // ─────────────── Enum based ordering ───────────────
    SearchOrderBy order_by = 16;          // defaults to RELEVANCE
    bool ascending = 17;                  // true = ASC, false = DESC
}

message SearchAssetsResponse {
    repeated Asset assets = 1;
    string next_page_token = 2;
    
    // New field for highlight information
    map<string, HighlightResult> highlights = 3; // Key is asset ID
    int32 total_results = 4;
}

message SetAssetInternalStatusRequest {
    string id = 1;
    bool is_internal = 2;
}

message SetAssetInternalStatusResponse {
    Asset asset = 1;
}
