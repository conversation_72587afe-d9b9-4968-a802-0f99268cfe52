package middleware

import (
	clients "common/clients/services"
	cmncontext "common/context"
	"context"
	"encoding/base64"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	orgs "proto/hero/orgs/v1"
	"strconv"
	"strings"

	"connectrpc.com/connect"
)

const (
	bearerPrefix    = "Bearer "
	iamPrefix       = "IAM "
	basicAuthPrefix = "Basic "
	secretPrefix    = "Secret "
)

var secretIDMap = make(map[string]string)
var secretOrgIdMap = make(map[string]string)

var iamIDMap = map[string]string{
	"AIDA34AMDAY6M3GBPZJIC":                   "bot:greengrass-device",
	"AROA34AMDAY6BAM4OJ3VE:AssumeRoleSession": "bot:greengrass-device-test",
	"AROA34AMDAY6CWEA2DQWJ":                   "bot:lambda-camera-listener",
	"AROA34AMDAY6HB5IJ3L45":                   "bot:lambda-post-confirmation",
}
var iamOrgIdMap = map[string]string{
	"AROA34AMDAY6BAM4OJ3VE:AssumeRoleSession": "1",
	"AROA34AMDAY6CWEA2DQWJ":                   "1",
	"AROA34AMDAY6HB5IJ3L45":                   "1",
}

var cognitoJWKSURL string

func init() {
	awsRegion := os.Getenv("AWS_REGION")
	if awsRegion == "" {
		log.Fatal("FATAL: AWS_REGION environment variable not set")
	}

	userPoolID := os.Getenv("COGNITO_USER_POOL_ID")
	if userPoolID == "" {
		log.Fatal("FATAL: COGNITO_USER_POOL_ID environment variable not set")
	}

	cognitoJWKSURL = fmt.Sprintf("https://cognito-idp.%s.amazonaws.com/%s/.well-known/jwks.json", awsRegion, userPoolID)

	// Pre-cache the JWKS on startup.
	// This avoids a lookup on the first request.
	_, err := getJWKS(cognitoJWKSURL)
	if err != nil {
		log.Fatalf("FATAL: Failed to pre-cache Cognito JWKS: %v", err)
	}

	populateBotSecretsFromEnv()
}

func populateBotSecretsFromEnv() {
	botSecretEnvVars := map[string]string{
		"bot:post-confirmation-lambda": "BOT_POST_CONFIRMATION_LAMBDA_SECRET",
		"bot:camera-listener-lambda":   "BOT_CAMERA_LISTENER_LAMBDA_SECRET",
		"bot:pre-signup-lambda":        "BOT_PRE_SIGNUP_LAMBDA_SECRET",
		"bot:basic-auth-lambda":        "BOT_BASIC_AUTH_LAMBDA_SECRET",
	}

	for botUsername, envVarName := range botSecretEnvVars {
		secretValue := os.Getenv(envVarName)

		if secretValue == "" {
			// In local development, these secrets might not be set.
			log.Printf("WARN: secret env var %s not set for bot %s", envVarName, botUsername)
			continue
		}

		secretIDMap[secretValue] = botUsername
		secretOrgIdMap[secretValue] = "-1" // All bot secrets belong to org -1
	}
}

// extractClientIP extracts the real client IP address from the request
// It checks various headers in order of preference for proxy scenarios
func extractClientIP(request *http.Request) string {
	// Check X-Forwarded-For header (most common for load balancers/proxies)
	if xForwardedForHeader := request.Header.Get("X-Forwarded-For"); xForwardedForHeader != "" {
		// X-Forwarded-For can contain multiple IPs, take the first one
		ipAddressList := strings.Split(xForwardedForHeader, ",")
		if len(ipAddressList) > 0 {
			firstIPAddress := strings.TrimSpace(ipAddressList[0])
			if firstIPAddress != "" && firstIPAddress != "unknown" {
				return firstIPAddress
			}
		}
	}

	// Check X-Real-IP header (alternative proxy header)
	if xRealIPHeader := request.Header.Get("X-Real-IP"); xRealIPHeader != "" && xRealIPHeader != "unknown" {
		return xRealIPHeader
	}

	// Check X-Forwarded header (less common)
	if xForwardedHeader := request.Header.Get("X-Forwarded"); xForwardedHeader != "" && xForwardedHeader != "unknown" {
		return xForwardedHeader
	}

	// Check Forwarded header (RFC 7239 standard)
	if forwardedHeader := request.Header.Get("Forwarded"); forwardedHeader != "" {
		// Parse "for=" parameter from Forwarded header
		forwardedParts := strings.Split(forwardedHeader, ";")
		for _, forwardedPart := range forwardedParts {
			trimmedPart := strings.TrimSpace(forwardedPart)
			if strings.HasPrefix(trimmedPart, "for=") {
				extractedIPAddress := strings.TrimPrefix(trimmedPart, "for=")
				// Remove quotes if present
				extractedIPAddress = strings.Trim(extractedIPAddress, "\"")
				// Handle IPv6 brackets
				if strings.HasPrefix(extractedIPAddress, "[") && strings.HasSuffix(extractedIPAddress, "]") {
					extractedIPAddress = extractedIPAddress[1 : len(extractedIPAddress)-1]
				}
				if extractedIPAddress != "" && extractedIPAddress != "unknown" {
					return extractedIPAddress
				}
			}
		}
	}

	// Fall back to RemoteAddr (direct connection)
	if request.RemoteAddr != "" {
		// RemoteAddr includes port, extract just the IP
		hostIPAddress, _, err := net.SplitHostPort(request.RemoteAddr)
		if err != nil {
			// If SplitHostPort fails, RemoteAddr might be just an IP
			return request.RemoteAddr
		}
		return hostIPAddress
	}

	return ""
}

// AuthMiddleware enforces authentication for all RPCs
// It looks for AccessControl.ProtectionLevel to determine the required protection level for the RPC
func AuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx, authHeader, done := getAuthHeader(r, w)
		if done {
			return
		}

		// Extract client IP address
		clientIP := extractClientIP(r)

		username := ""
		organizationIds := []string{}
		// Cognito auth - Bearer token is present
		// This is how users authenticate
		if strings.HasPrefix(authHeader, bearerPrefix) {
			uname, orgIds, err := getCognitoID(authHeader)
			if err != nil {
				http.Error(w, err.Error(), http.StatusUnauthorized)
				return
			}
			username = uname
			organizationIds = orgIds
		}

		// IAM auth - IAM role/user id is present
		// This is how Lambdas and Edge (greengrass) devices authenticate
		if strings.HasPrefix(authHeader, iamPrefix) {
			uname, orgIds, err := getIAMID(authHeader)
			if err != nil {
				http.Error(w, err.Error(), http.StatusUnauthorized)
				return
			}
			username = uname
			organizationIds = orgIds
		}

		// Basic auth - Basic auth is present
		// This is how some external services authenticate (e.g. Twilio)
		if strings.HasPrefix(authHeader, basicAuthPrefix) {
			orgId := r.Header.Get("X-Org-Id")
			uname := r.Header.Get("X-Username")
			if orgId == "" || uname == "" {
				if os.Getenv("ENV") == "local" {
					orgIdInner, unameInner, err := verifyBasicAuth(authHeader)
					if err != nil {
						http.Error(w, err.Error(), http.StatusUnauthorized)
						return
					}
					orgId = orgIdInner
					uname = unameInner
					r.Header.Set("X-Org-Id", orgIdInner)
					r.Header.Set("X-Username", unameInner)
				} else {
					http.Error(w, "Basic auth header is present, but orgId or uname is not present", http.StatusUnauthorized)
					return
				}
			}
			// Basic auth returns the actual username from the organization's API user
			// When retrieved via cmncontext.GetUsername(), it will return the plain username
			username = uname
			organizationIds = []string{orgId}
		}

		// Secret is present
		// This is how our bot services authenticate
		if strings.HasPrefix(authHeader, secretPrefix) {
			uname, orgIds, err := getSecretID(authHeader)
			if err != nil {
				http.Error(w, err.Error(), http.StatusUnauthorized)
				return
			}
			username = uname
			organizationIds = orgIds
		}

		// Store username and roles in the request context
		ctx = context.WithValue(ctx, cmncontext.UsernameContextKey, username)
		// Store IP address in the request context
		ctx = context.WithValue(ctx, cmncontext.IPAddressContextKey, clientIP)
		// more than one org id is only allowed with a guest org
		organizationId := ""
		if len(organizationIds) > 1 {
			guestOrg := ""
			for _, orgId := range organizationIds {
				// if org id ends with ":guest", then it is a guest org
				if strings.HasSuffix(orgId, ":guest") {
					if guestOrg != "" {
						http.Error(w, "Multiple guest organization IDs found", http.StatusBadRequest)
						return
					}
					guestOrg = orgId
				}
			}
			// if no guest org is found, then throw an error
			if guestOrg == "" {
				http.Error(w, "Multiple non-guest organization IDs found", http.StatusBadRequest)
				return
			}
			// use the guest org, but remove the ":guest" suffix
			organizationId = guestOrg[:len(guestOrg)-len(":guest")]
		} else if len(organizationIds) == 0 {
			http.Error(w, "No organization IDs found", http.StatusBadRequest)
			return
		} else {
			organizationId = organizationIds[0]
		}

		// Convert organization ID to int32
		orgIdInt, err := strconv.ParseInt(organizationId, 10, 32)
		if err != nil {
			http.Error(w, "Invalid organization ID", http.StatusBadRequest)
			return
		}
		// Convert organization IDs to int32
		orgIdsInt := make([]int32, len(organizationIds))
		for i, orgId := range organizationIds {
			orgIdInt, err := strconv.ParseInt(orgId, 10, 32)
			if err != nil {
				http.Error(w, "Invalid organization ID", http.StatusBadRequest)
				return
			}
			orgIdsInt[i] = int32(orgIdInt)
		}
		ctx = context.WithValue(ctx, cmncontext.OrgIdContextKey, int32(orgIdInt))
		ctx = context.WithValue(ctx, cmncontext.OrgIdsContextKey, orgIdsInt)

		// Create a new request with the updated context
		r = r.WithContext(ctx)

		next.ServeHTTP(w, r)
	})
}

// Creates a single Authorization header from multiple headers, to support multiple auth methods seamlessly
func getAuthHeader(r *http.Request, w http.ResponseWriter) (context.Context, string, bool) {
	ctx := r.Context()
	// Standard header for Cognito auth and Basic auth
	authHeader1 := r.Header.Get("Authorization")
	// Custom header for IAM auth
	authHeader2 := r.Header.Get("Authorization2")
	// Custom header for secret key
	authHeader4 := r.Header.Get("Authorization4")

	if authHeader1 == "" && authHeader2 == "" && authHeader4 == "" {
		w.Header().Set("WWW-Authenticate", `Basic realm="Default"`)
		http.Error(w, "Authorization header is required", http.StatusUnauthorized)
		return ctx, "", true
	}
	if authHeader1 != "" && authHeader2 != "" && authHeader4 != "" {
		http.Error(w, "Multiple authorization headers are not allowed", http.StatusBadRequest)
		return ctx, "", true
	}

	var authHeader string
	var originalAuthHeader cmncontext.OriginalAuthHeaderType
	if authHeader1 != "" {
		authHeader = authHeader1
		originalAuthHeader = cmncontext.OriginalAuthHeaderType{
			AuthHeaderKey:   "Authorization",
			AuthHeaderValue: authHeader1,
		}
	} else if authHeader2 != "" {
		authHeader = iamPrefix + authHeader2
		originalAuthHeader = cmncontext.OriginalAuthHeaderType{
			AuthHeaderKey:   "Authorization2",
			AuthHeaderValue: authHeader2,
		}
	} else if authHeader4 != "" {
		authHeader = secretPrefix + authHeader4
		originalAuthHeader = cmncontext.OriginalAuthHeaderType{
			AuthHeaderKey:   "Authorization4",
			AuthHeaderValue: authHeader4,
		}
	}

	// Store original headers in the context, so that they can be forwarded as needed
	ctx = context.WithValue(ctx, cmncontext.OriginalAuthHeaderContextKey, originalAuthHeader)
	return ctx, authHeader, false
}

func getSecretID(authHeader string) (string, []string, error) {
	if !strings.HasPrefix(authHeader, secretPrefix) {
		return "", nil, http.ErrNoCookie
	}

	secretKeyString := authHeader[len(secretPrefix):]

	var username string
	var organizationIds []string
	if uname, ok := secretIDMap[secretKeyString]; ok {
		// Secret-based authentication returns username in "bot:<service_name>" format
		// When retrieved via cmncontext.GetUsername(), it will return in this "bot:<service_name>" format
		username = uname
	}
	if orgId, ok := secretOrgIdMap[secretKeyString]; ok {
		organizationIds = append(organizationIds, orgId)
	}
	return username, organizationIds, nil
}

// Extracts IAM roles from Authorization header
func getIAMID(authHeader string) (string, []string, error) {
	if !strings.HasPrefix(authHeader, iamPrefix) {
		return "", nil, http.ErrNoCookie
	}

	IAMRoleString := authHeader[len(iamPrefix):]

	var username string
	var organizationIds []string
	if uname, ok := iamIDMap[IAMRoleString]; ok {
		// IAM-based authentication returns username in "bot:<service_name>" format
		// When retrieved via cmncontext.GetUsername(), it will return in this "bot:<service_name>" format
		username = uname
	}
	if orgId, ok := iamOrgIdMap[IAMRoleString]; ok {
		organizationIds = append(organizationIds, orgId)
	}
	return username, organizationIds, nil
}

// Extracts user roles and username from JWT token
func getCognitoID(authHeader string) (string, []string, error) {
	// Check if Authorization header is not a Bearer token
	if !strings.HasPrefix(authHeader, bearerPrefix) {
		return "", nil, http.ErrNoCookie
	}

	jwtToken := authHeader[len(bearerPrefix):]

	orgIds := []string{}

	// The JWKS for token validation is pre-cached at startup.
	// validate JWT token
	token, err := ValidateJWT(jwtToken, cognitoJWKSURL)
	if err != nil {
		return "", nil, err
	}

	groups, ok := token["cognito:groups"].([]interface{})
	if !ok {
		return "", nil, http.ErrNoCookie
	}
	for _, group := range groups {
		if groupStr, ok := group.(string); ok {
			if strings.HasPrefix(groupStr, "org:") {
				// Strip out the "org:" prefix before adding to orgIds
				orgId := strings.TrimPrefix(groupStr, "org:")
				orgIds = append(orgIds, orgId)
			}
		}
	}

	username, ok := token["sub"].(string)
	if !ok {
		return "", nil, http.ErrNoCookie
	}

	// Format username as "cognito:<cognito_user_id>" for consistent identification
	// This format is used throughout the system to identify Cognito-authenticated users
	// When retrieved via cmncontext.GetUsername(), it will return in this "cognito:<id>" format
	username = "cognito:" + username

	return username, orgIds, nil
}

func verifyBasicAuth(authHeader string) (string, string, error) {
	if !strings.HasPrefix(authHeader, basicAuthPrefix) {
		return "", "", http.ErrNoCookie
	}

	// Extract and decode the credentials
	base64Credentials := strings.TrimPrefix(authHeader, "Basic ")
	credentials, err := base64.StdEncoding.DecodeString(base64Credentials)
	if err != nil {
		return "", "", fmt.Errorf("error decoding credentials: %v", err)
	}

	parts := strings.SplitN(string(credentials), ":", 2)
	if len(parts) != 2 {
		return "", "", fmt.Errorf("invalid credentials format")
	}

	orgsClient := clients.NewOrgsClient(os.Getenv("ORGS_SERVICE_URL"),
		clients.AuthHeaderSecretKeyInterceptor("OHBBOJHBLHJBLHJVLHJVLJTESTEVLJVL"))

	resp, err := orgsClient.ValidateOrgCreds(context.Background(), &connect.Request[orgs.ValidateOrgCredsRequest]{
		Msg: &orgs.ValidateOrgCredsRequest{
			Username: parts[0],
			Password: parts[1],
		},
	})
	if err != nil {
		return "", "", fmt.Errorf("error validating org credentials: %v", err)
	}

	if resp.Msg.Valid {
		orgID := resp.Msg.OrgApiUser.OrgId
		username := resp.Msg.OrgApiUser.Username
		return strconv.Itoa(int(orgID)), username, nil
	}
	return "", "", fmt.Errorf("invalid org credentials")
}
