package middleware

import (
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"math/big"
	"net/http"
	"sync"

	"github.com/golang-jwt/jwt/v4"
)

// J<PERSON><PERSON> represents the JSON Web Key Set
type JWKS struct {
	Keys []JWK `json:"keys"`
}

// JW<PERSON> represents a JSON Web Key
type JWK struct {
	Kid string `json:"kid"`
	Kty string `json:"kty"`
	Alg string `json:"alg"`
	N   string `json:"n"`
	E   string `json:"e"`
}

var (
	jwksCache = make(map[string]*JWKS)
	jwksMutex = &sync.RWMutex{}
)

// getJWKS fetches JW<PERSON> from a given URL, using an in-memory cache.
func getJWKS(jwksURL string) (*JWKS, error) {
	jwksMutex.RLock()
	cachedJwks, ok := jwksCache[jwksURL]
	jwksMutex.RUnlock()

	if ok {
		return cachedJwks, nil
	}

	resp, err := http.Get(jwksURL)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var jwks JWKS
	if err := json.NewDecoder(resp.Body).Decode(&jwks); err != nil {
		return nil, err
	}

	jwksMutex.Lock()
	jwksCache[jwksURL] = &jwks
	jwksMutex.Unlock()

	return &jwks, nil
}

// findKey finds the matching JWK for a given kid
func findKey(jwks *JWKS, kid string) (*JWK, error) {
	for _, key := range jwks.Keys {
		if key.Kid == kid {
			return &key, nil
		}
	}
	return nil, errors.New("matching key not found")
}

// convertJWKToPublicKey converts a JWK to an RSA public key
func convertJWKToPublicKey(jwk *JWK) (*rsa.PublicKey, error) {
	nBytes, err := base64.RawURLEncoding.DecodeString(jwk.N)
	if err != nil {
		return nil, fmt.Errorf("error converting JWK: %v", err)
	}

	eBytes, err := base64.RawURLEncoding.DecodeString(jwk.E)
	if err != nil {
		return nil, fmt.Errorf("error converting JWK: %v", err)
	}

	e := 0
	for _, b := range eBytes {
		e = e<<8 + int(b)
	}

	pubKey := &rsa.PublicKey{
		N: new(big.Int).SetBytes(nBytes),
		E: e,
	}

	return pubKey, nil
}

// ValidateJWT validates the JWT token and returns the claims if valid
func ValidateJWT(jwtToken string, jwksURL string) (jwt.MapClaims, error) {
	// Fetch JWKS from the endpoint
	jwks, err := getJWKS(jwksURL)
	if err != nil {
		return nil, fmt.Errorf("error fetching JWKS: %v", err)
	}

	// Parse the JWT
	token, err := jwt.Parse(jwtToken, func(token *jwt.Token) (interface{}, error) {
		// Ensure the token is using RS256
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		// Extract key ID (kid) from token header
		kid, ok := token.Header["kid"].(string)
		if !ok {
			return nil, errors.New("kid not found in token header")
		}

		// Find the matching JWK key
		jwk, err := findKey(jwks, kid)
		if err != nil {
			return nil, err
		}

		// Convert JWK to RSA Public Key
		return convertJWKToPublicKey(jwk)
	})

	// Check validation result
	if err != nil {
		return nil, fmt.Errorf("error verifying token: %v", err)
	} else if token.Valid {
		if claims, ok := token.Claims.(jwt.MapClaims); ok {
			return claims, nil
		}
		return nil, errors.New("invalid token claims")
	} else {
		return nil, errors.New("token is invalid")
	}
}
