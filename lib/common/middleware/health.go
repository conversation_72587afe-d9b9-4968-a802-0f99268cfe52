package middleware

import (
	"net/http"
	"strings"
	"time"

	"connectrpc.com/grpchealth"
	"connectrpc.com/grpcreflect"
	"github.com/rs/cors"
	"golang.org/x/net/http2"
	"golang.org/x/net/http2/h2c"

	"common/middleware/permissions"
)

// HealthMuxConfig represents the configuration for health check and reflection endpoints
type HealthMuxConfig struct {
	// ServiceNames is a list of service names to check health for
	ServiceNames []string
	// HealthResponse is the response body for the /health endpoint
	HealthResponse string
}

// NewHealthMux creates a new mux for health check and reflection endpoints
func NewHealthMux(config HealthMuxConfig) *http.ServeMux {
	healthMux := http.NewServeMux()

	// Add health check endpoint
	healthMux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		if _, err := w.Write([]byte(config.HealthResponse)); err != nil {
			// Log error but don't fail the request
		}
	})

	// Add gRPC health check endpoint
	checker := grpchealth.NewStaticChecker(config.ServiceNames...)
	healthMux.Handle(grpchealth.NewHandler(checker))

	// Add gRPC reflection endpoint
	reflector := grpcreflect.NewStaticReflector(append(config.ServiceNames, "grpc.health.v1.Health")...)
	healthMux.Handle(grpcreflect.NewHandlerV1(reflector))
	healthMux.Handle(grpcreflect.NewHandlerV1Alpha(reflector))
	return healthMux
}

// isHealthEndpoint checks if the given path is a health or reflection endpoint
func isHealthEndpoint(path string) bool {
	return path == "/health" ||
		strings.HasPrefix(path, "/grpc.health") ||
		strings.HasPrefix(path, "/grpc.reflection")
}

// NewServerWithHealth creates a new HTTP server that handles both authenticated and health endpoints
func NewServerWithHealth(addr string, mainHandler http.Handler, healthMux *http.ServeMux, permissionsMiddleware bool) *http.Server {
	// Apply CORS to the main handler
	// This only matters for local development
	// In production, this is handled by the API Gateway
	corsHandler := cors.New(cors.Options{
		AllowedOrigins:   []string{"http://localhost:3000"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
		MaxAge:           300,
	}).Handler(mainHandler)

	var handler http.Handler

	// Apply permissions middleware
	if permissionsMiddleware {
		handler = permissions.PermissionsMiddleware(corsHandler)
	} else {
		handler = corsHandler
	}
	// Apply auth middleware
	handler = AuthMiddleware(handler)

	// Create the final handler that routes between health and main handlers
	finalHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if isHealthEndpoint(r.URL.Path) || r.Method == "OPTIONS" {
			// Allow health endpoints and OPTIONS requests to bypass auth
			if r.Method == "OPTIONS" {
				corsHandler.ServeHTTP(w, r)
			} else {
				healthMux.ServeHTTP(w, r)
			}
		} else {
			handler.ServeHTTP(w, r)
		}
	})

	return &http.Server{
		Addr:         addr,
		Handler:      h2c.NewHandler(finalHandler, &http2.Server{}),
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}
}
